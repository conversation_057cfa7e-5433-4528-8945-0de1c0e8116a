/* @ts-check */
/**
 * Đ<PERSON> sử dụng cho vscode thì phải sửa .vscode/settings.json, bổ sung các dòng sau:
 * "eslint.enable": true, // Set false để tắt eslint
 * "eslint.experimental.useFlatConfig": true, // Set true để sử dụng eslint dạng flat
 */


import globals from "globals"
import eslint from "@eslint/js"
import tseslint from "typescript-eslint"
// https://eslint.vuejs.org/rules/
import pluginVue from "eslint-plugin-vue"
// https://eslint-plugin-vue-i18n.intlify.dev/started.html
import vueI18n from "@intlify/eslint-plugin-vue-i18n"

export default tseslint.config({
  languageOptions: {
    globals: globals.browser,
    parserOptions: {
      parser: tseslint.parser,
      extraFileExtensions: [".vue"],
      sourceType: "module",
    },
  },
  ignores: [
    // Thêm các mẫu để bỏ qua các thư mục không liên quan ở đây
    "node_modules/**/*",
    "dist/**/*",
  ],
  extends: [
    eslint.configs.recommended,
    ...tseslint.configs.recommended,
    ...pluginVue.configs["flat/recommended"],
    ...vueI18n.configs["flat/recommended"],
  ],
  settings: {
    // https://eslint-plugin-vue-i18n.intlify.dev/started#settings-vue-i18n
    "vue-i18n": {
      localeDir: [
        {
          pattern: "./src/locales/*.{json,json5,yaml,yml}",
          localeKey: "file",
        },
        {
          // 2024/06/19 not work with .ts file :()
          pattern: "./src/locales/errorcodes/*.{json,json5,yaml,yml,ts}",
          localeKey: "file",
        },
      ], // extension is glob formatting!
      // Specify the version of `vue-i18n` you are using.
      // If not specified, the message will be parsed twice.
      messageSyntaxVersion: "^9.0.0",
    },
  },
  rules: {
    "no-console": "warn",
    "no-unused-vars": "off",
    "no-undef": "off",
    "@typescript-eslint/no-unused-vars": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/ban-types": "off",
    // https://eslint.vuejs.org/rules/
    "vue/multi-word-component-names": "off",
    "vue/html-self-closing": "off",
    "vue/singleline-html-element-content-newline": "off",
    "vue/multiline-html-element-content-newline": [
      "warn",
      { ignoreWhenEmpty: true, allowEmptyLines: true, ignores: ["pre", "textarea"] },
    ],
    // Fix các đoạn xung đột với prettier
    "vue/max-attributes-per-line": "off",
    // https://eslint-plugin-vue-i18n.intlify.dev/rules/#recommended
    "@intlify/vue-i18n/no-html-messages": "warn",
    "@intlify/vue-i18n/no-raw-text": [
      "warn",
      {
        ignorePattern: "^[-#:()&|]+$",
        ignoreText: ["EUR", "HKD", "USD", "VND"],
      },
    ],
  },
})
