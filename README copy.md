# PaymentLink.MerchantWeb

## Danh sách các đầu việc
- Hỗ trợ ts (DONE)
- Xử lý appsetting và .env (DONE)
- Format coding -> .prettierrc (DONE: Cần xem thêm config để format hợp lý hơn)
- Làm layout+page (DONE)
- Xử lý custom index.html với data động (DONE)
- Xử lý eslint
- Làm logger
- Làm store
- Làm authentication
- Tìm cách để xử lý verify các link 


đã support typescript => nên dùng .ts để được hỗ trợ. chưa check xem .js thì xử lý sao


# Nghiệp vụ

## Phân biệt các loại amount

1. **Total Amount (Tổng tiền)**
- Đ<PERSON>y là **tổng giá trị đơn hàng** trước khi thanh toán
- Tương tự như Order amount ở dưới
- Không theo định nghĩa của từ điển
- <PERSON><PERSON> số tiền của sản phẩm sau khi cộng, trừ các loại chiết khấu, thuế, phí
- <PERSON>à số tiền trước khi chuển đổi ngoại tệ (nếu có)

2. **Order Amount (Số tiền đặt hàng)**
- Đây là **tổng giá trị đơn hàng** trước khi thanh toán
- Bao gồm:
  - Giá trị của sản phẩm/dịch vụ trong đơn hàng.
  - Có thể bao gồm thuế, phí vận chuyển, hoặc các khoản phí khác (tùy chính sách).
  - **Theo định nghĩa của từ điển**:
    - Nó **KHÔNG** bao gồm các khoản giảm giá hoặc chiết khấu trừ vào đơn hàng (nếu có).
  - **Theo định nghĩa của Onepay**: 
    - **ĐÃ** bao gồm luôn giảm giá, chiết khấu (nếu có).
    - **CHƯA** bao gồm chuyển đổi ngoại tệ (nếu có).
- Hiển thị: 
  - Ở màn Link Detail: Payment amount
  - Ở màn Preview+Result: Order amount
- **Tóm lại**: Nó là số tiền KH cần phải trả trước khi sang cổng (Đã được chuyển đổi ngoại tệ).
- **Chú ý**: OP đang dùng lẫn từ này thành **Payment amount** ở màn **Link Detail**.

3. **Payment Amount (Số tiền thanh toán)**
- Đây là **số tiền thực tế mà khách hàng thanh toán** cho đơn hàng.
- Tính sau khi áp dụng:
  - Chiết khấu, mã giảm giá, hoặc voucher.
  - Phí bổ sung (nếu có).
- **Tóm lại**: Nó là số tiền thực tế KH đã trả sau qua cổng -> trở về transaction detail.

# Các chú ý

## Chú ý khi làm việc với jsnumber
Hiện tại công thức đang phải sử dụng tính lại ở 5 vị trí. Tuy nhiên việc dùng js dẫn tới tính toán sai kết quả cuối.

https://flaviocopes.com/javascript-decimal-arithmetics/

js Tính:
9.02*24705 = 222839.09999999998
9.19*24705 = 227038.94999999998

Hiện tại nhờ hàm làm tròn nên may mắn chính xác. khi debug sẽ thấy rõ giá trị bị lệch

Nên đổi sang dùng decimalJs cho tính toán các phần thập phân

### Các lib tham khảo
https://github.com/MikeMcl/big.js
https://github.com/MikeMcl/bignumber.js/
https://github.com/MikeMcl/decimal.js

## Tham khảo

Xử lý layout:
https://blog.izem.dev/a-vue-3-users-guide-to-creating-complex-layouts#heading-attach-layouts-to-your-routes
Tạm dừng. Để tối ưu thêm thì đọc và chỉnh để dùng function () => import('...') https://medium.com/@sakensaten1409/vue-3-layout-system-smart-layouts-for-vuejs-80ae700e48a6

Xử lý log
Đọc thêm 
- https://www.npmjs.com/package/loglevel-plugin-remote
- https://medium.com/@nirt04/how-did-i-manage-to-log-my-entire-vue-application-along-with-vuex-axios-627012b8957b
- https://dev.to/programequity/how-to-implement-a-full-stack-logging-solution-for-a-vuejs-and-nodejs-app-5bj3
- https://vue-i18n.intlify.dev/api/general.html#globalinjection
- https://eslint-plugin-vue-i18n.intlify.dev/started.html


## Nếu cài SPA trên IIS
```web.config
<configuration>
    <system.webServer>
    <rewrite>
      <rules>
        <rule name="backend" stopProcessing="true">
          <match url="^(backend)(.*)$" />
          <action type="None" />
        </rule>
        <rule name="Vuejs rules" stopProcessing="true">
          <match url="(.*)" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="/" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>
```

### Format
- https://editorconfig.org/
- https://prettier.io/docs/en/configuration
- https://stackoverflow.com/questions/69379869/how-to-set-json-with-comment-in-vscode
- https://github.com/dustinspecker/awesome-eslint

## Error handling
- https://enterprisevue.dev/blog/error-handling-in-vue-3/
- https://medium.com/js-dojo/error-exception-handling-in-vue-js-application-6c26eeb6b3e4
- https://www.c-sharpcorner.com/article/exception-handling-in-vue-js-application/
- https://www.reddit.com/r/vuejs/comments/12n2hpr/looking_for_help_with_error_handling_in_vue_app/

## helper
https://medium.com/js-dojo/registering-globally-your-helpers-in-a-vue-app-b7ba3b9596b3
https://tomaszs2.medium.com/how-to-install-vue-i18n-on-vue-2-7-7-8ca5fd66e5fd

## boostrap
https://medium.com/devops-techable/how-to-work-with-bootstrap5-and-scss-inside-vue3-vuejs-8d9d8cfd7f4
https://ynldev.medium.com/how-to-customize-bootstrap-with-sass-in-vue-3-77688f573ffd

## api
https://stackoverflow.com/questions/74460129/vue-3-pinia-how-to-handle-error-loading-and-data-properly **
https://stackoverflow.com/questions/76964046/using-onmounted-and-axios-requests-in-pinia
https://medium.com/@bugintheconsole/axios-vue-js-3-pinia-a-comfy-configuration-you-can-consider-for-an-api-rest-a6005c356dcd

   ---------------          --------------------          ---------------
   |     View    |          | State management |          |     API     |
   | ----------- | <----->  | ---------------- | <----->  | ----------- |
   | .vue        |          |   Pinia / Vuex   |          | axios/fetch |
   | composables |          |                  |          |             |
   |_____________|          |__________________|          |_____________|
   
   
   way1: Sử dụng Task (vue-concurrency) ở View để quản lý trạng thái loading (nhiều thành phần load cùng lúc lúc init page)
   way2: Sử dụng Task ở State để quản lý trạng thái state (bao gồm cả loading), trả về cả task -> View vẫn có thể perform, cancelAll
   way3: Sử dụng Task cả ở View Và State -> View để quản lý main task và State có các child tasks (https://vue-concurrency.netlify.app/cancellation/#cancelation-cascade)

## Vấn đề cors với vite dev server
https://rubenr.dev/cors-vite-vue/
Mô tả:
- Trường hợp trong môi trường DEV browser localhost -> call api từ http://**************:8888/WeatherForecast (header chửa origin localhost) -> server trả về browser response
Nếu response ko chứa Access-Control-Allow-Origin: localhost  hoặc  * thì browser sẽ chặn xử lý do lêch origin với host
-> ĐỂ PASS ĐƯỢC Trường hợp này thì cần qua proxy của vite dev server:
Browser localhost -> call api localhost/api/WeatherForecast -> vite dev server proxy xử lý call sang WeatherForecast -> WeatherForecast trả về Vite dev server -> vite dev server trả về browser với cùng origin -> browser xử lý tiếp thành công do cùng origin với host.
- Trường hợp trong môi trường PROP thì sẽ dùng nginx hoặc iis nên ko dùng vite dev server proxy được. -> tự chủ động set cors phía api hoặc control proxy phía fronent server

## Các vấn đề còn tồn:
1. router webpackChunkName not work
2. router page not have loading when use layout by inner -> need change to nest router

## Với number format
https://vue-number-format.netlify.app/guide/play-ground.html (https://github.com/coders-tm/vue-number-format)
Hoạt động cũng gặp lỗi xóa hết phần nguyên thì ko trả ra .01 mà ra thành 0.01

# Links hỗ trợ
https://unpkg.com/browse/primevue@3.51.0/

https://vuejs.org/guide/typescript/composition-api.html
https://vuejs.org/api/utility-types#extractpublicproptypes
https://vuejs.org/api/sfc-script-setup.html#generics

https://primevue.org/setup/#component
https://vueuse.org/functions.html
https://vue-concurrency.netlify.app/performing-tasks/