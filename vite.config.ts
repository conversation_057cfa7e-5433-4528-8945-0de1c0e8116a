import { execSync } from "node:child_process"
import fs from "node:fs"
import { fileURLToPath, URL } from "node:url"

import vue from "@vitejs/plugin-vue"
import moment from "moment"
import path from "path"
import pc from "picocolors"
import { visualizer } from "rollup-plugin-visualizer"
import slugid from "slugid"
import { PrimeVueResolver } from "unplugin-vue-components/resolvers"
import Components from "unplugin-vue-components/vite"
import { createLogger, defineConfig, loadEnv, PluginOption, ProxyOptions, ResolvedConfig } from "vite"
import { createHtmlPlugin } from "vite-plugin-html" /* https://github.com/vbenjs/vite-plugin-html */
import removeConsole from "vite-plugin-remove-console"
import VueDevTools from "vite-plugin-vue-devtools"
//import tsconfigPaths from 'vite-tsconfig-paths' // Nếu dùng thì phải dùng tsconfigPaths({loose: true}) vì không phải component nào cũng set lang="ts" -> hiểu thành js
import { runtimeEnv } from "vite-plugin-runtime" // Plugin config env at runtime

// Icon loader
//import svgLoader from "vite-svg-loader"
import { viteAwesomeSvgLoader } from "vite-awesome-svg-loader"
//import UnpluginSvgComponent from "unplugin-svg-component/vite"

const logger = createLogger()

interface ViteConfigData {
  title?: string
  mode: string
  isDev: boolean
  isProd: boolean
  isBuild: boolean
  version: string
  baseUrl: string
  buildDate: string
  useRemoveConsolePlugin: boolean
  useRuntimeEnv: boolean
  runtimeEnvPath: () => string
}

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  logger.info(`command: ${pc.blue(command)}-${pc.green(mode)}`)

  // Load env file based on `mode` in the current working directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.

  //const env = { ...process.env, ...loadEnv(mode, process.cwd(), '') }
  const env = loadEnv(mode, process.cwd(), "")
  const srcPath = path.resolve(__dirname, "./src")

  // NOTE: Chú ý sử dung NODE_ENV và MODE khác nhau. Tham khảo: https://vitejs.dev/guide/env-and-mode#node-env-and-modes
  const options: ViteConfigData = {
    mode,
    isDev: mode === "development",
    isProd: mode === "production",
    isBuild: command === "build",
    title: env.VITE_APP_TITLE, // Declare in .env file
    baseUrl: env.VITE_APP_BASE_URL, // Declare in .env file
    version: getVersion(), // TODO: should get from file version.txt
    //buildDate: new Date().toISOString(), // TODO: iso của js Date ko có timezone -> nên dùng thư viện
    buildDate: moment().toISOString(true),
    useRemoveConsolePlugin: false,
    useRuntimeEnv: true,
    runtimeEnvPath: () => (options.isDev ? "" : path.join(options.baseUrl, "env.js").replace(/\\/g, "/")), // replaceAll \ to /
  }

  logger.info("-----------------")
  logger.info(`root: ${pc.green(process.cwd())}`)
  logger.info(`mode: ${pc.green(mode)}`)
  logger.info(`isDev: ${pc.green(String(options.isDev))}`)
  logger.info(`version: ${pc.blue(options.version)}`)
  logger.info(`baseUrl: ${pc.green(options.baseUrl)}`)
  logger.info(`useRuntimeEnv: ${pc.green(String(options.useRuntimeEnv ? options.runtimeEnvPath() : false))}`)
  logger.info(`buildDate: ${pc.green(options.buildDate)}`)
  logger.info("-----------------")

  return {
    // WARN khi dùng base: cần đánh giá dùng chỗ này với head>base trong index.html đang dùng vite-plugin-html để config.
    // Info: base config chỉ làm việc với các tài nguyên assets urls bên trong js, css, html.
    // Nó ko xử lý các tài nguyên public urls
    // -> nên dùng head>base hơn vì nó work cả với css urls img
    // Update 20240727: base tag doesnot work for relative paths (like: /assets/index-xxx.js) -> must set vite config base
    base: options.baseUrl,
    plugins: [
      vue(),
      VueDevTools(),
      useRollupVisualizer(),
      useViteAwesomeSvgLoader(),
      useUnpluginVueComponents(),
      useVitePluginHtml(options),
      useRemoveConsole(options),
      ...useRuntimeEnv(options),
      useGenerateVersion(options),
    ],
    resolve: {
      alias: {
        // WARN: Khi khai báo ở đây thì nhớ khai báo thêm ở tsconfig.json (hoặc tsconfig.app.json)
        // Nếu ko thì cần dùng plugin vite-tsconfig-paths để khai báo ở mỗi tsconfig.json.
        "@": fileURLToPath(new URL("./src", import.meta.url)),
        "@assets": fileURLToPath(new URL("./src/assets", import.meta.url)),
        "@components": fileURLToPath(new URL("./src/components", import.meta.url)),
        "@data": fileURLToPath(new URL("./src/data", import.meta.url)),
        "@enums": fileURLToPath(new URL("./src/enums", import.meta.url)),
        "@layouts": fileURLToPath(new URL("./src/layouts", import.meta.url)),
        "@locales": fileURLToPath(new URL("./src/locales", import.meta.url)),
        "@pages": fileURLToPath(new URL("./src/pages", import.meta.url)),
        "@plugins": fileURLToPath(new URL("./src/plugins", import.meta.url)),
        "@routers": fileURLToPath(new URL("./src/routers", import.meta.url)),
        "@services": fileURLToPath(new URL("./src/services", import.meta.url)),
        "@stores": fileURLToPath(new URL("./src/stores", import.meta.url)),
        "@types": fileURLToPath(new URL("./src/types", import.meta.url)),
        "@utils": fileURLToPath(new URL("./src/utils", import.meta.url)),
        "@views": fileURLToPath(new URL("./src/views", import.meta.url)),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/assets/scss/_variables.scss" as global;`,
        },
      },
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            libs: ["xss", "axios", "moment", "jQuery"],
            libs_lodash: ["lodash-es"],
            vue: ["vue", "@vueuse/core", "vue-router", "vue-i18n"],
            theme1: ["bootstrap"],
            theme2: [
              "primevue/config",
              "primevue/confirmationservice",
              "primevue/toastservice",
              "primevue/toast",
              "primevue/tooltip",
              "primevue/datatable",
            ],
          },
        },
      },
    },
    server: {
      port: 5173,
      strictPort: false, // False: if port is already in use, instead of automatically trying the next available port.
      cors: {
        origin: false, // False: True way to disable cors in vite dev server (but it may not be necessary)
      },
      proxy: {
        "/WeatherForecast_SvrProxy": {
          target: "http://**************:8888",
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path.replace(/^\/WeatherForecast_SvrProxy/, ""),
        },
        "/Dev66_63_ApiProxy": {
          //target: "https://*************:4569/api",
          //target: "http://127.0.0.1:4567/api",
          target: "http://127.0.0.1:9082/ota/api",
          headers: {
            cookie:
              "auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzQyMzUwMDQxLCJleHAiOjE3NDIzNTcyNDEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.lgeZ3XOTa1w2Zp8rYmHe4_38QPdf999A36SHmLxLxMw; Max-Age=7200; Expires=Wed, 19 Mar 2025 04:07:21 GMT; Path=/; HTTPOnly",
          },
          changeOrigin: true,
          secure: false,
          rewrite: (path: string) => path.replace(/^\/Dev66_63_ApiProxy/, ""),
          configure: (proxy, options) => {
            const porxyName = "Dev66_63_ApiProxy"
            console.log(`Use proxy '${porxyName}'`)
            proxy.on("proxyReq", function (proxyReq, req, res): void {
              console.log(`Process proxy REQ '${porxyName}'`, {
                Method: proxyReq.method,
                From: `${req.url}`,
                To: `${proxyReq.protocol}//${proxyReq.host}${proxyReq.path}`,
                //ProxyReqHeaders: proxyReq.getHeaders(),
              })
            })
            proxy.on("error", (err, req, res) => {
              console.error(`Process proxy '${porxyName}'`, "Proxy error:", err)
            })
            proxy.on("proxyRes", (proxyRes, req, res) => {
              if (proxyRes.statusCode != 200) {
                console.error(
                  `Process proxy '${porxyName}'`,
                  "Received Response from the Target:",
                  proxyRes.statusCode,
                  "-",
                  req.url,
                  "-",
                  proxyRes.errored
                )
              }
              if (proxyRes.statusCode == 401) {
                console.warn(`Process proxy '${porxyName}'`, "response 401. PLEASE FIX PROXY headers cookies AUTH")
              }
            })
          },
        },
        "/FakeGetAllCurrencyProxy": {
          //target: "'https://4625-118-70-124-128.ngrok-free.app/api/v1/currency/getall'",
          //target: "https://dev.onepay.vn/plba/api/v1/currency/getall",
          target: "https://dev3-ma.onepay.vn/plba/api/v1/currency/getall",
          changeOrigin: true,
          headers: {
            cookie:
              "auth=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJuYW1lIjoiUXXhuqNuIHRy4buLIHZpw6puIGjhu4cgdGjhu5FuZyBo4buHIHRo4buRbmcgaOG7hyB0aOG7kW5nIGjhu4cgdGjhu5FuZyIsImVtYWlsIjoiYWRtaW5Ab25lcGF5LnZuIiwicGhvbmUiOiI4NDkzNDYyMTExMSIsImFkZHJlc3MiOiJ0cmFuIHF1YW5nIGtoYWkgIiwiaWF0IjoxNzQyMjAxMTgxLCJleHAiOjE3NDIyMDgzODEsInN1YiI6IjUxREQ3QzlDN0RBNkEyNzg4RjRBQjUzQjU2QkU2ODExIn0.Rmj2eOZ08zh3G7FnzAs9HyUwnkc0ZljRvN9wUkOi17E; Max-Age=7200; Expires=Mon, 17 Mar 2025 10:46:21 GMT; Path=/; HTTPOnly;",
          },
          secure: false,
          rewrite: (path: string) => path.replace(/^\/FakeGetAllCurrencyProxy/, ""),
          configure: (proxy, options) => {
            const porxyName = "FakeGetAllCurrencyProxy"
            console.log(`Use proxy '${porxyName}'`)
            proxy.on("proxyReq", function (proxyReq, req, res): void {
              console.log(`Process proxy '${porxyName}'`, {
                Method: proxyReq.method,
                From: `${req.url}`,
                To: `${proxyReq.protocol}//${proxyReq.host}${proxyReq.path}`,
              })
            })
            proxy.on("error", (err, req, res) => {
              console.error(`Process proxy '${porxyName}'`, "Proxy error:", err)
            })
            proxy.on("proxyRes", (proxyRes, req, res) => {
              if (proxyRes.statusCode != 200) {
                console.error(
                  `Process proxy '${porxyName}'`,
                  "Received Response from the Target:",
                  proxyRes.statusCode,
                  "-",
                  req.url
                )
              }
              if (proxyRes.statusCode == 401) {
                console.warn(`Process proxy '${porxyName}'`, "response 401. PLEASE FIX PROXY headers cookies AUTH")
              }
            })
          },
        },
      } as Record<string, string | ProxyOptions>,
    },
  }
})

const getVersion = (): string => {
  // Đường dẫn đến package.json
  const packageJsonPath = path.resolve(__dirname, "package.json")
  // Đọc file package.json
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"))
  // Lấy Major Minor và Patch từ version hiện tại
  const packageVersion = packageJson.version // 1.0.0
  //const [major, minor, patch] = packageVersion.split(".")
  // TODO: Tăng giá trị và lưu vào file version.txt

  // (*) Lấy hash của commit hiện tại
  const gitHash = execSync("git rev-parse --short HEAD").toString().trim()

  // (*) Lấy ngày build (Unix timestamp)
  const buildDate = moment().valueOf()

  // Tạo uuid ngẫu nhiên
  const uuid = slugid.nice()

  return `${packageVersion}.${gitHash}.${buildDate}`
}

/** Tạo file version tự động để kiểm tra. File được generated thằng vào dist khi build (chưa có khả năng sử dụng trong dev-mode) */
const useGenerateVersion = (options: ViteConfigData): PluginOption => {
  if (!options.version) return null
  let config: ResolvedConfig | null = null
  return {
    name: "generate-app-version",
    configResolved(resolvedConfig) {
      config = resolvedConfig
    },
    closeBundle() {
      if (!config) return
      const rootPath = path.resolve(config.root)
      const filePath = path.resolve(rootPath, config.build.outDir, "./version.json") // .well-known/version
      const relativedPath = path.relative(rootPath, filePath)
      const dirname = path.dirname(filePath)
      if (!fs.existsSync(dirname)) {
        fs.mkdirSync(dirname, { recursive: true })
      }
      const content = JSON.stringify({ version: options.version })
      fs.writeFileSync(filePath, content, { encoding: "utf-8", flag: "w" })
      logger.info(`Generate version file to ${pc.green(relativedPath)} done! Content: ${pc.cyan(content)}`)
    },
  }
}

/** Visualize and analyze your Rollup bundle to see which modules are taking up space. */
const useRollupVisualizer = (): PluginOption =>
  visualizer({
    filename: "index.stats.html",
  })

/** Hỗ trợ import tự động các component theo config */
const useUnpluginVueComponents = (): PluginOption =>
  Components({
    dts: path.resolve(__dirname, "./src/types", "components.d.ts"),
    // relative paths to the directory to search for components.
    dirs: ["src/components"],
    allowOverrides: true,
    //dirs: ["src/components", "src/pages", "src/views"], // pages có lẽ nên dùng defineAsyncComponent thay vì dùng unplugin-vue-components sử dụng import trực tiếp.
    resolvers: [PrimeVueResolver()], // Tự động import các components của PrimeVue
  })

/** Hỗ trợ sử dụng svg trong ứng dụng */
const useViteAwesomeSvgLoader = (): PluginOption =>
  viteAwesomeSvgLoader({
    //defaultImport: "url",
  })

const useVitePluginHtml = (options: ViteConfigData): PluginOption =>
  createHtmlPlugin({
    minify: options.isDev
      ? false
      : {
          collapseWhitespace: true,
          keepClosingSlash: true,
          removeComments: true,
          removeRedundantAttributes: true,
          removeScriptTypeAttributes: true,
          removeStyleLinkTypeAttributes: true,
          useShortDoctype: true,
          minifyCSS: true,
          minifyJS: true,
        },
    /**
     * Data that needs to be injected into the index.html ejs template
     */
    inject: {
      data: options,
    },
  })

/** Hỗ trợ xóa console khỏi code prod */
const useRemoveConsole = (options: ViteConfigData): PluginOption =>
  options.useRemoveConsolePlugin
    ? removeConsole({
        includes: ["log", "warn", "info"],
        //custom: ["console.log()", "console.warn()", "console.info()", "debugger"],
      })
    : null

/** Hỗ trợ config file env.js trong khi ứng dụng đang chạy mà không phải build lại */
const useRuntimeEnv = (options: ViteConfigData): PluginOption[] =>
  options.useRuntimeEnv
    ? [
        runtimeEnv({
          name: "env",
          generateTypes: true,
          generatedTypesPath: path.resolve(__dirname, "./src"),
          injectHtml: !options.runtimeEnvPath(), // Nếu có khai báo baseUrl thì set false để xử lý custom trong index.html bằng VitePluginHtml
          //injectHtml: true, // set false vì bản hiện tại nó lỗi chỉ tạo ra '/env.js' -> ko sử dụng chung với baseUrl -> tự custom bằng transformIndexHtml ở dưới.
        }),
        !options.runtimeEnvPath()
          ? null
          : {
              name: "my-vite-plugin-runtime-html",
              transformIndexHtml() {
                return [
                  {
                    tag: "script",
                    attrs: {
                      type: "module",
                    },
                    children: `import rtenv from '${options.runtimeEnvPath()}';window.env={...window.env, ...rtenv};`,
                    injectTo: "head-prepend",
                  },
                ]
              },
            },
      ]
    : []
