{
  "editor.codeActionsOnSave": {
    "source.fixAll": "never"
  },
  "editor.formatOnPaste": true,
  "editor.formatOnSave": false,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "workbench.editor.pinnedTabsOnSeparateRow": true,
  "eslint.enable": true, // false: tắt eslint
  "eslint.useFlatConfig": true,
  "files.exclude": {
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true,
    "**/node_modules": true
  },
  "files.associations": {
    "*.json": "jsonc",
    ".prettierrc.json": "jsonc"
  },
  "i18n-ally.keystyle": "nested",
  "i18n-ally.localesPaths": ["src/locales"],
  "i18n-ally.pathMatcher": "{namespace?}/?{locale}.{ext}",
  "i18n-ally.enabledParsers": ["json", "ts"],
  "i18n-ally.namespace": false, // false do eslint chưa có hỗ trợ namespace -> dùng prop thuần
  "editor.indentSize": "tabSize",
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": true,
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
