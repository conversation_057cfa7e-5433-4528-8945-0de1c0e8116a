{
  "compilerOptions": {
    "composite": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "target": "ES2020",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable", "ES2021.String"], // "ES2021.String" là để hỗ trợ cho thằng string.replaceAll chỉ mới có trên 2021
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "preserve",

    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    "noImplicitAny": false,

    /* Aliats */
    "paths": {
      "@/*": ["./src/*"],
      "@assets/*": ["./src/assets/*"],
      "@components/*": ["./src/components/*"],
      "@data/*": ["./src/data/*"],
      "@enums/*": ["./src/enums/*"],
      "@layouts/*": ["./src/layouts/*"],
      "@locales/*": ["./src/locales/*"],
      "@pages/*": ["./src/pages/*"],
      "@plugins/*": ["./src/plugins/*"],
      "@routers/*": ["./src/routers/*"],
      "@services/*": ["./src/services/*"],
      "@stores/*": ["./src/stores/*"],
      "@types/*": ["./src/types/*"],
      "@utils/*": ["./src/utils/*"],
      "@views/*": ["./src/views/*"],
    },
  },
  "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"]
}
