/**
 * @see https://prettier.io/docs/en/configuration.html
 * @type {import("prettier").Config}
 */
const config = {
  //trailingComma: "none",
  trailingComma: "es5",
  semi: false,
  useTabs: false, //# =indent_style=space (.editorconfig)
  tabWidth: 2, //# =indent_size (.editorconfig)
  singleQuote: false,
  printWidth: 120, //# =max_line_length (.editorconfig)
  endOfLine: "lf", //# =end_of_line (.editorconfig)
  overrides: [
    {
      // Hỗ trợ comment cho file .json cụ thể (ko áp dụng cho các file locale)
      files: ["tsconfig*.json"],
      options: {
        parser: "jsonc",
      },
    },
  ],
}

export default config
