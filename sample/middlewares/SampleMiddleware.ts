import type { NavigationGuard } from 'vue-router';

export default (): NavigationGuard => async (to, from, next) => {

    if (!to?.meta?.isAllowAnonymous == true) {
      next();
    } else {
      const isAuthenticated : boolean = true;
      const isAuthorized: boolean = true

      if (isAuthenticated && isAuthorized) {
        next();
      } else {
        // You can use try/catch to get an id token and set it to your request header
        // ex: try { ... next() } catch { ... next({ name: '/login') }
        next('/login');
      }
    }
};