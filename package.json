{"name": "paymentlink-merchantweb", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run type-check && npm run build-only --", "build:oldcmd": "vue-tsc && vite build", "build:dev63": "npm run build -- --mode dev63", "build:devop": "npm run build -- --mode devop", "build:prod": "npm run build -- --mode production", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "eslint:check": "eslint src --ext .vue", "eslint:fix": "eslint src --ext .vue --fix", "audit:prod": "npm audit --omit=dev"}, "dependencies": {"@popperjs/core": "^2.11.8", "@types/crypto-js": "^4.2.2", "@vuepic/vue-datepicker": "^8.4.0", "@vueuse/components": "^10.11.0", "@vueuse/core": "^10.9.0", "@vueuse/head": "^2.0.0", "async-validator": "^4.2.5", "axios": "^1.7.7", "bootstrap": "^5.3.3", "chart.js": "^4.4.2", "crypto-js": "^4.2.0", "decimal.js": "^10.4.3", "deep-freeze-es6": "^3.0.2", "html-to-image": "^1.11.11", "jquery": "^3.7.1", "lodash-es": "^4.17.21", "maska": "^3.0.3", "moment": "^2.30.1", "pinia": "^2.1.7", "primevue": "^3.51.0", "qrcode": "^1.5.4", "vite-awesome-svg-loader": "^1.3.4", "vue": "^3.4.29", "vue-concurrency": "^5.0.0", "vue-draggable-plus": "^0.5.0", "vue-i18n": "^9.10.2", "vue-logger-plugin": "^2.2.3", "vue-router": "^4.3.0", "vue3-cookies": "^1.0.6", "vue3-daterange-picker": "^1.0.1", "xss": "^1.0.15"}, "devDependencies": {"@eslint/js": "^9.4.0", "@intlify/eslint-plugin-vue-i18n": "^3.0.0", "@types/bootstrap": "^5.2.10", "@types/decimal.js": "^7.4.3", "@types/html-minifier-terser": "^7.0.2", "@types/jquery": "^3.5.29", "@types/lodash-es": "^4.17.12", "@types/node": "^20.11.30", "@types/qrcode": "^1.5.5", "@vitejs/plugin-vue": "^5.0.5", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.24.0", "prettier": "3.2.5", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.72.0", "slugid": "^5.0.1", "typescript": "^5.4.4", "typescript-eslint": "^7.5.0", "unplugin-vue-components": "^0.26.0", "vite": "^5.4.11", "vite-plugin-html": "^3.2.2", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-runtime": "^1.3.0", "vite-plugin-vue-devtools": "^7.6.5", "vue-eslint-parser": "^9.4.2", "vue-tsc": "^2.1.10"}}