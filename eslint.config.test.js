/* eslint-disable @typescript-eslint/no-unused-vars */
// @ts-check

import eslint from "@eslint/js"
//import tseslint from "typescript-eslint"
import vue from "eslint-plugin-vue"
import eslintConfigPrettier from "eslint-config-prettier"

//export default tseslint.config(eslint.configs.recommended, eslintConfigPrettier, ...tseslint.configs.recommended, vue.configs["flat/recommended"])

export default [
  eslint.configs.recommended,
  vue.configs["flat/recommended"],
    // eslintConfigPrettier, // Sử dụng eslint với Prettier khiến nó báo lỗi khi mình muốn style riêng -> ko nên enable
]
