// load .env dynamic (not work)
// const rtenv = import.meta.glob("./env.js")
// console.log("xx", rtenv)
// Object.assign(window, {
//   //env: { ...window.env, ...rtenv },
//   env: rtenv,
// })

import { createApp } from "vue"

import "@vuepic/vue-datepicker/dist/main.css"
//import "bootstrap/scss/bootstrap.scss" // Import boostrap css
import "@assets/scss/_primeboostrap_custom.scss"
import "@assets/scss/boostrap.scss"
import "@assets/scss/prime.scss"
//import "primevue/resources/primevue.min.css";
//import "primevue/resources/themes/aura-light-green/theme.css";
import "@assets/main.scss"
import "@assets/style.css"

import "bootstrap"; // Import boostrap js
import "./extenders"

import App from "./App.vue"
import { cookies } from "./plugins/cookies.ts"
import { head } from "./plugins/head.ts"
import { i18n } from "./plugins/i18n.ts"
import { jQuery } from "./plugins/jQuery"
import { layouts } from "./plugins/layouts.ts"
import { logger } from "./plugins/logger.ts"
import { primeVue } from "./plugins/primeVue.ts"
import { router } from "./plugins/router.ts"
import { sanitizeHtmlPlugin as htmlSafe } from "./plugins/sanitizeHtml.ts"
import { stores } from "./plugins/store.ts"

// import {
//   SvgImage, // Basically implements SVG sprites
//   SvgIcon, // Basic SVG icon that uses SvgImage component internally
// } from "vite-awesome-svg-loader/vue-integration"
//import AppSvgIcon from "./components/ui/SvgIcon.vue"
//import AppFilterInput from "./components/ui/AppFilterInput.vue"

//import router from "./routers/router.ts"

const app = createApp(App)
  .use(stores)
  .use(logger)
  .use(router)
  .use(layouts)
  .use(i18n)
  .use(jQuery)
  .use(cookies)
  .use(head)
  .use(primeVue)
  .use(htmlSafe)

app.mount("#app")

//import "bootstrap/dist/js/bootstrap.js"
