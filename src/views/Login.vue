<script setup lang="ts">
import APPCONFIG from "@/appConfig"
import { useGlobalCookies } from "@/plugins/cookies"
import { useAuthService } from "@/services/authService"
import { useAsyncValidator } from "@vueuse/integrations/useAsyncValidator"
import { Rules } from "async-validator"
import { storeToRefs } from "pinia"
import { onMounted, reactive } from "vue"
import { useAsyncTask } from "vue-concurrency"
import { useRoute, useRouter } from "vue-router"

const route = useRoute()
const router = useRouter()

defineProps({
  msg: String,
})

const cookies = useGlobalCookies()
const authService = useAuthService()
const { loginTask, logoutTask } = authService
const { isAuth } = storeToRefs(authService)

const form = reactive({ email: "", password: "" })
const rules: Rules = {
  email: [
    {
      type: "email",
      required: true,
    },
  ],
  password: [
    {
      type: "string",
      required: true,
    },
  ],
}

const { pass: isValid, isFinished, errorFields, execute } = useAsyncValidator(form, rules, {
})

// const canShowLogin = ref(false)
// const canShowLoginDelay = useDebounce(canShowLogin, 1000)
// const canShowLogout = ref(false)
// const canShowLogoutDelay = useDebounce(canShowLogin, 1000)

onMounted(async () => {
  console.log('Login page')
  //canShowLogin.value = true
  //canShowLogout.value = true

  // fake default user
  form.email = APPCONFIG.FAKE_X_USER_ID || "<EMAIL>"
  form.password = "123456"
})

const onClickLoginTask = useAsyncTask(async (signal, options: void) => {
  if (isValid.value) {
    const { success } = await loginTask.perform({
      username: form.email,
      password: form.password,
      returnUrl: route.query.returnUrl?.toString() || "/",
    })

    if (success) {
      // trong loginTask đã xử lý sẵn nhưng cứ đặt thêm ở đây
      router.push({
        path: route.query.returnUrl?.toString() || "/",
      })
    } else {
      alert('login failed.')
    }
  }
})

async function onClickLogout() {
  await logoutTask.perform()
}
</script>

<template>
  <div class="w-100 h-100 d-flex flex-column align-items-center justify-content-center">
    <div v-if="APPCONFIG.MODE_DEV_MIC" class="card border">
      <div class="card-body">
        <h1>Login (DEBUG MODE)</h1>
        <div class="d-flex flex-column gap-3">
          <div>
            <InputText v-model="form.email" :invalid="!!errorFields?.email?.length" type="text" placeholder="email" name="username" />
            <div v-if="errorFields?.email?.length" text-red>{{ errorFields.email[0].message }}</div>
          </div>
          <div>
            <InputText
              v-model="form.password"
              :invalid="!!errorFields?.password?.length"
              type="password"
              placeholder="123456"
            />
            <div v-if="errorFields?.password?.length" text-red>{{ errorFields.email[0].message }}</div>
          </div>
          <div>
            <Button class="w-100" label="Login" :loading="onClickLoginTask.isRunning" :disabled="!isValid" @click="onClickLoginTask.perform()"></Button>
          </div>
        </div>
      </div>
    </div>
    <!-- <div v-else-if="canShowLogoutDelay">
      <Button @click="onClickLogout">Logout</Button>
    </div> -->
  </div>
</template>

<style scoped></style>
