<script setup lang="ts">
import { useLayoutService } from '@/services/layoutService';
import { storeToRefs } from 'pinia';
import { isNavigationFailure, useRouter } from 'vue-router';

const router = useRouter()

// Hỗ trợ để check lại kết quả khi f5 page
// Component chỉ init một lần nên lần sau sẽ biến cờ sẽ chặn
// Hoặc do navigationResult != null nên màn sẽ cố định ko thay đổi
let stopRedirectToReturnUrl = false
const returnUrl = router.currentRoute.value.query["returnUrl"]?.toString()
if (returnUrl) {
  if (stopRedirectToReturnUrl == false) {
    console.log("[Redirect to returnUrl]", returnUrl, "Executing")
    const navigationResult = await router.push({
      path: returnUrl,
    })

    console.log("[Redirect to returnUrl]", returnUrl, "Executed", navigationResult?.message)
    if (isNavigationFailure(navigationResult)) {
      stopRedirectToReturnUrl = true
    }
  } else {
    console.log("[Redirect to returnUrl]", returnUrl, "Stopped")
  }
}

//#region Trường hợp NoAppPermission thì cho phép show logout mà ko có authen
const layoutService = useLayoutService()
const { showLogoutWithouUser } = storeToRefs(layoutService)
showLogoutWithouUser.value = true
//#endregion

</script>

<template>
  <div class="container container-error--403-NoAppPermission">
    <Error403NoAppPermissionComponent></Error403NoAppPermissionComponent>
  </div>
</template>

<style lang="scss">
.layout-body-right:has(.container-error--403-NoAppPermission) {
  background: var(--surface-ghost-hover, #f5f5f5);
}

.container-error--403-NoAppPermission {
  display: flex;
  align-items: center;
  height: 100%;
}
</style>
