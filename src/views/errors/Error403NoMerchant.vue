<script setup lang="ts">
import { isNavigationFailure, useRouter } from 'vue-router';

const router = useRouter()

// Hỗ trợ để check lại kết quả khi f5 page
// Component chỉ init một lần nên lần sau sẽ biến cờ sẽ chặn
// Hoặc do navigationResult != null nên màn sẽ cố định ko thay đổi
let stopRedirectToReturnUrl = false
const returnUrl = router.currentRoute.value.query["returnUrl"]?.toString()
if (returnUrl) {
  if (stopRedirectToReturnUrl == false) {
    console.log("[Redirect to returnUrl]", returnUrl, "Executing")
    const navigationResult = await router.push({
      path: returnUrl,
    })

    console.log("[Redirect to returnUrl]", returnUrl, "Executed", navigationResult?.message)
    if (isNavigationFailure(navigationResult)) {
      stopRedirectToReturnUrl = true
    }
  } else {
    console.log("[Redirect to returnUrl]", returnUrl, "Stopped")
  }
}
</script>

<template>
  <div class="container container-error--403-NoMerchant">
    <Error403NoMerchantComponent></Error403NoMerchantComponent>
  </div>
</template>

<style lang="scss">
.layout-body-right:has(.container-error--403-NoMerchant) {
  background: var(--surface-ghost-hover, #f5f5f5);
}

.container-error--403-NoMerchant {
  display: flex;
  align-items: center;
  height: 100%;
}
</style>
