<script setup lang="ts">
  import ChevronDownIcon from "@assets/icons/chevron-down-for-filter.svg";
  import ReloadIcon from "@assets/icons/reload.svg";
  import DownloadIcon from "@assets/icons/download.svg";
  import SearchIcon from "@assets/icons/search.svg";

  import { onMounted, ref } from "vue";
  import { useReportService } from "@/services/reportService";
  import { usePaymentLinkService } from "@/services/paymentLinkService";
  import {
    DataReportFilter,
    EnumReportLinkType,
    EnumReportTimeInterval,
    GetListReportResponse
  } from "@/apis/reportApi";
  import { EnumExportType, EnumPaymentLinkType } from "@/app.const";
  import { formatCurrency, formatRangeDateOnly } from "@/utils/formatUtil";
  import { useDebounceFn, useToggle, watchDebounced } from "@vueuse/core";
  import moment from "moment";
  import DateTimePickerOverlayPanel from "@/components/ui/dateui/DateTimePickerOverlayPanel.vue";
  import APPCONFIG from "@/appConfig";
  import { PermissionEnum } from "@/enums/permissions"
  import OverlayPanel from "primevue/overlaypanel";
  import { SelectListItem } from "@/types/base";
  import { enumToSelectListItem } from "@/utils/enumUtil";
  import { useAuthService } from "@/services/authService";
  import { useToastService } from "@/services/toastService";
  import { useI18n } from "vue-i18n";
  import InputTextClearable from "@/components/ui/input/InputTextClearable.vue";
  import InputTextFilterClearable from "@/components/ui/input/InputTextFilterClearable.vue";
  import { useAsyncTask } from "vue-concurrency";
  import { onBeforeRouteUpdate } from "vue-router";

  const { t } = useI18n()
  const toastService = useToastService()
  const { hasPermission } = useAuthService()
  const {
    getListReportTask,
    exportSumaryTask
  } = useReportService()
  const { getListPaymentLinkTask } = usePaymentLinkService()

  const getDefaultFilters = (): DataReportFilter => {
    return {
      keywords: "",
      dateRange: {
        startDate: moment().startOf('month').toDate(),
        endDate: moment().endOf('day').toDate()
      },
      dataTableQuery: {
        start: 0,
        length: APPCONFIG.DEFAULT_TABLE_ITEM_PER_PAGE,
      },
      linkType: EnumReportLinkType.All,
      timeInterval: EnumReportTimeInterval.Daily,
      exportType: EnumExportType.CSV
    }
  }

  const dataFilters = ref<DataReportFilter>(getDefaultFilters());
  const resultTable = ref<GetListReportResponse>({
    total: 0,
    data: {
      currency: "",
      totalAuthorizeCount: 0,
      totalCaptureCount: 0,
      totalPurchaseCount: 0,
      totalRefundAmount: 0,
      totalRevenueAmount: 0,
      totalVoidRefundCount: 0,
      transactionMetrics: []
    }
  });

  //#region filter date

  const refFilterDate = ref<InstanceType<typeof DateTimePickerOverlayPanel> | null>(null);
  const [isShowDate] = useToggle()
  const toggleFilterDate = (event: any) => {
    refFilterDate.value?.toggle(event)
  }

  //#endregion

  //#region filter time interval

  const refFilterTimeInterval = ref<InstanceType<typeof OverlayPanel> | null>(null);
  const isVisibledTimeInterval = ref(false);
  function toggleFilterTimeInterval(event: Event) {
    refFilterTimeInterval.value?.toggle(event)
  }

  const listItemsTimeInterval = ref<SelectListItem[]>([])
  listItemsTimeInterval.value = enumToSelectListItem(EnumReportTimeInterval).map((item) => {
    return {
      value: item.value,
      label: item.label,
      selected: item.value === EnumReportTimeInterval.Daily ? true : false
    }
  });

  const labelTimeInterval = ref<string | undefined>(listItemsTimeInterval.value.find(item => item.selected)?.label);
  function onApplyrefFilterTimeInterval(timeInterval: any, isFetch: boolean) {
    dataFilters.value.timeInterval = timeInterval.value;
    timeInterval.selected = true;
    listItemsTimeInterval.value.forEach(item => {
      if (item.value !== timeInterval.value) {
        item.selected = false;
      }
    })
    refFilterTimeInterval.value?.hide();
    labelTimeInterval.value = timeInterval.label

    if (isFetch) fetchData();
  }

  function openPanelTimeInterval() {
    isVisibledTimeInterval.value = true;
  }

  function hidePanelTimeInterval() {
    isVisibledTimeInterval.value = false;
  }

  //#endregion

  //#region filter report link type

  const refFilterLinkType = ref<InstanceType<typeof OverlayPanel> | null>(null)
  const isVisibledFilterLinkType = ref(false);
  function toggleFilterLinkType(event: Event) {
    refFilterLinkType.value?.toggle(event)
  }

  const listItemsLinkType = ref<SelectListItem[]>([])
  listItemsLinkType.value = enumToSelectListItem(EnumReportLinkType).map((item) => {
    return {
      value: item.value,
      label: item.label,
      selected: item.value === EnumReportLinkType.All ? true : false
    }
  })

  const labelLinkType = ref<string | undefined>(listItemsLinkType.value.find(item => item.selected)?.label);
  function onApplyrefFilterLinkType(linkType: any, isFetch: boolean) {
    dataFilters.value.linkType = linkType.value;
    linkType.selected = true;
    listItemsLinkType.value.forEach(item => {
      if (item.value !== linkType.value) {
        item.selected = false;
      }
    })
    refFilterLinkType.value?.hide();
    labelLinkType.value = linkType.label
    if (linkType.value === EnumReportLinkType.PaymentLink) {
      isShowFilterLinkName.value = true;
    } else {
      onApplyFilterLinkName(false);
      isShowFilterLinkName.value = false;
    }

    if (isFetch) fetchData();
  }

  function openPanelType() {
    isVisibledFilterLinkType.value = true;
  }

  function hidePanelType() {
    isVisibledFilterLinkType.value = false;
  }

  //#endregion

  //#region filter link name

  const isShowFilterLinkName = ref(false);
  const filterLinkName = ref('');
  const [isShowExportFileDialog, toggleShowExportFileDialog] = useToggle();
  const listItemsLinkName = ref<SelectListItem[]>([]);

  watchDebounced(filterLinkName, async (filterLinkName) => {
    const value = filterLinkName
    if (!value || value.length <= 1) {
      listItemsLinkName.value = []
      return
    }

    const res = await getListPaymentLinkTask.perform({
      start: -1,
      length: -1,
      keywords: value,
    })

    listItemsLinkName.value = res.data.map((item, index) => {
      return {
        label: item.name,
        value: index + 1,
        selected: false
      }
    })
  })

  const refFilterLinkName = ref<InstanceType<typeof OverlayPanel> | null>(null);
  const isVisibledFilterLinkName = ref(false);
  const refInputLinkName = ref<InstanceType<typeof InputTextFilterClearable> | null>(null);
  function toggleFilterLinkName(event: Event) {
    refFilterLinkName.value?.toggle(event)
  }

  function openPanelLinkName() {
    refInputLinkName.value?.focusInput(true);
    isVisibledFilterLinkName.value = true;
  }

  function hidePanelLinkName() {
    refInputLinkName.value?.focusInput(false);
    isVisibledFilterLinkName.value = false;
  }

  const lableLinkName = ref<string | undefined>(listItemsLinkName.value.find(item => item.selected)?.label);

  const onApplyFilterLinkName = (isFetch: boolean, linkItem?: any) => {
    if (linkItem) {
      dataFilters.value.keywords = linkItem.label;
      linkItem.selected = true;
      listItemsLinkName.value.forEach(item => {
        if (item.value !== linkItem.value) {
          item.selected = false;
        }
      })
      // Chỉ khi bấm vào link item thì mới hide luôn
      refFilterLinkName.value?.hide();
    } else {
      dataFilters.value.keywords = "";
    }

    lableLinkName.value = linkItem ? linkItem.label : '-';

    if (isFetch) fetchData();
  }

  //#endregion

  const fetchDataTask = useAsyncTask(async (signal, input: void) => {
    const res = await getListReportTask.perform({
      keywords: dataFilters.value.keywords,
      linkType: dataFilters.value.linkType,
      dateFrom: dataFilters.value.dateRange.startDate ? dataFilters.value.dateRange.startDate : moment().startOf('month').toDate(),
      dateTo: dataFilters.value.dateRange.endDate ? dataFilters.value.dateRange.endDate : moment().endOf('day').toDate(),
      start: dataFilters.value.dataTableQuery?.start,
      length: dataFilters.value.dataTableQuery?.length,
      timeInterval: dataFilters.value.timeInterval,
      exportType: dataFilters.value.exportType
    })

    resultTable.value = res
  }).keepLatest()
  const fetchData = useDebounceFn(async () => await fetchDataTask.perform())

  const clearData = () => {
    dataFilters.value = getDefaultFilters()

    listItemsTimeInterval.value.forEach(element => {
      if (element.value === dataFilters.value.timeInterval) {
        onApplyrefFilterTimeInterval(element, false)
      }
    });

    listItemsLinkType.value.forEach(element => {
      if (element.value === dataFilters.value.linkType) {
        onApplyrefFilterLinkType(element, false)
      }
    });

    filterLinkName.value = dataFilters.value.keywords;
    onApplyFilterLinkName(false);

    fetchData()
  }

  const exportFile = async () => {
    if (!hasPermission(PermissionEnum.DownloadTransactions)) {
      toastService.error({
        summary: t('common.toast.action-not-permission')
      })
      return
    }
    toggleShowExportFileDialog(true);

    await exportSumaryTask.perform({
      keywords: dataFilters.value.keywords,
      linkType: dataFilters.value.linkType,
      dateFrom: dataFilters.value.dateRange.startDate ? dataFilters.value.dateRange.startDate : moment().startOf('month').toDate(),
      dateTo: dataFilters.value.dateRange.endDate ? dataFilters.value.dateRange.endDate : moment().endOf('day').toDate(),
      start: -1,
      length: -1,
      timeInterval: dataFilters.value.timeInterval,
      exportType: dataFilters.value.exportType
    });

    toggleShowExportFileDialog(false);
  }

  onMounted(async () => {
    fetchData()
  })
  onBeforeRouteUpdate(()=>{
    fetchData()
  })

  const applyFilterDate = () => {
    fetchData();
  }

  const clearInputLinkName = () => {
    filterLinkName.value = ""
    onApplyFilterLinkName(true);
  }
</script>

<template>
  <div class="page-ReportManagement panel">
    <div class="panel-header">
      <div class="panel-header-title">
        <div class="page-title">{{ $t("page-ReportManagement.page-title") }}</div>
      </div>

      <div class="panel-header-body">
        <div class="panel-filter">
          <!-- filter date -->

          <div
            class="filter-item filter-item--date"
            :class="{ 'show-menu': isShowDate }"
            @click="toggleFilterDate"
          >
            <div class="filter-item-header">
              <span>{{ $t("page-ReportManagement.filter.date") }}</span>
              <SvgIcon :src="ChevronDownIcon" />
            </div>

            <div class="filter-item-body">
              {{ formatRangeDateOnly(dataFilters.dateRange?.startDate, dataFilters.dateRange?.endDate) }}
            </div>

            <DateTimePickerOverlayPanel
              ref="refFilterDate"
              v-model:modelValue="dataFilters.dateRange"
              :max-date="moment().endOf('day').toDate()"
              :limit-days="APPCONFIG.MAX_SELECTED_DAYS_IN_DATERANGE"
              @toggle="(isVisible) => isShowDate = isVisible"
              @apply="applyFilterDate()"
            />
          </div>
          <div class="divider divider-vertical"></div>

          <!-- end filter date -->

          <!-- filter time interval -->

          <div
            class="filter-item filter-item--time-interval"
            :class="{ 'show-menu': isVisibledTimeInterval }"
            @click="toggleFilterTimeInterval"
          >
            <div class="filter-item-header">
              <span>{{ $t("page-ReportManagement.filter.time-interval") }}</span>
              <SvgIcon :src="ChevronDownIcon" />
            </div>

            <div class="filter-item-body text-truncate">
              {{ $t(`page-ReportManagement.filter.list-time-interval.${labelTimeInterval}`) }}
            </div>

            <OverlayPanel
              ref="refFilterTimeInterval"
              class="single-select-panel"
              @show="openPanelTimeInterval"
              @hide="hidePanelTimeInterval"
            >
              <div
                v-for="(item, index) of listItemsTimeInterval"
                :key="index"
                class="time-interval-option"
                :class="{ 'time-interval-focus': item.selected }"
                @click="onApplyrefFilterTimeInterval(item, true)"
              >
                <span>{{ $t(`page-ReportManagement.filter.list-time-interval.${item.label}`) }}</span>
              </div>
            </OverlayPanel>
          </div>
          <div class="divider divider-vertical"></div>

          <!-- end time interval -->

          <!-- filter link type -->

          <div
            class="filter-item filter-item--linktype"
            :class="{ 'show-menu': isVisibledFilterLinkType }"
            @click="toggleFilterLinkType"
          >
            <div class="filter-item-header">
              <span>{{ $t("page-ReportManagement.filter.link-type") }}</span>
              <SvgIcon :src="ChevronDownIcon" />
            </div>

            <div class="filter-item-body text-truncate">
              {{ $t(`page-ReportManagement.filter.list-link-type.${labelLinkType}`) }}
            </div>

            <OverlayPanel
              ref="refFilterLinkType"
              class="single-select-panel"
              @show="openPanelType"
              @hide="hidePanelType"
            >
              <div
                v-for="(item, index) of listItemsLinkType"
                :key="index"
                class="link-type-option"
                :class="{ 'link-type-focus': item.selected }"
                @click="onApplyrefFilterLinkType(item, true)"
              >
                <span>{{ $t(`page-ReportManagement.filter.list-link-type.${item.label}`) }}</span>
              </div>
            </OverlayPanel>
          </div>
          <div class="divider divider-vertical"></div>

          <!-- end link type -->

          <!-- link name filter -->

          <div
            v-if="isShowFilterLinkName"
            class="filter-item filter-item--link-name"
            :class="{ 'show-menu': isVisibledFilterLinkName }"
            @click="toggleFilterLinkName"
          >
            <div class="filter-item-header">
              <span>{{ $t("page-ReportManagement.filter.link-name") }}</span>
              <SvgIcon :src="ChevronDownIcon" />
            </div>

            <div class="filter-item-body text-truncate" style="max-width: 200px;">
              {{ lableLinkName ?? '-' }}
            </div>

            <OverlayPanel
              ref="refFilterLinkName"
              class="single-select-panel link-name-overlay-panel"
              @show="openPanelLinkName"
              @hide="hidePanelLinkName"
            >
              <div style="margin-bottom: 20px;">
                <InputGroup class="group-input-filter-clearable">
                  <InputIcon>
                    <SvgIcon :src="SearchIcon" />
                  </InputIcon>
                  <InputTextClearable
                    ref="refInputLinkName"
                    v-model="filterLinkName"
                    :placeholder="$t('page-ReportManagement.filter.placeholder')"
                    class="input-search-column"
                    @clear="clearInputLinkName"
                  />
                </InputGroup>
              </div>

              <div class="link-name-contents">
                <div
                  v-for="(item, index) of listItemsLinkName"
                  :key="index"
                  class="link-name-option text-truncate"
                  :class="{ 'link-name-focus': item.selected }"
                  @click="onApplyFilterLinkName(true, item)"
                >
                  <span>{{ item.label }}</span>
                </div>
              </div>
            </OverlayPanel>
          </div>
          <div v-if="isShowFilterLinkName" class="divider divider-vertical"></div>

          <!-- end link name -->

          <!-- clear filter -->

          <div class="filter-item filter-item--clearfilter">
            <Button
              class="btn-clearfilter"
              :label="$t('page-Transaction-Management.all-trans.clear-filter')"
              text
              @click="clearData"
            >
              <template #icon>
                <span class="p-button-icon-left">
                  <SvgIcon :src="ReloadIcon" />
                </span>
              </template>
            </Button>
          </div>

          <!-- end clear filter -->

          <!-- export file -->

          <div class="divider divider-vertical invisible mr-0"></div>
          <div class="filter-actions ml-auto">
            <Button
              v-tooltip.bottom="{
                value: !hasPermission(PermissionEnum.DownloadReport) ? $t('common.tooltip.action-not-permission') : ''
              }"
              :class="{'no-permission': !hasPermission(PermissionEnum.DownloadReport)}"
              :disabled="!hasPermission(PermissionEnum.DownloadReport)"
              severity="download"
              class="btn-exportdata"
              @click="exportFile()"
            >
              <template #icon>
                <span class="p-button-icon">
                  <SvgIcon :src="DownloadIcon" />
                </span>
              </template>
            </Button>
          </div>

          <!-- end export file -->
        </div>
      </div>
    </div>

    <div ref="refDataTableWrapper" class="panel-body">
      <AppDataTable
        v-model:query="dataFilters.dataTableQuery"
        class="datatable-report-transactions"
        :value="resultTable.data.transactionMetrics"
        :total-records="resultTable.total"
        :loading="fetchDataTask?.isRunning"
        scrollable
        @query-change="fetchData"
      >
        <ColumnGroup type="header">
          <Row>
            <Column :header="$t('page-ReportManagement.datatable.column.date')" field="date" />
            <Column :header="$t('page-ReportManagement.datatable.column.purchase')" field="noOfPurchase" class="column-title" />
            <Column :header="$t('page-ReportManagement.datatable.column.authorize')" field="noOfAuthorize" class="column-title" />
            <Column :header="$t('page-ReportManagement.datatable.column.capture')" field="noOfCapture" class="column-title" />
            <Column :header="$t('page-ReportManagement.datatable.column.voi-refund')" field="noOfRefundVoid" class="column-title" />
            <Column :header="$t('page-ReportManagement.datatable.column.total-revenue')" field="totalRevenue" class="column-title" />
            <Column :header="$t('page-ReportManagement.datatable.column.total-refund')" field="totalAmount" class="column-title" />
          </Row>
          <Row>
            <Column :header="$t('page-ReportManagement.datatable.row.total')" field="date" class="column-total-data" />
            <Column :header="resultTable.data.totalPurchaseCount.toString()" field="noOfPurchase" class="column-total-data" />
            <Column :header="resultTable.data.totalAuthorizeCount.toString()" field="noOfAuthorize" class="column-total-data" />
            <Column :header="resultTable.data.totalCaptureCount.toString()" field="noOfCapture" class="column-total-data" />
            <Column :header="resultTable.data.totalVoidRefundCount.toString()" field="noOfRefundVoid" class="column-total-data" />
            <Column
              :header="formatCurrency(resultTable.data.totalRevenueAmount, resultTable.data.currency)"
              field="totalRevenue"
              class="column-total-data"
            />
            <Column
              :header="formatCurrency(resultTable.data.totalRefundAmount, resultTable.data.currency)"
              field="totalAmount"
              class="column-total-data"
            />
          </Row>
        </ColumnGroup>

        <Column
          field="date"
          style="min-width: 178px;"
          class="column-data-date"
        >
          <template #body="slotProps">
            <span>{{ slotProps.data.GroupLabel }}</span>
          </template>
        </Column>

        <Column
          field="noOfPurchase"
          style="min-width: 148px;"
          class="column-data-number"
        >
          <template #body="slotProps">
            <span>{{ slotProps.data.Metrics.NoOfPurchaseTrans }}</span>
          </template>
        </Column>

        <Column
          field="noOfAuthorize"
          style="min-width: 148px;"
          class="column-data-number"
        >
          <template #body="slotProps">
            <span>{{ slotProps.data.Metrics.NoOfAuthorizeTrans }}</span>
          </template>
        </Column>

        <Column
          field="noOfCapture"
          style="min-width: 148px;"
          class="column-data-number"
        >
          <template #body="slotProps">
            <span>{{ slotProps.data.Metrics.NoOfCaptureTrans }}</span>
          </template>
        </Column>

        <Column
          field="noOfRefundVoid"
          style="min-width: 148px;"
          class="column-data-number"
        >
          <template #body="slotProps">
            <span>{{ slotProps.data.Metrics.NoOfRefundVoidTrans }}</span>
          </template>
        </Column>

        <Column
          field="totalRevenue"
          style="min-width: 144px;"
          class="column-data-number"
        >
          <template #body="slotProps">
            <span>{{ formatCurrency(slotProps.data.Metrics.TotalRevenue, resultTable.data.currency) }}</span>
          </template>
        </Column>

        <Column
          field="totalAmount"
          style="min-width: 144px;"
          class="column-data-number"
        >
          <template #body="slotProps">
            <span>{{ formatCurrency(slotProps.data.Metrics.TotalRefund, resultTable.data.currency) }}</span>
          </template>
        </Column>
      </AppDataTable>
    </div>
  </div>

  <Lazy>
    <Dialog
      :visible="isShowExportFileDialog"
      class="dialog-download-export"
      modal
      :header="$t('page-LinkManagement.export-file.title')"
    >
      <!-- <div class="dialog-download-export-text">
        {{ $t("page-LinkManagement.export-file.running-export") }}
      </div> -->
      <div class="dialog-download-export-progress">
        <ProgressBar mode="indeterminate"></ProgressBar>
      </div>
    </Dialog>
  </Lazy>
</template>

<style lang="scss">
.page-ReportManagement {
  display: flex;
  flex-direction: column;
  height: 100%;

  .panel-header {
    padding: 20px 24px 12px;
    border-bottom: 1px solid #e6e6e6;
  }

  .panel-body {
    padding: 16px 24px;

    flex: 1 1 auto;
    overflow: auto;

    .datatable-report-transactions table {
      thead tr th.column-total-data {
        background-color: #E6F9EF;
        padding: 12px 16px;

        &:not(:first-child) {
          .p-column-header-content {
            justify-content: flex-end;
          }
        }

        .p-column-title {
          color: #006934;
          font-weight: 700;
        }
      }

      thead tr th.column-title {
        .p-column-header-content {
          justify-content: flex-end;
        }
      }

      tbody tr td.column-data-number {
        text-align: end;
      }
    }
  }

  .panel-header-title {
    display: flex;
    margin-bottom: 16px;
  }

  .panel-header-body {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .panel-filter {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    gap: 0rem;
    width: 100%;

    .filter-item {
      display: flex;
      flex-direction: column;
      justify-content: center;

      .filter-item-header {
        display: inline-flex;
        align-items: center;
        gap: var(--layout-spacing-spacing-xxs, 4px);

        color: var(--text-tertiary, #6c6c6c);
        /* Inter/B3/12_Regular */
        font-size: var(--Typeface-size-sm, 0.75rem);
        font-style: normal;
        font-weight: 400;
        line-height: 1rem; /* 133.333% */

        > svg {
          color: inherit;
          stroke: currentColor;
          transition: transform 0.3s;
        }
      }

      &.show-menu {
        .filter-item-header,
        .filter-item-button {
          color: var(--text-info, #2e6be5);
        }
        .filter-item-header {
          > svg {
            transform: rotate(180deg);
          }
        }
      }
    }

    .filter-actions {
      display: inline-flex;
      gap: 1rem;

      color: #2e6be5;
    }

    .filter-item--keywords {
      max-width: 15.625rem;
    }
  }

  .btn-clearfilter {
    margin-left: -1rem;
    margin-right: -1rem;
  }
}

.single-select-panel {
  .p-overlaypanel-content {
    padding: 12px;

    .link-type-option,
    .time-interval-option,
    .link-name-option {
      padding: 8px;
      border-radius: 4px;

      &:not(:last-child) {
        margin-bottom: 8px;
      }

      &:hover {
        background-color: #F5F5F5;
      }
    }
  }
}

.link-type-focus,
.time-interval-focus,
.link-name-focus {
  background: rgba(59, 130, 246, 0.24);
}

.link-name-overlay-panel {
  max-width: 282px;
  max-height: 250px;

  .link-name-contents {
    overflow-y: auto;
    max-height: 160px;
    height: auto;
  }
}

.group-input-filter-clearable{
  display: flex;
  align-items: center;
  border: 1px solid #DCDCDC;
  border-radius: 4px;
  padding: 0 0 0 16px;

  &:focus-within {
    outline: 1px solid #2E6BE5;
  }

  .p-inputtext {
    border: none;
    padding-left: 0;
    height: 40px;

    &:enabled,
    :focus {
      outline: none;
    }
  }
}

.dialog-download-export {
  width: 30rem;

  .p-dialog-header {
    justify-content: center;

    .p-dialog-header-icons {
      display: none;
    }
  }

  .dialog-download-export-text {
    font-size: var(--Typeface-size-md, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 1.25rem; /* 142.857% */
  }
  .dialog-download-export-progress {
    margin-top: 1.5rem;

    .p-progressbar {
      height: 0.5rem;
    }
  }
}
</style>
