<script setup lang="ts">
//import type { AppDateRangPickerValueModelType } from "@/components/ui/AppDateRangPicker.vue"
import { useLogger } from "@/plugins/logger"
import { useDemoService } from "@/services/demoService"
import { useLocaleService } from "@/services/localeService"
import { GetStudentInput } from "@/types/demo"
import { getCurrentInstance, ref } from "vue"
import { useI18n } from "vue-i18n"
//import {AppDateRangPickerValueModelType} from "@/components/ui/AppDateRangPicker";

const { t } = useI18n()
const logger = useLogger()
const localeService = useLocaleService()

defineProps({
  msg: {
    type: String,
    default: "",
  },
})

// Hướng dẫn dùng ref và change its value
const count = ref(0)
count.value++

// Hướng dẫn logger và dùng i18n trans
logger.info("HOME", t("hello"))

// Hướng dẫn change locale cho page
function changeLang(event) {
  logger.warn("HOME.changeLang", getCurrentInstance())

  if (localeService.locale == "en") localeService.setLocale("vi")
  else localeService.setLocale("en")
}

//#region Demo services

const { students, getStudentsTask } = useDemoService()

const data = ref()
const amount = ref(0)
function onQuantityChange(value) {
  amount.value = value
  console.log("onQuantityChange", value, amount.value)
}

const searchData = ref("")
const searchMechan = ref("")

async function filter(event) {
  // Nên dùng để quản lý được kết quả get
  const res = await getStudentsTask.perform({} as GetStudentInput)
  data.value = res

  // getStudentsAsyncTask.perform() // cái này sẽ ko trả kết quả mà xử lý bất đồng bộ, phải control dữ liệu bất đồng bộ qua students

  console.log("filter", data.value)
  console.log("getStudentsTask", getStudentsTask.last?.value) // Warning: if use  getStudentsTask.value -> it return undefine
}

//#endregion

const dateRange = ref({})
function onDateRangeUpdate(value) {
  console.log("onDateRangeUpdate", value)
}
</script>

<template>
  <h1>LANG: {{ localeService.locale }} - {{ $t("hello") }} - {{ $t("hello") }}</h1>

  <h1>{{ msg }}</h1>

  <div class="card">
    <div class="card-body">
      <button type="button" @click="count++">count is {{ count }}</button>

      <button type="button" @click="changeLang">Cahnge</button>

      <p>
        Edit
        <code>components/HelloWorld.vue</code> to test HMR
      </p>
    </div>
  </div>

  <p>
    Check out
    <a href="https://vuejs.org/guide/quick-start.html#local" target="_blank">create-vue</a>, the official Vue + Vite
    starter
  </p>
  <p>
    Install
    <a href="https://github.com/vuejs/language-tools" target="_blank">Volar</a>
    in your IDE for a better DX
  </p>
  <p class="read-the-docs">Click on the Vite and Vue logos to learn more</p>

  <!-- <IconField icon-position="left">
    <InputIcon>
      <SearchIcon />
    </InputIcon>
    <InputText v-model="searchMechan" placeholder="Search" />
  </IconField> -->
  <br />
  <label>form-control: {{ searchData }}</label>
  <input v-model="searchData" type="text" class="form-control" placeholder="Search" />
  <br />
  <label>searchMechan: {{ searchMechan }}</label>
  <InputText v-model="searchMechan" type="text" />
  <br />
  <label>amount: {{ amount }}</label>
  <InputNumber v-model:modelValue="amount" @input="onQuantityChange($event.value)" />
  <br />
  <button type="button" @click="filter">Filter</button>
  <br />
  <Button icon="pi pi-btn-create" severity="create" :label="$t('page-LinkManagement.btn-create')">
    <template #icon>
      <span class="p-button-icon p-button-icon-left">
        <!-- <SvgIcon :src="ReloadIcon" /> -->
        icon
      </span>
    </template>
  </Button>
  <br />
  <Button icon="pi pi-btn-search" severity="create" :label="$t('page-LinkManagement.btn-create')" />

  <DataTable
    table-class="table-tms"
    paginator
    :rows="5"
    :rows-per-page-options="[5, 10, 20, 50]"
    :value="getStudentsTask?.last?.value"
    :loading="getStudentsTask?.isRunning"
  >
    <Column field="_id" header="Id"></Column>
    <Column field="name" header="name"></Column>
    <Column field="age" header="Age"></Column>
  </DataTable>

  <a class="btn btn-primary" data-bs-toggle="modal" href="#exampleModalToggle" role="button">
    Test Open fullscreen modal
  </a>

  <!-- Full screen modal -->
  <div
    id="exampleModalToggle"
    class="modal fade"
    aria-hidden="true"
    aria-labelledby="exampleModalToggleLabel"
    tabindex="-1"
  >
    <div class="modal-dialog modal-fullscreen">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="btn btn-close" data-bs-dismiss="modal" aria-label="Close">
            Close in header
          </button>
          <h5 id="exampleModalFullscreenLabel" class="modal-title h4">Full screen modal</h5>
          <button type="button" class="btn btn-save btn-primary" data-bs-dismiss="modal" aria-label="Save">Save</button>
        </div>
        <div class="modal-body">
          <h1>MY FULL SCREEN</h1>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close in footer</button>
        </div>
      </div>
    </div>
  </div>

  <div>
    dateRange: {{ dateRange }}
    <br />
    <!-- <AppDateRangPicker v-model="dateRange" @update="onDateRangeUpdate" /> -->
    <AppDateRangPickerInline @update="onDateRangeUpdate" />
  </div>
</template>

<style scoped>
.read-the-docs {
  color: #888;
}
</style>
@/services/localeService@/services
