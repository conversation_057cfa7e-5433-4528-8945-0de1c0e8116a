<script setup lang="ts">
import InputDateTimePickerSelectable from '@/components/ui/dateui/InputDateTimePickerSelectable.vue';
import { ref } from 'vue';

defineProps({})

const date = ref<Date>(new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate()))
const dateTime = ref()

const daterange = ref()
const datetimerange = ref()

</script>

<template>
  <div class="d-flex justify-content-center">
    <form class="p-3" style="width: 500px; ">
      <div class="mb-3">
        <label for="input11" class="form-label">Date input</label>

        <InputDateTimePickerSelectable v-model="daterange" :time-picker="false"></InputDateTimePickerSelectable>

        <div id="input11help" class="form-text">{{ daterange }}</div>
      </div>

      <div class="mb-3">
        <label for="input11" class="form-label">Date input</label>

        <InputDateTimePickerAppliable v-model="date" :timePicker="true"></InputDateTimePickerAppliable>

        <div id="input11help" class="form-text">{{ date }}</div>
      </div>
      
      <div class="mb-3">
        <label for="input1" class="form-label">Date range inline</label>

        <input type="text" class="form-control mb-1" id="input1" aria-describedby="emailHelp" :value="daterange" />

        <DateRangPickerInline
          v-model="daterange" 
          :show-ranges-menu="false"
          :autoApply="true"
        ></DateRangPickerInline>

        <div id="input1help" class="form-text">{{ daterange }}</div>
      </div>

      <!-- <div class="mb-3">
        <label for="input2" class="form-label">DateTime range inline without ranges menu</label>

        <input type="text" class="form-control mb-1" id="input2" aria-describedby="emailHelp" :value="datetimerange" />

        <DateRangPickerInline v-model="datetimerange" :show-ranges-menu="false" :time-picker="true" :timePicker24Hour="false"></DateRangPickerInline>

        <div id="input2help" class="form-text">{{ datetimerange }}</div>
      </div> -->

      <button type="submit" class="btn btn-primary">Submit</button>
    </form>
  </div>
</template>

<style scoped>
</style>
