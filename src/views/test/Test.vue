<script setup lang="ts">
import ToastCopyIcon from "@assets/icons/toast-copy.svg"
import ToastErorrIcon from "@assets/icons/toast-error.svg"
import ToastSendMailIcon from "@assets/icons/toast-send-mail.svg"
import ToastSuccessIcon from "@assets/icons/toast-success.svg"

import Toast from "primevue/toast"
import { ref } from "vue"

const icons = ref([
  { name: "success", component: ToastSuccessIcon },
  { name: "erorr", component: ToastErorrIcon },
  { name: "copy", component: ToastCopyIcon },
  { name: "mail", component: ToastSendMailIcon },
])

defineProps({
  toastType: String,
})

function getImageUrl(url: string): string {
  return url
}
</script>

<template>
  <Toast position="bottom-center" group="bottom">
    <template #container="{ message, closeCallback }">
      <span v-for="icon in icons" :key="icon.name">
        <span v-if="icon.name === message.icon">
          {{ icon.component }}
        </span>
      </span>

      <span>{{ message.detail }}</span>
      <div v-if="message.isSendMail" class="d-inline">
        <svg width="1" height="24" viewBox="0 0 1 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <line x1="0.5" y1="2.18557e-08" x2="0.499999" y2="24" stroke="white" />
        </svg>
        <a :href="message.send">Send email to customer</a>
        <div class="d-inline" style="cursor: pointer" @click="closeCallback">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M16.067 15.1832C16.1251 15.2412 16.1712 15.3102 16.2026 15.386C16.234 15.4619 16.2502 15.5432 16.2502 15.6253C16.2502 15.7075 16.234 15.7888 16.2026 15.8647C16.1712 15.9405 16.1251 16.0095 16.067 16.0675C16.009 16.1256 15.94 16.1717 15.8642 16.2031C15.7883 16.2345 15.707 16.2507 15.6249 16.2507C15.5427 16.2507 15.4614 16.2345 15.3855 16.2031C15.3097 16.1717 15.2407 16.1256 15.1827 16.0675L9.99986 10.8839L4.81705 16.0675C4.69977 16.1848 4.54071 16.2507 4.37486 16.2507C4.20901 16.2507 4.04995 16.1848 3.93267 16.0675C3.8154 15.9503 3.74951 15.7912 3.74951 15.6253C3.74951 15.4595 3.8154 15.3004 3.93267 15.1832L9.11627 10.0003L3.93267 4.81753C3.8154 4.70026 3.74951 4.5412 3.74951 4.37535C3.74951 4.2095 3.8154 4.05044 3.93267 3.93316C4.04995 3.81588 4.20901 3.75 4.37486 3.75C4.54071 3.75 4.69977 3.81588 4.81705 3.93316L9.99986 9.11675L15.1827 3.93316C15.2999 3.81588 15.459 3.75 15.6249 3.75C15.7907 3.75 15.9498 3.81588 16.067 3.93316C16.1843 4.05044 16.2502 4.2095 16.2502 4.37535C16.2502 4.5412 16.1843 4.70026 16.067 4.81753L10.8835 10.0003L16.067 15.1832Z"
              fill="white"
            />
          </svg>
        </div>
      </div>
    </template>
  </Toast>

  <Toast position="center" group="center">
    <template #container="{ message }">
      <img v-if="message.icon && message.icon !== 'undefined'" :src="getImageUrl(message.icon)" />
      <span>{{ message.detail }}</span>
    </template>
  </Toast>
</template>

<style lang="scss">
.tms-layout-body {
  .tms-layout-page-content {
    .toast-custom {
      max-width: 573px;
      max-height: 100px;
      min-width: 195px;
      min-height: 40px;

      &__noti-success {
        width: 275px;
        height: 40px;
        border-radius: 4px;
        padding: 8px 24px;
        background-color: #00bf5f;
        display: flex;
        align-items: center;

        img {
          padding-right: 8px;
        }

        span {
          color: #fff;
          font-family: "Inter", sans-serif;
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          text-align: left;
        }
      }

      &__noti-erorr {
        width: 312px;
        height: 40px;
        border-radius: 4px;
        padding: 8px 24px;
        background-color: #e04d61;

        img {
          padding-right: 8px;
        }

        span {
          color: #fff;
          font-family: "Inter", sans-serif;
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          text-align: left;
        }
      }

      &__copy-link {
        width: 195px;
        height: 100px;
        border-radius: 8px;
        padding: 16px 16px 16px 20px;
        gap: 8px;
        opacity: 0.85;
        background-color: #000;

        img {
          display: block;
          margin-left: auto;
          padding-bottom: 8px;
          margin-right: auto;
        }

        span {
          color: #fff;
          font-family: "Inter", sans-serif;
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          text-align: center;
        }
      }

      &__send-mail {
        width: 573px;
        height: 48px;
        border-radius: 8px;
        padding: 12px 16px;
        background-color: #282829;

        span {
          color: #fff;
          font-family: "Inter", sans-serif;
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          padding-right: 16px;
        }

        a {
          color: #5889ea;
          padding: 0 16px;
          text-decoration: none;
        }
      }
    }
  }
}
</style>
