<script setup lang="ts">
import ChevronDownIcon from "@assets/icons/chevron-down-for-filter.svg"
import DownloadIcon from "@assets/icons/download.svg"
import ReloadIcon from "@assets/icons/reload.svg"
import DisplayColumn from "@assets/icons/transaction-display-column.svg"
import MoreFiltersHasFilters from "@assets/icons/transaction-more-filters-icon-hasFilters.svg"
import MoreFilters from "@assets/icons/transaction-more-filters-icon.svg"

import { handleApiError } from "@/apis/https/httpApi"
import {
  ApproveRejectTransactionRequest,
  GetListRefundItem,
  GetListTranItem,
  ListTransaction,
  Transaction,
  TransactionAction,
  TransactionColumnDisplay,
} from "@/apis/transactionApi"
import { EnumExportType, EnumSearchRefundType, EnumSearchTransactionType, EnumTransactionStatus } from "@/app.const"
import APPCONFIG from "@/appConfig"
import { PermissionEnum } from "@/enums/permissions"
import { AppRouteNames } from "@/enums/routers"
import { useAuthService } from "@/services/authService"
import { useLayoutService } from "@/services/layoutService"
import { useToastService } from "@/services/toastService"
import { useTransactionService } from "@/services/transactionService"
import { DateRangModel, IPagedRequest, SelectListItem } from "@/types/base"
import { urlHash } from "@/utils/urlHash"
import { enumToSelectListItem } from "@utils/enumUtil"
import { formatCurrency, formatDateTime, formatRangeDateTime, formatTimeZone } from "@utils/formatUtil"
import { useDebounceFn, useToggle } from "@vueuse/core"
import { isAxiosError } from "axios"
import moment from "moment"
import type { DataTableRowClickEvent } from "primevue/datatable"
import type { TabViewChangeEvent } from "primevue/tabview"
import { computed, onMounted, ref, watch } from "vue"
import { useAsyncTask } from "vue-concurrency"
import { useI18n } from "vue-i18n"
import { onBeforeRouteUpdate, useRoute, useRouter } from "vue-router"

import TranMoreFilter from "@/components/transaction/TranMoreFilter.vue"
import AppMultipleSelectOverlayPanel from "@/components/ui/AppMultipleSelectOverlayPanel.vue"
import type DateTimePickerOverlayPanel from "@components/ui/DateTimePickerOverlayPanel.vue"
import { result } from "lodash-es"

interface DataFilter {
  merchant_id?: string
  from_date?: datetime
  to_date?: datetime
  dataTableQuery: IPagedRequest
}

interface DataFilterRefund {
  keywords?: string
  dateRange?: DateRangModel
  refundType: any[]
  refundStatus: any[]
  dataTableQuery: IPagedRequest
}

const { t } = useI18n()
const toastService = useToastService()
const {
  getListAllTransTask,
  formatTranStatus,
  formatTranType,
  formatCololumnTable,
  getListRefundTask,
  exportTransactionsTask,
  exportRefundTask,
  approveRejectTask,
  formatPaymentMethod,
} = useTransactionService()
const { authUserId, authMerchantProfile, hasPermission } = useAuthService()
const route = useRoute()
const router = useRouter()

//const isRefundTrans = ref<boolean>(false);
const isRefundTrans = computed<boolean>(() => tabActiveIndex.value == EnumViewTab.TabRefund)
const dataAllTrans = ref<Transaction[]>([])
const allTransTotal = ref<number>(0)
const allTransAmount = ref<number>(0)
const refTableAllTrans = ref<HTMLElement>()
const dataRefund = ref<GetListRefundItem[]>([])
const selectedItem = ref<any[]>([])
const [isShowDetailDialog, toggleShowDetailDialog] = useToggle()
const isCheckAllRefund = ref<boolean>(false)

const linkId = computed(() => router.currentRoute.value.query["id"]?.toString())
const linkName = computed(() => router.currentRoute.value.query["linkName"]?.toString())
const fromDate = computed(() => router.currentRoute.value.query["fromDate"]?.toString())
const endDate = computed(() => router.currentRoute.value.query["endDate"]?.toString())

const isSelectedTransType = ref(true)
const isSelectedAllRefundStatus = ref(true)
const isSelectedAllRefundType = ref(true)

const getDefaultFilters = (isClear?: boolean): DataFilter => {
  return {
    merchant_id: "TESTONEPAYUSD",
    from_date: moment().startOf("day").toDate(),
    to_date: moment().endOf("day").toDate(),
    dataTableQuery: {
      start: 0,
      length: APPCONFIG.DEFAULT_TABLE_ITEM_PER_PAGE,
    },
  }
}

const getDefaultRefundFilters = () => {
  return {
    refundType: [],
    refundStatus: [],
    dateRange: {
      startDate: moment().startOf("day").toDate(),
      endDate: moment().endOf("day").toDate(),
    },
    dataTableQuery: {
      start: 0,
      length: APPCONFIG.DEFAULT_TABLE_ITEM_PER_PAGE,
    },
  }
}

const dataFilters = ref<DataFilter>(getDefaultFilters())
const dataRefundFilters = ref<DataFilterRefund>(getDefaultRefundFilters())

const visibleAction = ref(false)
const itemAction = ref<TransactionAction>({
  title: "",
  isVoid: false,
  isApprove: false,
  isReject: false,
  isCapture: false,
  isRefund: false,
  textAction: "",
})

/// show tran date
const refFilterDate = ref<InstanceType<typeof DateTimePickerOverlayPanel> | null>(null)
const [isVisibledDate] = useToggle()
const toggleFilterDate = (event: any) => {
  refFilterDate.value?.toggle(event)
}

/// show tran type
const refFilterTranType = ref<InstanceType<typeof AppMultipleSelectOverlayPanel> | null>(null)
const [isvisibleTranType] = useToggle()
const toggleFilterTranType = (event: any) => {
  refFilterTranType.value?.toggle(event)
}

// show refund create date
const refFilterRefundCreateDate = ref<InstanceType<typeof DateTimePickerOverlayPanel> | null>(null)
const [isVisibledRefundCreateDate] = useToggle()
const toggleFilterRefundCreateDate = (event: any) => {
  refFilterRefundCreateDate.value?.toggle(event)
}

// show refund type
const refFilterRefundType = ref<InstanceType<typeof AppMultipleSelectOverlayPanel> | null>(null)
const [isVisibledRefundType] = useToggle()
const toggleFilterRefundType = (event: any) => {
  refFilterRefundType.value?.toggle(event)
}

// show status
const refFilterStatus = ref<InstanceType<typeof AppMultipleSelectOverlayPanel> | null>(null)
const [isVisibledStatus] = useToggle()
const toggleFilterStatus = (event: any) => {
  refFilterStatus.value?.toggle(event)
}

const listItemTranType = computed<SelectListItem[]>(() =>
  enumToSelectListItem(EnumSearchTransactionType)
    .filter((item) => {
      return item.value !== EnumSearchTransactionType.RequestRefund
    })
    .map((item) => {
      return {
        value: item.value,
        label: t(`page-Transaction-Management.all-trans.list-trans-type.${item.label}`),
      }
    })
)

const listItemRefundType = computed<SelectListItem[]>(() =>
  enumToSelectListItem(EnumSearchRefundType).map((item) => {
    return {
      value: item.value,
      label: t(`page-Transaction-Management.refund.list-refund-type.${item.label}`),
    }
  })
)
const labelRefundType = computed(() =>
  dataRefundFilters.value.refundType.length == 0
    ? t(`page-Transaction-Management.refund.list-refund-type.All`)
    : listItemTranType.value
        .filter((item) => dataRefundFilters.value.refundType.includes(item.value))
        .map((item) => item.label)
        .join(", ")
)

const listItemRefundStatus = computed<SelectListItem[]>(() =>
  enumToSelectListItem(EnumTransactionStatus).map((item) => {
    return {
      value: item.value,
      label: t(`page-Transaction-Management.refund.list-trans-status.${item.label}`),
    }
  })
)
const labelRefundStatus = computed(() =>
  dataRefundFilters.value.refundStatus.length == 0
    ? t(`page-Transaction-Management.refund.list-trans-status.All`)
    : listItemRefundStatus.value
        .filter((item) => dataRefundFilters.value.refundStatus.includes(item.value))
        .map((item) => item.label)
        .join(", ")
)

const refMoreFilter = ref<InstanceType<typeof TranMoreFilter> | null>(null)
const [isvisibleMoreFilter] = useToggle()
const toggleMoreFilter = (event: any) => {
  refMoreFilter.value?.toggle(event)
}

const visible = ref(false)
const columnDisplay = ref<TransactionColumnDisplay[]>([])
const getLocalStorage = localStorage.getItem(`dispay-column-${authUserId}`)
const columnIsDisplayed = ref<any[]>(JSON.parse(getLocalStorage != null ? getLocalStorage : "[]"))

// todo: get column display from api
// if (columnIsDisplayed.value && columnIsDisplayed.value.length > 0) {
//   coulumnDisplay.value = columnIsDisplayed.value
// } else {
//   coulumnDisplay.value = [
//     { isChecked: true, ordinal: 1, isDisabled: true, value: 1, field: "trans-ref", isDisplay: true },
//     { isChecked: true, ordinal: 2, isDisabled: false, value: 2, field: "order-ref", isDisplay: true },
//     { isChecked: false, ordinal: 3, isDisabled: false, value: 3, field: "amount", isDisplay: true },
//     { isChecked: false, ordinal: 4, isDisabled: false, value: 4, field: "note", isDisplay: true },
//     { isChecked: false, ordinal: 5, isDisabled: false, value: 5, field: "payment-date", isDisplay: true },
//     { isChecked: false, ordinal: 6, isDisabled: false, value: 6, field: "card-type", isDisplay: true },
//     { isChecked: true, ordinal: 7, isDisabled: false, value: 7, field: "card-number", isDisplay: true },
//     { isChecked: false, ordinal: 8, isDisabled: false, value: 8, field: "operator", isDisplay: true },
//     { isChecked: false, ordinal: 9, isDisabled: false, value: 9, field: "status", isDisplay: true },
//     { isChecked: false, ordinal: 10, isDisabled: false, value: 10, field: "response-code", isDisplay: true },
//   ]
// }

columnDisplay.value = [
    { isChecked: true, ordinal: 1, isDisabled: true, value: 1, field: "trans-ref", isDisplay: true },
  { isChecked: true, ordinal: 2, isDisabled: true, value: 2, field: "order-ref", isDisplay: true },
  { isChecked: true, ordinal: 3, isDisabled: true, value: 3, field: "amount", isDisplay: true },
  { isChecked: true, ordinal: 4, isDisabled: true, value: 4, field: "note", isDisplay: true },
  { isChecked: true, ordinal: 5, isDisabled: true, value: 5, field: "payment-date", isDisplay: true },
  { isChecked: true, ordinal: 6, isDisabled: true, value: 6, field: "card-type", isDisplay: true },
  { isChecked: true, ordinal: 7, isDisabled: true, value: 7, field: "card-number", isDisplay: true },
  { isChecked: true, ordinal: 8, isDisabled: true, value: 8, field: "operator", isDisplay: true },
  { isChecked: true, ordinal: 9, isDisabled: true, value: 9, field: "status", isDisplay: true },
  { isChecked: true, ordinal: 10, isDisabled: true, value: 10, field: "response-code", isDisplay: true },
  ]

const onApplyDate = () => {
  fetchDataAllTrans()
}

const onApplyTranType = () => {
  fetchDataAllTrans()
}

const onApplyMoreFilter = () => {
  fetchDataAllTrans()
}

const onClickClearFilter = async () => {
  const linkId = router.currentRoute.value.query["linkId"]?.toString()
  if (linkId) {
    // do ko thể change được query nên tốt nhất là ngăn back về chính nó. thực hiện reload chuẩn
    await router.replace({ query: {} })
    const layoutService = useLayoutService()
    layoutService.reload()
    return
  }
  dataFilters.value = getDefaultFilters(true)
  isSelectedTransType.value = true
  refMoreFilter.value?.checkAllFilter()

  fetchDataAllTrans()
}

// watch(
//   () => dataFilters.value.keywords,
//   (keywords) => {
//     onChangeFilterTranKeyword()
//   }
// )

const onChangeFilterTranKeyword = useDebounceFn(() => {
  fetchDataAllTrans()
}, APPCONFIG.DEBOUNCED_DELAY_INPUT)

watch(
  () => dataRefundFilters.value.keywords,
  (keywords) => {
    onChangeFilterRefundKeyword()
  }
)

const onChangeFilterRefundKeyword = useDebounceFn(() => {
  fetchDataRefund()
}, APPCONFIG.DEBOUNCED_DELAY_INPUT)

const onApplyRefundCreateDate = () => {
  fetchDataRefund()
}

const onApplyRefundType = () => {
  fetchDataRefund()
}

const onApplyStatus = () => {
  fetchDataRefund()
}

const onClickClearFilterRefund = () => {
  dataRefundFilters.value = getDefaultRefundFilters()
  isSelectedAllRefundType.value = true

  fetchDataRefund()
}

const changeTab = (event: TabViewChangeEvent) => {
  selectedItem.value = []

  if (event.index === 0) {
    //isRefundTrans.value = false;
    fetchDataAllTrans()
  } else {
    //isRefundTrans.value = true;
    fetchDataRefund()
  }
}

const closeDialog = () => {
  visible.value = false
}

const openDialog = () => {
  visible.value = true
}

const fetchDataAllTransTask = useAsyncTask(async (signal, input: void) => {
  // custom query
  //const linkId = urlHash.getUrlHashParameter("linkId")
  const linkId = router.currentRoute.value.query["linkId"]?.toString()

  const res = await getListAllTransTask.perform({
    from_date: dataFilters.value.from_date,
    to_date: dataFilters.value.to_date,
    merchant_id: dataFilters.value.merchant_id,
    start: dataFilters.value.dataTableQuery?.start,
    length: dataFilters.value.dataTableQuery?.length,
  })

  dataAllTrans.value = res.list
  allTransTotal.value = res.total_records
}).keepLatest()
const fetchDataAllTrans = useDebounceFn(async () => await fetchDataAllTransTask.perform())

const fetchDataRefundTask = useAsyncTask(async (signal, input: void) => {
  const defaultType = [
    EnumSearchTransactionType.Refund,
    EnumSearchTransactionType.RefundCapture,
    EnumSearchTransactionType.RefundDispute,
    EnumSearchTransactionType.VoidRefund,
    EnumSearchTransactionType.VoidRefundCapture,
  ]

  const res = await getListRefundTask.perform({
    keywords: dataRefundFilters.value.keywords,
    dateFrom: dataRefundFilters.value.dateRange?.startDate,
    dateTo: dataRefundFilters.value.dateRange?.endDate,
    refundType: dataRefundFilters.value.refundType,
    refundStatus: dataRefundFilters.value.refundStatus,
    start: dataRefundFilters.value.dataTableQuery?.start,
    length: dataRefundFilters.value.dataTableQuery?.length,
  })

  dataRefund.value = res.data
  allTransTotal.value = res.total
  allTransAmount.value = res.totalAmount
}).keepLatest()
const fetchDataRefund = useDebounceFn(async () => {
  selectedItem.value = []
  isCheckAllRefund.value = false
  await fetchDataRefundTask.perform()
})

onMounted(async () => {
  if (isRefundTrans.value) {
    fetchDataRefund()
  } else {
    fetchDataAllTrans()
  }
})
onBeforeRouteUpdate(() => {
  if (isRefundTrans.value) {
    fetchDataRefund()
  } else {
    fetchDataAllTrans()
  }
})

const onDataTableQueryChange = () => {
  fetchDataAllTrans()
}

const onDataTableRefundQueryChange = () => {
  fetchDataRefund()
}

const onDataTableRowClick = (event: DataTableRowClickEvent) => {
  detailItem(event.data as GetListTranItem)
}

const detailItem = (item: GetListTranItem) => {
  // trnasId.value = item.id;
  // toggleShowDetailDialog(true)

  if (item.paymentMethod && item.paymentMethod === "VIETQR") return

  router.push({
    name: AppRouteNames.TRANSACTIONMANAGEMENT_DETAIL,
    params: {
      tranId: item.id,
    },
  })
}

const checkAllRefund = () => {
  if (isCheckAllRefund.value) {
    dataRefund.value.map((el) => {
      if (el.status === EnumTransactionStatus.AwaitMerchantApproval) {
        selectedItem.value.push(el.id)
      }
    })
  } else {
    selectedItem.value = []
  }
}

const hideDetail = () => {
  if (!isRefundTrans.value) fetchDataAllTrans()
  else fetchDataRefund()
}

const [isShowExportFileDialog, toggleShowExportFileDialog] = useToggle()

const exportTransactions = async () => {
  if (!hasPermission(PermissionEnum.DownloadTransactions)) {
    toastService.error({
      summary: t("common.toast.action-not-permission"),
    })
    return
  }

  toggleShowExportFileDialog(true)

  // await exportTransactionsTask.perform({
  //   exportType: EnumExportType.CSV,
  //   keywords: dataFilters.value.keywords,
  //   dateFrom: dataFilters.value.dateRange?.startDate,
  //   dateTo: dataFilters.value.dateRange?.endDate,
  //   paymentMethod: dataFilters.value.paymentMethod,
  //   transactionStatus: dataFilters.value.tranStatus,
  //   transactionType: dataFilters.value.tranType,
  // })

  toggleShowExportFileDialog(false)
}

const exportRefund = async () => {
  toggleShowExportFileDialog(true)

  await exportRefundTask.perform({
    exportType: EnumExportType.CSV,
    keywords: dataRefundFilters.value.keywords,
    dateFrom: dataRefundFilters.value.dateRange?.startDate,
    dateTo: dataRefundFilters.value.dateRange?.endDate,
    refundType: dataRefundFilters.value.refundType,
    refundStatus: dataRefundFilters.value.refundStatus,
  })

  toggleShowExportFileDialog(false)
}

const openApprove = () => {
  if (!hasPermission(PermissionEnum.DoApproveRefund)) {
    toastService.error({
      summary: t("common.toast.action-not-permission"),
    })
    return
  }

  const approveTran = {
    title: t("page-Transaction-Detail.dialog-actions.approve.title"),
    isVoid: false,
    isApprove: true,
    isReject: false,
    isCapture: false,
    isRefund: false,
    textAction: t("page-Transaction-Detail.dialog-actions.approve.actions.confirm"),
  }

  openModalAction(approveTran)
}

const openReject = () => {
  if (!hasPermission(PermissionEnum.DoApproveRefund)) {
    toastService.error({
      summary: t("common.toast.action-not-permission"),
    })
    return
  }

  const rejectTran = {
    title: t("page-Transaction-Detail.dialog-actions.reject.title"),
    isVoid: false,
    isApprove: false,
    isReject: true,
    isCapture: false,
    isRefund: false,
    textAction: t("page-Transaction-Detail.dialog-actions.reject.actions.confirm"),
  }

  openModalAction(rejectTran)
}

const openModalAction = (item: TransactionAction) => {
  itemAction.value = item
  visibleAction.value = true
}

const closeModalAction = () => {
  visibleAction.value = false
}

const applyAction = async (action: TransactionAction) => {
  if (!hasPermission(PermissionEnum.DoApproveRefund)) {
    toastService.error({
      summary: t("common.toast.action-not-permission"),
    })
    return
  }

  if (action.isApprove) {
    await approveList()
  } else if (action.isReject) {
    await rejecttList()
  }
}

const approveList = async () => {
  const request: ApproveRejectTransactionRequest[] = selectedItem.value.map((item) => {
    const approveItem = dataRefund.value.find((el) => el.id === item)

    return {
      id: approveItem?.transactionId,
      op: "replace",
      path: "/approve",
      value: {
        merchant_id: approveItem?.merchantId,
        transaction_reference: approveItem?.merchantTransRef,
        currency: approveItem?.currency,
      },
      skipCallSynchronize: true, // ngăn ép đồng bộ hàng loạt gây treo hệ thống.
      service: approveItem?.service,
      note: "",
    }
  })

  await approveRejectTask
    .perform(request)
    .then((response) => {
      if (response && !response.errors) {
        toastService.success({ summary: t("page-Transaction-Detail.dialog-actions.approve.toast.success") })
      } else {
        toastService.error({ summary: t("page-Transaction-Detail.dialog-actions.approve.toast.erorr") })
      }
    })
    .catch((error) => {
      if (isAxiosError<{ errors: [] }>(error) && error.response?.data.errors) {
        console.error("approveRejectList", request, error.response?.data.errors)
        const total = selectedItem.value.length
        const numberSuccess = total - error.response.data.errors.length
        if (numberSuccess > 0 && numberSuccess < total) {
          toastService.warn({
            summary: t("page-Transaction-Detail.dialog-actions.approve.toast.number-success", {
              number: numberSuccess,
              total: total,
            }),
          })
        } else {
          toastService.error({ summary: t("page-Transaction-Detail.dialog-actions.approve.toast.erorr") })
        }
        return
      }
      handleApiError(error)
    })

  if (!approveRejectTask.isRunning) {
    visibleAction.value = false
    selectedItem.value = []
    await fetchDataRefund()
  }
}

const rejecttList = async () => {
  const request: ApproveRejectTransactionRequest[] = selectedItem.value.map((item) => {
    const approveItem = dataRefund.value.find((el) => el.id === item)

    return {
      id: approveItem?.transactionId,
      op: "replace",
      path: "/reject",
      value: {
        merchant_id: approveItem?.merchantId,
        transaction_reference: approveItem?.merchantTransRef,
        currency: approveItem?.currency,
      },
      skipCallSynchronize: true, // ngăn ép đồng bộ hàng loạt gây treo hệ thống.
      service: approveItem?.service,
      note: "",
    }
  })

  await approveRejectTask
    .perform(request)
    .then((response) => {
      if (response && !response.errors) {
        toastService.success({ summary: t("page-Transaction-Detail.dialog-actions.reject.toast.success") })
      } else {
        toastService.error({ summary: t("page-Transaction-Detail.dialog-actions.reject.toast.erorr") })
      }
    })
    .catch((error) => {
      if (isAxiosError<{ errors: [] }>(error) && error.response?.data.errors) {
        console.error("approveRejectList", request, error.response?.data.errors)
        const total = selectedItem.value.length
        const numberSuccess = total - error.response.data.errors.length
        if (numberSuccess > 0 && numberSuccess < total) {
          toastService.warn({
            summary: t("page-Transaction-Detail.dialog-actions.reject.toast.number-success", {
              number: numberSuccess,
              total: total,
            }),
          })
        } else {
          toastService.error({ summary: t("page-Transaction-Detail.dialog-actions.reject.toast.erorr") })
        }
        return
      }
      handleApiError(error)
    })

  if (!approveRejectTask.isRunning) {
    visibleAction.value = false
    selectedItem.value = []
    await fetchDataRefund()
  }
}
//#region Control View tabs & type

enum EnumViewTab {
  TabAllTrans = 0,
  TabRefund = 1,
}

const tabActiveIndex = ref<number>(Number(urlHash.getUrlHashParameter("tab") ?? EnumViewTab.TabAllTrans))
watch(tabActiveIndex, (tabActiveIndex) => urlHash.setUrlHashParameter("tab", String(tabActiveIndex)))

//#endregion Control View type

const displayResponseCode = (responseCode: string, message: string) => {
  return responseCode + " - " + message
}

const convertStatus = (responseCode: string) => {
  if (responseCode === "approved") {
    return "common.payment-link-transaction-status.Successful"
  } else if (responseCode != "") {
    return "common.payment-link-transaction-status.Failed"
  }
  return "common.payment-link-transaction-status.Processing"
}

const getFullUrl = (routerUri: string, host?: string) => {
  host = host ?? APPCONFIG.APP_PL_HOST
  if (host.endsWith("/")) host = host.substring(0, host.length - 2)

  routerUri = routerUri.replace("\\", "/")
  if (!routerUri.startsWith("/")) routerUri = `/${routerUri}`

  return `${host}${routerUri}`
}

watch(selectedItem, (selectedItem) => {
  const numberRefundAwaiMerchainApproval = dataRefund.value.filter(
    (item) => item.status === EnumTransactionStatus.AwaitMerchantApproval
  ).length
  if (numberRefundAwaiMerchainApproval > 0) {
    if (selectedItem.length === numberRefundAwaiMerchainApproval) {
      isCheckAllRefund.value = true
    } else {
      isCheckAllRefund.value = false
    }
  }
})
</script>

<template>
  <div class="page-Transaction-Management panel">
    <div class="panel-header">
      <div class="panel-header-title">
        <div class="page-title">{{ $t("page-Transaction-Management.page-title") }}</div>
        <div class="mx-auto"></div>
        <div class="group-button">
          <Button 
            v-tooltip.bottom="{
              value: !hasPermission(PermissionEnum.DoApproveRefund) ? $t('common.tooltip.action-not-permission') : '',
            }" 
            :class="{ 'no-permission': !hasPermission(PermissionEnum.DoApproveRefund) }"
            :disabled="!hasPermission(PermissionEnum.DoApproveRefund)" 
            severity="reject"
             
            class="btn-approve" 
            @click="openReject"
          >
            <SvgIcon class="mr-1" :src="DownloadIcon" />
            {{ $t("page-Transaction-Management.button-download") }}
          </Button>
        </div>
      </div>

    </div>
    <div class="panel-body">
      <div class="panel-body-data-table">
        <div class="all-trans">
          <AppDataTable ref="refTableAllTrans" v-model:query="dataFilters.dataTableQuery"
            class="datatable-AllTrans table-nowrap" :value="dataAllTrans" :total-records="allTransTotal"
            :loading="fetchDataAllTransTask.isRunning" scrollable scroll-height="548px" table-style="min-width: 50rem"
            @query-change="onDataTableQueryChange" @row-click="onDataTableRowClick">
            <div v-for="col of columnDisplay" :key="col.value">
              <Column v-if="col.field === 'trans-ref'" :header="formatCololumnTable(col.field)" :hidden="!col.isDisplay"
                header-class="table-column">
                <template #body="slotProps">
                  <div v-tooltip.bottom="{ value: slotProps.data.transRef }" class="d-flex flex-row">
                    <div class="flex-fill text-truncate" style="width: 116px">{{ slotProps.data.transRef }}</div>
                  </div>
                </template>
              </Column>

              <Column v-if="col.field === 'order-ref'" :header="formatCololumnTable(col.field)" :hidden="!col.isDisplay"
                header-style="width: 200px" header-class="table-column">
                <template #body="slotProps">
                  <div v-tooltip.bottom="{ value: slotProps.data.orderInfo }" class="d-flex flex-row">
                    <div class="flex-fill text-truncate" style="width: 124px">
                      {{ slotProps.data.orderInfo }}
                    </div>
                  </div>
                </template>
              </Column>

              <Column v-if="col.field === 'note'" :header="formatCololumnTable(col.field)" :hidden="!col.isDisplay"
                header-style="width: 126px" header-class="table-column">
                <template #body="slotProps">
                  <div v-tooltip.bottom="{ value: slotProps.data.note }" class="d-flex flex-row">
                    <div class="flex-fill text-truncate" style="width: 110px">{{ slotProps.data.note }}</div>
                  </div>
                </template>
              </Column>

              <!-- Column-Money(9,999,999,999.99 USD): Content:136px + padding:16px*2 = max-width:168px -> min-width: 128px -->
              <Column v-if="col.field === 'amount'" :header="formatCololumnTable(col.field)" header-style="width: 128px"
                header-class="table-column" class="text-right">
                <template #body="slotProps">
                  <div :data-currency="slotProps.data.currency">
                    {{ formatCurrency(slotProps.data.amount, slotProps.data.currency, { valueIfZero: "-" }) }}
                  </div>
                </template>
              </Column>



              <!-- Column-DateTime: Content:128px + padding:16px*2 = width:160px -->
              <Column v-if="col.field === 'payment-date'" :header="formatCololumnTable(col.field)"
                :hidden="!col.isDisplay" header-class="table-column">
                <template #body="slotProps">
                  <div :data-timezone="formatTimeZone(slotProps.data.paymentDate)">
                    {{ formatDateTime(slotProps.data.paymentDate, "-") }}
                  </div>
                </template>
              </Column>

              <Column v-if="col.field === 'card-type'" :header="formatCololumnTable(col.field)" :hidden="!col.isDisplay"
                header-style="width: 120px;white-space: nowrap" header-class="table-column">
                <template #body="slotProps">
                  <div v-tooltip.bottom="{ value: slotProps.data.cardType }" class="d-flex flex-row">
                    <div class="flex-fill text-truncate" style="width: 104px">
                      {{ slotProps.data.cardType }}
                    </div>
                  </div>
                </template>
              </Column>

              <Column v-if="col.field === 'card-number'" :header="formatCololumnTable(col.field)"
                :hidden="!col.isDisplay" header-class="table-column">
                <template #body="slotProps">
                  <div v-tooltip.bottom="{ value: slotProps.data.cardNumber }" class="d-flex flex-row">
                    <div class="flex-fill text-truncate" style="width: 162px">
                      {{ slotProps.data.cardNumber }}
                    </div>
                  </div>
                </template>
              </Column>

              <Column v-if="col.field === 'operator'" :header="formatCololumnTable(col.field)" :hidden="!col.isDisplay"
                header-class="table-column">
                <template #body="slotProps">
                  <div v-tooltip.bottom="{ value: slotProps.data.operator }" class="d-flex flex-row">
                    <div class="flex-fill text-truncate" style="width: 162px">{{ slotProps.data.operator }}</div>
                  </div>
                </template>
              </Column>

              <Column v-if="col.field === 'status'" :header="formatCololumnTable(col.field)" :hidden="!col.isDisplay"
                header-class="table-column">
                <template #body="slotProps">
                  <div class="d-flex flex-row">
                    <div class="flex-fill text-truncate" 
                         :class="{
                           'text-approved': slotProps.data.status === 'approved',
                           'text-rejected': slotProps.data.status !== 'approved' && slotProps.data.status !== '',
                           'text-pending': slotProps.data.status === ''
                         }"
                         style="width: 162px">{{ $t(convertStatus(slotProps.data.status)) }}</div>
                  </div>
                </template>
              </Column>

              <Column v-if="col.field === 'response-code'" :header="formatCololumnTable(col.field)"
                :hidden="!col.isDisplay" header-class="table-column">
                <template #body="slotProps">
                  <div v-tooltip.bottom="{ value: slotProps.data.responseCode + ' - ' + slotProps.data.message }"
                    class="d-flex flex-row">
                    <div class="flex-fill text-truncate" style="width: 128px">
                      {{ displayResponseCode(slotProps.data.responseCode, slotProps.data.message) }}
                    </div>
                  </div>
                </template>
              </Column>


            </div>
          </AppDataTable>
        </div>

      </div>
    </div>
  </div>

  <RouterView v-slot="{ Component, route }">
    <component :is="Component" :key="route.fullPath"></component>
  </RouterView>

  <Dialog v-model:visible="visibleAction" :draggable="false" modal :header="itemAction.title" :closable="false"
    class="modal-action-trans">
    <Teleport to="body">
      <div class="transaction-managerment-actions-loading">
        <AppLoading v-if="approveRejectTask.isRunning"></AppLoading>
      </div>
    </Teleport>

    <div v-if="itemAction.isApprove">
      <span>
        {{ $t("page-Transaction-Management.refund.actions-message.approve", { number: selectedItem.length }) }}
      </span>
    </div>

    <div v-else-if="itemAction.isReject">
      <span>
        {{ $t($t("page-Transaction-Management.refund.actions-message.reject", { number: selectedItem.length })) }}
      </span>
    </div>

    <template #footer>
      <Button type="button" :label="$t('common.buttons.cancel')" class="btn-cancel-trans-list"
        @click="closeModalAction"></Button>
      <Button v-tooltip.bottom="{
          value: !hasPermission(PermissionEnum.DoApproveRefund) ? $t('common.tooltip.action-not-permission') : '',
        }" :class="{
          'no-permission': !hasPermission(PermissionEnum.DoApproveRefund),
        }" :loading="approveRejectTask.isRunning" :disabled="!hasPermission(PermissionEnum.DoApproveRefund)"
        type="button" :label="itemAction.textAction" class="btn-action-trans-list"
        @click="applyAction(itemAction)"></Button>
    </template>
  </Dialog>

  <Lazy>
    <SettingTableColumnComponent v-model:model-column="columnDisplay" :visible="visible" @close-dialog="closeDialog">
    </SettingTableColumnComponent>

    <!-- <TransactionDetail v-model:visible="isShowDetailDialog" :transaction-id="trnasId" @hide-detail="hideDetail"></TransactionDetail> -->

    <Dialog v-model:visible="isShowExportFileDialog" class="dialog-download-export-transaction" modal
      :header="$t('page-Transaction-Management.export-file.title')">
      <!-- Tạm bỏ theo yc của TA do chức năng quản lý file đang download mình chưa code -->
      <!-- <div class="dialog-download-export-transaction-text">
        {{ $t("page-Transaction-Management.export-file.running-export") }}
      </div> -->
      <div class="dialog-download-export-transaction-progress">
        <ProgressBar mode="indeterminate"></ProgressBar>
      </div>
    </Dialog>
  </Lazy>
</template>

<style lang="scss">
.page-Transaction-Management {
  display: flex;
  flex-direction: column;
  height: 100%;
  margin-top: 10px;
  border-radius: 8px;
  background-color: #FFF;

  .text-approved {
    color: #00BF5F;
  }

  .text-rejected {
    color: #D82039;
  }

  .text-pending {
    color: #F38713;
  }

  .panel-header {
    border-bottom: 1px solid #e6e6e6;

    .panel-header-title {
      padding: 20px 24px 0 24px;
      display: flex;
      margin-bottom: 16px;

      .page-title {
        height: 2.5rem;
      }
    }

    .panel-header-body {
      .p-tabview {
        .p-tabview-nav-container {
          .p-tabview-nav {
            height: 27px;

            .p-tabview-header {
              .p-tabview-nav-link {
                padding: 0;
                border: none;

                .p-tabview-title {
                  font-weight: 600;
                  font-size: 14px;
                  line-height: 20px;
                  color: #949494;
                }
              }

              &:first-child {
                margin-left: 24px;
                margin-right: 24px;
                margin-bottom: 6px;
              }
            }

            .p-highlight {
              .p-tabview-title {
                color: #2e6be5 !important;
              }
            }

            .p-tabview-ink-bar {
              height: 2px !important;
              background-color: #2e6be5;
            }
          }
        }

        .p-tabview-panels {
          .p-tabview-panel {
            &:first-child {
              display: flex;
              justify-content: space-between;
              align-items: center;
            }

            &:nth-child(2) {
              display: flex;
              justify-content: space-between;
              align-items: center;
            }
          }
        }
      }
    }

    .more-filter {
      background-color: #fff;
      color: #404040;
      min-width: 100px;
      height: 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0;
    }

    // .clear-filter {
    //   background-color: #FFF;
    //   color: #404040;
    //   min-width: 100px;
    //   height: 40px;
    //   display: flex;
    //   justify-content: space-between;
    //   align-items: center;
    //   padding: 0;
    // }

    // .btn-icon {
    //   width: 36px;
    //   height: 36px;
    //   padding: 8px;
    //   border-radius: 4px;
    //   background-color: #F5F5F5;
    //   border: none;

    //   &:first-child {
    //     margin-right: 16px;
    //   }
    // }

    .group-button {
      .btn-reject {
        background-color: #f5f5f5;
        border: none;
        color: #404040;
        font-weight: 600;
        margin-right: 16px;
      }

      .btn-approve {
        color:  #2E6BE5;
        text-align: center;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px; /* 142.857% */
        font-weight: 600;
        border-radius: var(--layout-radius-radius-xxs, 4px);
        background: var(--surface-ghost-hover, #F5F5F5);
      }
    }
  }

  .panel-body {
    padding-top: 16px;
    padding-left: 24px;
    padding-right: 24px;

    &-stats {
      display: flex;

      .trans-number {
        margin-right: 24px;
      }

      .label {
        margin-right: 8px;
      }

      .result {
        font-weight: 600;
        color: #1c1c1c;
      }
    }

    &-data-table {
      .table-column {
        padding: 6px 16px;
      }

      .header-column-checkbox {
        padding: 2px 0 2px 16px;
      }

      // .column-check-box {

      // }

      .p-checkbox-box {
        width: 18px;
        height: 18px;
        border: 2px solid #6c6c6c;
        border-radius: 2px;
      }
    }
  }

  .panel-filter {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    gap: 0rem;
    width: 100%;

    .filter-item {
      display: flex;
      flex-direction: column;
      justify-content: center;

      .filter-item-header {
        display: inline-flex;
        align-items: center;
        gap: var(--layout-spacing-spacing-xxs, 4px);

        color: var(--text-tertiary, #6c6c6c);
        /* Inter/B3/12_Regular */
        font-size: var(--Typeface-size-sm, 0.75rem);
        font-style: normal;
        font-weight: 400;
        line-height: 1rem; /* 133.333% */

        > svg {
          color: inherit;
          stroke: currentColor;
          transition: transform 0.3s;
        }
      }

      &.show-menu {
        .filter-item-header,
        .filter-item-button {
          color: var(--text-info, #2e6be5);
        }
        .filter-item-header {
          > svg {
            transform: rotate(180deg);
          }
        }
      }
    }

    .filter-actions {
      display: inline-flex;
      gap: 1rem;

      color: #2e6be5;
    }

    .filter-item--keywords {
      width: 100%;
      max-width: 15.625rem;
      @media (min-width: 1441px) {
        max-width: 20rem;
      }
    }
  }

  .btn-clearfilter,
  .btn-morefilter {
    margin-left: -1rem;
    margin-right: -1rem;
  }
}

.text-transaction-await-transaction-result {
  color: #f38713;

  .p-tooltip-text {
    color: #f38713;
  }
}

.text-transaction-successful {
  color: #00bf5f;

  .p-tooltip-text {
    color: #00bf5f;
  }
}

.text-transaction-failed {
  color: #d82039;

  .p-tooltip-text {
    color: #d82039;
  }
}

.text-transaction-processing {
  color: #f38713;

  .p-tooltip-text {
    color: #f38713;
  }
}

.text-transaction-await-merchant-approval {
  color: #f38713;

  .p-tooltip-text {
    color: #f38713;
  }
}

.text-transaction-merchant-rejected {
  color: #d82039;

  .p-tooltip-text {
    color: #d82039;
  }
}

.text-transaction-await-onepay-approvval {
  color: #f38713;

  .p-tooltip-text {
    color: #f38713;
  }
}

.text-transaction-onepay-rejected {
  color: #d82039;

  .p-tooltip-text {
    color: #d82039;
  }
}

.text-transaction-merchant-approved {
  color: #00bf5f;

  .p-tooltip-text {
    color: #00bf5f;
  }
}

.text-transaction-onepay-approved {
  color: #00bf5f;

  .p-tooltip-text {
    color: #00bf5f;
  }
}

.datatable-Refund {
  .p-column-title {
    width: 100%;
  }
}

.dialog-download-export-transaction {
  width: 30rem;

  .dialog-download-export-transaction-text {
    font-size: var(--Typeface-size-md, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 1.25rem; /* 142.857% */
  }
  .dialog-download-export-transaction-progress {
    margin-top: 1.5rem;

    .p-progressbar {
      height: 0.5rem;
    }
  }
}

.btn-cancel-trans-list {
  min-width: 208px;
  height: 48px;
  padding: 12px 24px;
  border-radius: 8px;
  background-color: #f5f5f5;
  border: none;
  color: #404040;
  font-weight: 700;
  text-align: center;
}

.btn-action-trans-list {
  min-width: 208px;
  height: 48px;
  padding: 12px 24px;
  border-radius: 8px;
  background-color: #2e6be5;
  border: none;
  color: #ffffff;
  font-weight: 700;
  text-align: center;
}

.btn-action-trans-list-disabled {
  background-color: #e6e6e6 !important;
  color: #bababa !important;
  pointer-events: none;
}

.transaction-managerment-actions-loading {
  .component-loading {
    position: fixed;
    top: 0;
    z-index: 9999;
    background-color: black;
    opacity: 0.75;
  }
}
</style>
