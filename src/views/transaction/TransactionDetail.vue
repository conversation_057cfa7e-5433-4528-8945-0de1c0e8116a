<script setup lang="ts">
import InfoIcon from "@assets/icons/info.svg"
import RefundHistoryIcon from "@/assets/icons/icon-refund-history.svg"

import { handleApiError } from "@/apis/https/httpApi"
import {
  ApproveRejectTransactionRequest,
  BnplRequestRefund,
  BnplRequestVoid,
  CaptureRequest,
  DetailTransaction_PaymentlinkModel,
  GetListTranItem,
  RefundRequest,
  TransactionAction,
  TransactionDetailMaApp,
  TransactionHistory,
  VoidRequest,
} from "@/apis/transactionApi"
import { EnumTransactionPaymentMethod } from "@/app.const"
import { PermissionEnum } from "@/enums/permissions"
import { AppRouteNames } from "@/enums/routers"
import { useAuthService } from "@/services/authService"
import { useToastService } from "@/services/toastService"
import { useTransactionService } from "@/services/transactionService"
import { safeExecuteAsync } from "@/utils/exceptionUtil"
import { formatCurrency, formatDateTime, formatExchangeRate, formatNumber } from "@/utils/formatUtil"
import { useDebounceFn } from "@vueuse/core"
import { isAxiosError } from "axios"
import { DataTableRowClickEvent } from "primevue/datatable"
import { computed, ref } from "vue"
import { useAsyncTask } from "vue-concurrency"
import { useI18n } from "vue-i18n"
import { useRoute, useRouter } from "vue-router"
import { generateUUid } from "@/utils/uuidUtil"
import OverlayPanel from "primevue/overlaypanel"

const emit = defineEmits(["hideDetail"])

const route = useRoute()
const router = useRouter()

const {
  getDetailTask,
  formatTranStatus,
  formatTranType,
  refundTransactionTask,
  voidTransactionTask,
  captureTransactionTask,
  approveRejectTask,
  formatToastVoidAction,
} = useTransactionService()
const { hasPermission } = useAuthService()
const i18n = useI18n()
const { t } = useI18n()

/**
 * To visible Approve/Reject/Void/Capture/Refund dialog
 */
const visibleAction = ref(false)
const toastService = useToastService()

const opPaymentAmountPanel = ref<InstanceType<typeof OverlayPanel> | null>(null)
const onClickTogglePaymentAmount = (event: Event) => {
  const elm = opPaymentAmountPanel.value
  // trường hợp ở đây do đặt trong for nên elm sẽ có thể là mảng
  if (Array.isArray(elm) && elm.length > 0) {
    elm[0]?.toggle(event)
  } else {
    elm?.toggle(event)
  }
}

const props = withDefaults(
  defineProps<{
    transactionId: string | undefined
    transactionIds: string[] | undefined
  }>(),
  {}
)

const [visibleModel, visibleModelModifiers] = defineModel<boolean>("visible", {
  default: false,
})
// Lấy giá trị cuối cùng trong chuỗi route params để xử lý
const currentTransactionId = computed(
  () => props.transactionId || (props.transactionIds && props.transactionIds.lastOrDefault())
)
const selectedTranHistoryDataTableRow = computed(() =>
  modelTranHistory.value?.firstOrDefault((o) => currentTransactionId.value == o.transactionId)
)

const modelPaymentlink = ref<DetailTransaction_PaymentlinkModel>()
const modelTran = ref<GetListTranItem>()
const modelTranDetail = ref<TransactionDetailMaApp>()
const modelTranHistory = ref<TransactionHistory[]>()

const isProcessOpSyncData = ref<boolean>(false)

const availableToRefund = ref<number>(0)
const availableToCapture = ref<number>(0)
const refundAmount = ref<number>(0)
const captureAmount = ref<number>(0)

const fetchDataTask = useAsyncTask(async (signal, id?: string) => {
  id ??= currentTransactionId.value

  if (!id) return

  const res = await safeExecuteAsync(async () => await getDetailTask.perform(id))

  // Kiểm trang trạng thài đang chờ đồng bộ OP
  isProcessOpSyncData.value = false
  if (!res.success) {
    const error = res.error
    if (isAxiosError(error)) {
      isProcessOpSyncData.value =
        error.status == 102 ||
        error.response?.status == 102 ||
        error.response?.data.status == 102 ||
        String(error.response?.data.type).toLocaleLowerCase() == "processing"
    }

    // default handle error
    handleApiError(error, () => {
      // Bỏ qua show default lỗi nếu là đang chờ đồng bộ để hiển thị màn hình chờ
      if (isProcessOpSyncData.value == true) {
        return false
      }
    })

    return
  }

  const data = res.data

  if (
    data.transaction &&
    data.transaction_details.body.length > 2 &&
    (data.transaction.paymentMethod === EnumTransactionPaymentMethod.DomesticCard ||
      data.transaction.paymentMethod === EnumTransactionPaymentMethod.MobileApp ||
      data.transaction.paymentMethod === EnumTransactionPaymentMethod.BuyNowPayLater)
  ) {
    data.transaction_details.body.pop()
  }

  modelPaymentlink.value = data.paymentlink_model
  modelTran.value = data.transaction
  modelTranDetail.value = data.transaction_details
  modelTranHistory.value = data.transaction_histories

  if (modelTranDetail.value) {
    availableToRefund.value = modelTranDetail.value.refundData.amount
    availableToCapture.value = modelTranDetail.value.captureData.amount
  }
}).keepLatest()
const fetchData = useDebounceFn(async (id?: string) => await fetchDataTask.perform(id))

const onSidebarShow = async () => {
  await fetchData()
}

const onSidbarHide = async () => {
  emit("hideDetail")

  modelTranDetail.value = undefined
  modelTranHistory.value = undefined
  modelTran.value = undefined

  // Kiểm tra redirect back bằng params chuẩn
  if (route.params.linkId) {
    // Nếu màn được redirect từ LinkDetail -> Quay trở lại LinkDetail
    await router.push({
      name: AppRouteNames.PAYMENTLINK_DETAIL,
      params: {
        linkId: route.params.linkId,
      },
    })
    return
  }

  // Kiểm tra redirect back bằng previousRoute (WARN: Cách này dễ bị back lặp vô tận nếu không control tốt)
  // if (previousRoute.value?.name == AppRouteNames.TRANSACTIONMANAGEMENT_DETAIL) {await router.push(previousRoute.value)} -> chưa nên dùng
  const paramsTranIds = Array.isArray(route.params.tranId) ? [...route.params.tranId] : [route.params.tranId]
  if (paramsTranIds.length > 1) {
    // Nếu màn được redirect từ TransactionDetail -> Quay trở lại TransactionDetail
    paramsTranIds.pop()
    await router.push({
      name: AppRouteNames.TRANSACTIONMANAGEMENT_DETAIL,
      params: {
        tranId: paramsTranIds,
      },
    })
    return
  }

  // Redirect mặc định về Transaction List
  await router.push({
    name: AppRouteNames.TRANSACTIONMANAGEMENT_LIST,
  })
}

const checkHistory = ref<boolean>(false)

const openRefund = () => {
  if (!hasPermission(PermissionEnum.DoRefund)) {
    toastService.error({
      summary: t("common.toast.action-not-permission"),
    })
    return
  }

  const refundTran = {
    title: i18n.t("page-Transaction-Detail.dialog-actions.refund.title"),
    isVoid: false,
    isApprove: false,
    isReject: false,
    isCapture: false,
    isRefund: true,
    textAction: i18n.t("page-Transaction-Detail.dialog-actions.refund.actions.confirm"),
  }

  openModalAction(refundTran)
}

const openVoid = () => {
  if (!hasPermission(PermissionEnum.DoVoid)) {
    toastService.error({
      summary: t("common.toast.action-not-permission"),
    })
    return
  }

  const voidTran = {
    title: i18n.t("page-Transaction-Detail.dialog-actions.void.title"),
    isVoid: true,
    isApprove: false,
    isReject: false,
    isCapture: false,
    isRefund: false,
    textAction: i18n.t("page-Transaction-Detail.dialog-actions.void.actions.confirm"),
  }

  openModalAction(voidTran)
}

const openCapture = () => {
  if (!hasPermission(PermissionEnum.DoCapture)) {
    toastService.error({
      summary: t("common.toast.action-not-permission"),
    })
    return
  }

  const captureTran = {
    title: i18n.t("page-Transaction-Detail.dialog-actions.capture.title"),
    isVoid: false,
    isApprove: false,
    isReject: false,
    isCapture: true,
    isRefund: false,
    textAction: i18n.t("page-Transaction-Detail.dialog-actions.capture.actions.confirm"),
  }

  openModalAction(captureTran)
}

const openApprove = () => {
  if (!hasPermission(PermissionEnum.DoApproveRefund)) {
    toastService.error({
      summary: t("common.toast.action-not-permission"),
    })
    return
  }

  const approveTran = {
    title: i18n.t("page-Transaction-Detail.dialog-actions.approve.title"),
    isVoid: false,
    isApprove: true,
    isReject: false,
    isCapture: false,
    isRefund: false,
    textAction: i18n.t("page-Transaction-Detail.dialog-actions.approve.actions.confirm"),
  }

  openModalAction(approveTran)
}

const openReject = () => {
  if (!hasPermission(PermissionEnum.DoApproveRefund)) {
    toastService.error({
      summary: t("common.toast.action-not-permission"),
    })
    return
  }

  const rejectTran = {
    title: i18n.t("page-Transaction-Detail.dialog-actions.reject.title"),
    isVoid: false,
    isApprove: false,
    isReject: true,
    isCapture: false,
    isRefund: false,
    textAction: i18n.t("page-Transaction-Detail.dialog-actions.reject.actions.confirm"),
  }

  openModalAction(rejectTran)
}

const itemAction = ref<TransactionAction>({
  title: "",
  isVoid: false,
  isApprove: false,
  isReject: false,
  isCapture: false,
  isRefund: false,
  textAction: "",
})

const disabledRefundAction = computed(() => {
  return (
    itemAction.value.isRefund &&
    (!refundAmount.value || refundAmount.value === 0 || refundAmount.value > availableToRefund.value)
  )
})

const disableCaptureAction = computed(() => {
  return (
    itemAction.value.isCapture &&
    (!captureAmount.value || captureAmount.value === 0 || captureAmount.value > availableToCapture.value)
  )
})

const openModalAction = (item: TransactionAction) => {
  refundAmount.value = availableToRefund.value
  captureAmount.value = availableToCapture.value
  itemAction.value = item
  visibleAction.value = true
}

const closeModalAction = () => {
  visibleAction.value = false
}

const applyAction = async (action: TransactionAction) => {
  if (action.isRefund) {
    await refund()
  } else if (action.isVoid) {
    await voidAction()
  } else if (action.isCapture) {
    await capture()
  } else if (action.isApprove) {
    await approve()
  } else if (action.isReject) {
    await reject()
  }
}

const refund = async () => {
  const tranType = modelTranDetail.value ? modelTranDetail.value.service : ""

  const request: RefundRequest = {
    id: modelTran.value?.transactionId,
    op: "replace",
    path: "/refund",
    value: {
      merchant_id: modelTranDetail.value?.merchantId,
      amount: refundAmount.value,
      transaction_reference: generateUUid(),
      currency: modelTran.value?.currency,
      note: "",
    },
    skipCallSynchronize: false,
  }

  const bnblRequest: BnplRequestRefund = {
    transactionId: modelTran.value?.transID,
    merchantId: modelTranDetail.value?.merchantId,
    amount: refundAmount.value,
    note: "",
    canceledby: "",
    currency: modelTran.value?.currency,
    provider: modelTranDetail.value?.provider,
    refundable: true,
    settlementAmount: modelTranDetail.value?.settlementAmount,
    discount: modelTranDetail.value?.discount,
    merchantTransRef: generateUUid(),
  }

  await refundTransactionTask
    .perform(tranType, request, bnblRequest)
    .then(async (response) => {
      if (response && !response.error) {
        if (response.redirect_id) {
          const redirect_id = response.redirect_id
          await redirectToChildTransDetail(redirect_id)
        }

        toastService.success({ summary: i18n.t("page-Transaction-Detail.dialog-actions.refund.toast.success") })
      } else {
        toastService.error({ summary: i18n.t("page-Transaction-Detail.dialog-actions.refund.toast.erorr") })
      }
    })
    .catch((error) => {
      // eslint-disable-next-line no-console
      console.error(error)
    })

  if (!refundTransactionTask.isRunning) {
    visibleAction.value = false
    await fetchData()
  }
}

const voidAction = async () => {
  const tranType = modelTranDetail.value ? modelTranDetail.value.service : ""
  const request: VoidRequest = {
    id: modelTran.value?.transactionId,
    op: "replace",
    path: "",
    value: {
      merchant_id: modelTranDetail.value?.merchantId,
      amount: modelTranDetail.value?.header.amount,
      transaction_reference: generateUUid(),
      currency: modelTran.value?.currency,
      note: "",
    },
    skipCallSynchronize: false,
  }

  const bnblRequest: BnplRequestVoid = {
    transaction_id: modelTran.value?.transID,
    merchant_id: modelTranDetail.value?.merchantId,
    merchant_transaction_ref: generateUUid(),
    reason: "",
    note: "",
  }

  const transactionTypeMic = modelTran.value?.transType
  const toastMessage = formatToastVoidAction(transactionTypeMic ?? 0)

  await voidTransactionTask
    .perform(tranType, request, bnblRequest, transactionTypeMic)
    .then(async (response) => {
      if (response && !response.error) {
        if (response.redirect_id) {
          const redirect_id = response.redirect_id
          await redirectToChildTransDetail(redirect_id)
        }

        toastService.success({ summary: i18n.t(`page-Transaction-Detail.dialog-actions.void.toast.${toastMessage}`) })
      } else {
        toastService.error({ summary: i18n.t("page-Transaction-Detail.dialog-actions.void.toast.erorr") })
      }
    })
    .catch((error) => {
      // eslint-disable-next-line no-console
      console.error(error)
    })

  if (!voidTransactionTask.isRunning) {
    visibleAction.value = false
    await fetchData()
  }
}

const capture = async () => {
  const tranType = modelTranDetail.value ? modelTranDetail.value.service : ""
  const request: CaptureRequest = {
    id: modelTran.value?.transactionId,
    op: "replace",
    path: "/capture",
    value: {
      merchant_id: modelTranDetail.value?.merchantId,
      amount: captureAmount.value,
      transaction_reference: generateUUid(),
      currency: modelTran.value?.currency,
      note: "",
      order_info: null,
      cvv: null,
      user_name: "",
    },
    skipCallSynchronize: false,
  }

  await captureTransactionTask
    .perform(tranType, request)
    .then(async (response) => {
      if (response && !response.error) {
        if (response.map.redirect_id) {
          const redirect_id = response.map.redirect_id
          await redirectToChildTransDetail(redirect_id)
        }

        toastService.success({ summary: i18n.t("page-Transaction-Detail.dialog-actions.capture.toast.success") })
      } else {
        toastService.error({ summary: i18n.t("page-Transaction-Detail.dialog-actions.capture.toast.erorr") })
      }
    })
    .catch((error) => {
      // eslint-disable-next-line no-console
      console.error(error)
    })

  if (!captureTransactionTask.isRunning) {
    visibleAction.value = false
    await fetchData()
  }
}

const approve = async () => {
  const request: ApproveRejectTransactionRequest[] = [
    {
      id: modelTran.value?.transactionId,
      op: "replace",
      path: "/approve",
      service: modelTranDetail.value?.service,
      skipCallSynchronize: false,
      note: "",
      value: {
        merchant_id: modelTranDetail.value?.merchantId,
        transaction_reference: generateUUid(),
        currency: modelTran.value?.currency,
      },
    },
  ]

  await approveRejectTask
    .perform(request)
    .then((response) => {
      if (response && !response.errors) {
        toastService.success({ summary: i18n.t("page-Transaction-Detail.dialog-actions.approve.toast.success") })
      } else {
        toastService.error({ summary: i18n.t("page-Transaction-Detail.dialog-actions.approve.toast.erorr") })
      }
    })
    .catch((error) => {
      if (isAxiosError<{ errors: [] }>(error) && error.response?.data.errors) {
        console.error("approveRejectTask", request, error.response?.data.errors)
        toastService.error({ summary: t("page-Transaction-Detail.dialog-actions.approve.toast.erorr") })
        return
      }
      handleApiError(error)
    })

  if (!approveRejectTask.isRunning) {
    visibleAction.value = false
    await fetchData()
  }
}

const reject = async () => {
  const request: ApproveRejectTransactionRequest[] = [
    {
      id: modelTran.value?.transactionId,
      op: "replace",
      path: "/reject",
      service: modelTranDetail.value?.service,
      skipCallSynchronize: false,
      note: "",
      value: {
        merchant_id: modelTranDetail.value?.merchantId,
        transaction_reference: generateUUid(),
        currency: modelTran.value?.currency,
      },
    },
  ]

  await approveRejectTask
    .perform(request)
    .then((response) => {
      if (response && !response.errors) {
        toastService.success({ summary: i18n.t("page-Transaction-Detail.dialog-actions.reject.toast.success") })
      } else {
        toastService.error({ summary: i18n.t("page-Transaction-Detail.dialog-actions.reject.toast.erorr") })
      }
    })
    .catch((error) => {
      if (isAxiosError<{ errors: [] }>(error) && error.response?.data.errors) {
        console.error("approveRejectTask", request, error.response?.data.errors)
        toastService.error({ summary: t("page-Transaction-Detail.dialog-actions.approve.toast.erorr") })
        return
      }
      handleApiError(error)
    })

  if (!approveRejectTask.isRunning) {
    visibleAction.value = false
    await fetchData()
  }
}

const formatLinkName = computed(() => {
  return modelTran.value?.orderType === 1 ? t("page-Dashboard.mylink-title") : modelPaymentlink.value?.name
})

async function onTranHistoryDataTableRowClick(event: DataTableRowClickEvent) {
  const data = event.data as TransactionHistory
  const selectedTranId = data.transactionId

  await redirectToChildTransDetail(selectedTranId)
}

/**
 * redirect sang trang detail con.
 * @param tranId transaction detail id
 */
const redirectToChildTransDetail = async (tranId: uuid) => {
  const currentTranId = modelTran.value?.id || currentTransactionId.value || ""

  if (tranId != currentTranId) {
    let paramsTranIds = Array.isArray(route.params.tranId) ? [...route.params.tranId] : [route.params.tranId]
    //paramsTranIds.push(selectedTranId)
    // get last n elements from array
    // https://stackoverflow.com/questions/6473858/how-do-i-get-the-last-5-elements-excluding-the-first-element-from-an-array
    //paramsTranIds.update(paramsTranIds.slice(Math.max(paramsTranIds.length - 3, 0))) // last 3

    // Chỉ cho 2 trans /mainTranId/childTranId
    paramsTranIds = [paramsTranIds.firstOrDefault() ?? "", tranId]

    await router.push({
      name: AppRouteNames.TRANSACTIONMANAGEMENT_DETAIL,
      params: {
        tranId: paramsTranIds,
      },
    })
  }
}
</script>

<template>
  <AppSidebar
    v-model:visible="visibleModel"
    class="page-linkmanagement-detail-dialog"
    position="full"
    @show="onSidebarShow"
    @hide="onSidbarHide"
  >
    <template #header>
      <span class="header-title">{{ $t("page-Transaction-Detail.header.title") }}</span>

      <div v-if="!fetchDataTask?.isRunning && !fetchDataTask.isError && modelTranDetail" class="header-actions">
        <button
          v-if="modelTranDetail.canVoid"
          v-tooltip.bottom="{
            value: !hasPermission(PermissionEnum.DoVoid) ? $t('common.tooltip.action-not-permission') : '',
          }"
          :class="{ 'no-permission': !hasPermission(PermissionEnum.DoVoid) }"
          :disabled="!hasPermission(PermissionEnum.DoVoid)"
          type="button"
          class="btn-trans btn-void"
          @click="openVoid"
        >
          {{ $t("page-Transaction-Detail.header.actions.void") }}
        </button>

        <button v-if="checkHistory" type="button" class="btn-trans btn-secondary-icon btn-history">
          <SvgIcon :src="RefundHistoryIcon" />
        </button>

        <Button
          v-if="modelTranDetail.canRefund"
          v-tooltip.bottom="{
            value: !hasPermission(PermissionEnum.DoRefund) ? $t('common.tooltip.action-not-permission') : '',
          }"
          :class="{ 'no-permission': !hasPermission(PermissionEnum.DoRefund) }"
          :disabled="modelTranDetail.refundData.disable || !hasPermission(PermissionEnum.DoRefund)"
          type="button"
          class="btn-trans btn-refund"
          @click="openRefund"
        >
          {{ $t("page-Transaction-Detail.header.actions.refund") }}
        </Button>

        <button
          v-if="modelTranDetail.canCapture"
          v-tooltip.bottom="{
            value: !hasPermission(PermissionEnum.DoCapture) ? $t('common.tooltip.action-not-permission') : '',
          }"
          :class="{ 'no-permission': !hasPermission(PermissionEnum.DoCapture) }"
          :disabled="modelTranDetail.captureData.disable || !hasPermission(PermissionEnum.DoCapture)"
          type="button"
          class="btn-trans btn-capture"
          @click="openCapture"
        >
          {{ $t("page-Transaction-Detail.header.actions.capture") }}
        </button>

        <button
          v-if="modelTranDetail.canReject"
          v-tooltip.bottom="{
            value: !hasPermission(PermissionEnum.DoApproveRefund) ? $t('common.tooltip.action-not-permission') : '',
          }"
          :class="{ 'no-permission': !hasPermission(PermissionEnum.DoApproveRefund) }"
          :disabled="!hasPermission(PermissionEnum.DoApproveRefund)"
          type="button"
          class="btn-trans btn-reject"
          @click="openReject"
        >
          {{ $t("page-Transaction-Detail.header.actions.reject") }}
        </button>

        <button
          v-if="modelTranDetail.canApprove && !modelTranDetail.approveData.disable"
          v-tooltip.bottom="{
            value: !hasPermission(PermissionEnum.DoApproveRefund) ? $t('common.tooltip.action-not-permission') : '',
          }"
          :class="{ 'no-permission': !hasPermission(PermissionEnum.DoApproveRefund) }"
          :disabled="!hasPermission(PermissionEnum.DoApproveRefund)"
          type="button"
          class="btn-trans btn-approve"
          @click="openApprove"
        >
          {{ $t("page-Transaction-Detail.header.actions.approve") }}
        </button>
      </div>
    </template>

    <AsyncContent :loading="fetchDataTask.isRunning" class="asynccontent-wrapper">
      <div v-if="isProcessOpSyncData" class="container container-error--102">
        <Error102ProcessingComponent></Error102ProcessingComponent>
      </div>
      <div v-else-if="modelTran">
        <div class="container d-flex justify-content-between container--transaction-status">
          <div class="detail-status" :class="`detail-${formatTranStatus(modelTran?.status ?? 0).class}`">
            {{ formatTranStatus(modelTran?.status ?? 0).label }}
          </div>
        </div>

        <div class="container d-flex justify-content-between container--content-data gap-4">
          <div
            v-for="(transDetailMaApp, index) in modelTranDetail?.body"
            :key="index"
            class="card with-of-card"
            :class="`card--block-${index}`"
          >
            <div class="card-body item-content-list">
              <div v-for="(transDetailMaAppContent, key) in transDetailMaApp.content" :key="key" class="item-content">
                <div class="item-label">{{ transDetailMaAppContent.label }}</div>
                <div class="item-value text-truncate">
                  <img v-if="transDetailMaAppContent.iconPrefix" :src="transDetailMaAppContent.iconPrefix" alt="" />
                  {{ transDetailMaAppContent?.value }}
                </div>
              </div>

              <template v-if="index == 0 && modelTranDetail">
                <div class="item-content item-content--transactionDate">
                  <div class="item-label">{{ $t("page-Transaction-Detail.content.date") }}</div>
                  <div class="item-value">
                    {{ modelTranDetail.header.transactionDate }}
                  </div>
                </div>

                <div class="item-content item-content--orderRef">
                  <div class="item-label">{{ $t("page-Transaction-Detail.content.order-ref") }}</div>
                  <div class="item-value">
                    {{ modelTranDetail.header.orderInfo }}
                  </div>
                </div>

                <div class="item-content item-content--transactionState">
                  <div class="item-label">{{ $t("component-transaction-setting-column.content.trans-id") }}</div>
                  <div class="item-value">
                    {{ modelTran.transID }}
                  </div>
                </div>

                <div class="item-content item-content--orderAmount" v-if="modelPaymentlink">
                  <div class="item-label">{{ $t("page-Transaction-Detail.content.order-amount") }}</div>
                  <div class="item-value d-inline-flex gap-1 align-items-center">
                    {{ formatCurrency(modelPaymentlink.paymentAmount, modelPaymentlink.paymentCurrency) }}
                    <span
                      v-if="modelPaymentlink.currency != modelPaymentlink.paymentCurrency"
                      class="user-clickable d-inline-flex"
                      @click="onClickTogglePaymentAmount"
                    >
                      <SvgIcon :src="InfoIcon" />
                    </span>
                  </div>

                  <OverlayPanel
                    ref="opPaymentAmountPanel"
                    class="popover-PaymentAmountPanel"
                  >
                    <div class="item-content-list">
                      <div class="item-content item-content--totalAmount">
                        <div class="item-label">
                          {{ $t("page-Transaction-Detail.content.total-amount") }}
                        </div>
                        <div class="item-value">
                          {{ formatCurrency(modelPaymentlink.totalAmount, modelPaymentlink.currency) }}
                        </div>
                      </div>
                      <div class="item-content item-content--totalAmount">
                        <div class="item-label">
                          {{ $t("page-Transaction-Detail.content.exchange-rate") }}
                        </div>
                        <div class="item-value">
                          <!-- {{ 
                            //`1 ${modelPaymentlink.currency} = ${formatCurrency(modelPaymentlink.exchangeRate, modelPaymentlink.paymentCurrency)}`
                            formatExchangeRate(1, modelPaymentlink.currency, modelPaymentlink.exchangeRate, modelPaymentlink.paymentCurrency) 
                          }} -->
                          {{ formatNumber(modelPaymentlink.exchangeRate, 2) }}
                        </div>
                      </div>
                    </div>
                  </OverlayPanel>
                </div>

                <div class="item-content item-content--paymentAmount">
                  <div class="item-label">{{ $t("page-Transaction-Detail.content.payment-amount") }}</div>
                  <div class="item-value">
                    {{ formatCurrency(modelTranDetail.header.amount, modelTranDetail.header.currency) }}
                  </div>
                </div>

                <!-- <div class="item-content item-content--captureAmount">
                  <div class="item-label">{{ $t("page-Transaction-Detail.content.capture-amount") }}</div>
                  <div class="item-value">
                    {{ formatCurrency(modelTranDetail.captureData.amount, modelTranDetail.header.currency) }}
                  </div>
                </div> -->

                <!-- <div class="item-content item-content--refundAmount">
                  <div class="item-label">{{ $t("page-Transaction-Detail.content.refund-amount") }}</div>
                  <div class="item-value">
                    {{ formatCurrency(modelTranDetail.refundData.amount, modelTranDetail.header.currency) }}
                  </div>
                </div> -->

                <div class="item-content item-content--ResponseCode">
                  <div class="item-label">{{ $t("page-Transaction-Detail.content.res-code") }}</div>
                  <div class="item-value">
                    <template
                      v-if="modelTranDetail.header.responseCode?.code || modelTranDetail.header.responseCode?.response"
                    >
                      {{
                        `${modelTranDetail.header.responseCode.code}-${modelTranDetail.header.responseCode.response}`
                      }}
                      <!-- <span v-tooltip.right="modelTranDetail.header.responseCode.reason">
                        <SvgIcon :src="InfoIcon" :size="20"></SvgIcon>
                      </span> -->
                    </template>
                  </div>
                </div>

                <!-- <div class="item-content item-content--transactionState">
                  <div class="item-label">{{ $t("page-Transaction-Detail.content.trans-state") }}</div>
                  <div class="item-value" :style="{ color: modelTranDetail.header.transactionStatusColor?.text }">
                    {{ modelTranDetail.header.transactionAdvanceStatus }}
                  </div>
                </div> -->

                <!-- <div class="item-content">
                  <div class="item-label">Note</div>
                  <div class="item-value">
                    {{ model.header.responseCode.reason }}
                  </div>
                </div> -->
              </template>
            </div>
          </div>

          <div class="card with-of-card with-of-card--CustomerInformation card--block-eof">
            <div class="card-body item-content-list">
              <div class="item-content">
                <div class="item-label">{{ $t("page-Transaction-Detail.content.customer-name") }}</div>
                <div v-tooltip.bottom="modelTran?.customerName" class="item-value text-truncate">
                  {{ modelTran?.customerName }}
                </div>
              </div>

              <div class="item-content">
                <div class="item-label">{{ $t("page-Transaction-Detail.content.customer-email") }}</div>
                <div v-tooltip.bottom="modelTran?.email" class="item-value text-truncate">{{ modelTran?.email }}</div>
              </div>

              <div class="item-content">
                <div class="item-label">{{ $t("page-Transaction-Detail.content.customer-phone") }}</div>
                <div v-tooltip.bottom="modelTran?.phoneNumber" class="item-value text-truncate">
                  {{ modelTran?.phoneNumber }}
                </div>
              </div>

              <div class="item-content">
                <div class="item-label">{{ $t("page-Transaction-Detail.content.customer-address") }}</div>
                <div v-tooltip.bottom="modelTran?.address" class="item-value text-truncate">
                  {{ modelTran?.address }}
                </div>
              </div>

              <div class="item-content">
                <div class="item-label">{{ $t("common.payment-link-options.labels.notes") }}</div>
                <div v-tooltip.bottom="modelTran?.customerNote" class="item-value text-truncate">
                  {{ modelTran.customerNote || $t("common.payment-link-options.labels.notes") }}
                </div>
              </div>

              <div class="item-content">
                <div class="item-label">{{ $t("page-Transaction-Detail.content.merchant-name") }}</div>
                <div class="item-value text-truncate">{{ modelPaymentlink?.merchantName }}</div>
              </div>

              <div class="item-content">
                <div class="item-label">{{ $t("page-Transaction-Detail.content.link-name") }}</div>
                <div v-tooltip.bottom="formatLinkName" class="item-value text-truncate">
                  {{ formatLinkName }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="container d-flex justify-content-center container--transaction-history">
          <div class="card">
            <div class="card-body">
              <div class="card-title">{{ $t("page-Transaction-Detail.history.title") }}</div>

              <AppDataTable
                selection-mode="single"
                data-key="transactionId"
                :selection="[selectedTranHistoryDataTableRow]"
                :paginator="false"
                :value="modelTranHistory"
                @row-click="onTranHistoryDataTableRowClick"
              >
                <Column field="transID" :header="$t('page-Transaction-Detail.history.column-header.trans-id')">
                  <template #body="slotProps">
                    <span>
                      {{ slotProps.data.transID }}
                    </span>
                  </template>
                </Column>

                <Column field="transDate" :header="$t('page-Transaction-Detail.history.column-header.date')">
                  <template #body="slotProps">
                    <span>
                      {{ formatDateTime(slotProps.data.transDate) }}
                    </span>
                  </template>
                </Column>

                <Column field="transType" :header="$t('page-Transaction-Detail.history.column-header.tran-type')">
                  <template #body="slotProps">
                    <span>
                      {{ formatTranType(slotProps.data.transType) }}
                    </span>
                  </template>
                </Column>

                <Column
                  :header="$t('page-Transaction-Detail.history.column-header.amount')"
                  header-style="text-align: right;"
                  class="text-right"
                >
                  <template #body="slotProps">
                    <div class="d-flex flex-row">
                      <span class="flex-grow-1 text-truncate" style="width: 120px">
                        {{ formatCurrency(slotProps.data.amount, slotProps.data.currency) }}
                      </span>
                    </div>
                  </template>
                </Column>

                <Column field="status" :header="$t('page-Transaction-Detail.history.column-header.status')">
                  <template #body="slotProps">
                    <span
                      class="flex-grow-1 text-truncate"
                      :class="formatTranStatus(slotProps.data.status).class"
                      style="width: 81px"
                    >
                      {{ formatTranStatus(slotProps.data.status).label }}
                    </span>
                  </template>
                </Column>

                <!-- <Column field="description" :header="$t('page-Transaction-Detail.history.column-header.description')">
                  <template #body="slotProps">
                    <span>
                      {{ `${slotProps.data.responseCode} - ${slotProps.data.message}` }}
                    </span>
                  </template>
                </Column> -->
              </AppDataTable>
            </div>
          </div>
        </div>

        <Dialog v-model:visible="visibleAction" :draggable="false" modal class="modal-action-trans">
          <template #header>
            <div class="trans-action-header">
              {{ itemAction.title }}
            </div>
          </template>

          <Teleport to="body">
            <div class="transaction-actions-loading">
              <AppLoading
                v-if="
                  voidTransactionTask.isRunning ||
                  approveRejectTask.isRunning ||
                  captureTransactionTask.isRunning ||
                  refundTransactionTask.isRunning
                "
              ></AppLoading>
            </div>
          </Teleport>

          <div v-if="itemAction.isVoid">
            <span>{{ $t("page-Transaction-Detail.dialog-actions.void.message") }}</span>
          </div>

          <div v-else-if="itemAction.isApprove">
            <span>{{ $t("page-Transaction-Detail.dialog-actions.approve.message") }}</span>
          </div>

          <div v-else-if="itemAction.isReject">
            <span>{{ $t("page-Transaction-Detail.dialog-actions.reject.message") }}</span>
          </div>

          <div v-else-if="itemAction.isCapture" class="action-capture">
            <div class="available-to-capture">
              <div class="merchain-currency">
                <span>{{ $t("page-Transaction-Detail.dialog-actions.capture.available-to-capture") }}</span>
                <span>{{ formatCurrency(availableToCapture, modelTran?.currency) }}</span>
              </div>
            </div>

            <div class="capture-amount">
              <div class="capture-value">
                <span>{{ $t("page-Transaction-Detail.dialog-actions.capture.capture-amount") }}</span>

                <!-- <InputNumberNew
                  v-if="modelTran.currency === 'VND'"
                  v-model="captureAmount"
                  :suffix="` ${modelTran?.currency}`"
                  :is-integer="true"
                  locale="en-US"
                /> -->

                <!-- <InputNumberNew
                  v-else
                  v-model="captureAmount"
                  :suffix="` ${modelTran?.currency}`"
                  :is-integer="false"
                  locale="en-US"
                /> -->

                <InputAmount
                  v-model:model-value="captureAmount"
                  class="capture-input"
                  name="tranDetail-captureAmount"
                  mode="currency"
                  :currency="modelTran?.currency"
                  :suffix="`${modelTran?.currency}`"
                />
              </div>
            </div>
          </div>

          <div v-else-if="itemAction.isRefund" class="action-refund">
            <div class="available-to-refund">
              <div class="merchain-currency">
                <span>{{ $t("page-Transaction-Detail.dialog-actions.refund.available-to-refund") }}</span>
                <span>{{ formatCurrency(availableToRefund, modelTran?.currency) }}</span>
              </div>
            </div>

            <div class="refund-amount">
              <div class="refund-value">
                <span>{{ $t("page-Transaction-Detail.dialog-actions.refund.refund-amount") }}</span>

                <!-- <InputNumberNew
                  v-if="modelTran.currency === 'VND'"
                  v-model="refundAmount"
                  :suffix="` ${modelTran?.currency}`"
                  :is-integer="true"
                  locale="en-US"
                /> -->

                <!-- <InputNumberNew
                  v-else
                  v-model="refundAmount"
                  :suffix="` ${modelTran?.currency}`"
                  :is-integer="false"
                  locale="en-US"
                /> -->

                <InputAmount
                  v-model:model-value="refundAmount"
                  class="refund-input"
                  name="tranDetail-refundAmount"
                  mode="currency"
                  :currency="modelTran?.currency"
                  :suffix="`${modelTran?.currency}`"
                />
              </div>
            </div>
          </div>

          <template #footer>
            <Button
              type="button"
              :label="$t('common.buttons.cancel')"
              class="btn-cancel"
              @click="closeModalAction"
            ></Button>
            <Button
              type="button"
              :label="itemAction.textAction"
              class="btn-action"
              :class="{ 'btn-action-disabled': disableCaptureAction || disabledRefundAction }"
              @click="applyAction(itemAction)"
            ></Button>
          </template>
        </Dialog>
      </div>
      <div v-else><!-- NO INFO --></div>
    </AsyncContent>
  </AppSidebar>
</template>

<style lang="scss">
.page-linkmanagement-detail-dialog {
  .p-sidebar-content {
    background-color: #f0f0f0;
    height: 100%;
    padding: 0;

    .container--transaction-status {
      padding-left: 0;
      padding-right: 0;
      margin-top: 16px;
      max-width: 1200px;
    }

    .container--content-data {
      padding-left: 0;
      padding-right: 0;
      margin-top: 16px;
      max-width: 1200px;
      flex-wrap: wrap;

      .card {
        .card-body {
          padding: 1.25rem 1.5rem;
        }

        .card-title {
          font-weight: 600;
          color: #1c1c1c;
        }
      }

      .item-content-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }

      .item-content {
        display: flex;
        align-items: center;
        height: 24px;

        .item-label {
          width: 260px;
          min-width: 260px;

          color: var(--text-tertiary, #6c6c6c);
          /* Inter/B2/14_Regular */
          font-size: var(--Typeface-size-md, 0.875rem);
          font-style: normal;
          font-weight: 400;
          line-height: 1.25rem; /* 142.857% */
        }

        .item-value {
          color: var(--text-body, #404040);
          /* Inter/B2/14_Medium */
          font-size: var(--Typeface-size-md, 0.875rem);
          font-style: normal;
          font-weight: 500;
          line-height: 1.25rem; /* 142.857% */
          width: 300px;
        }

        &.item-content--ResponseCode .item-value {
          display: flex;
          gap: 0.5rem;
          align-items: center;
        }
      }
    }

    .container--transaction-history {
      padding-left: 0;
      padding-right: 0;
      margin-top: 24px;
      margin-bottom: 33px;
      max-width: 1200px;

      .card {
        width: 100%;

        .card-title {
          font-weight: 600;
          margin-bottom: 16px;
        }
      }
    }
  }

  .with-of-card {
    max-width: 588px;
    width: 100%;
  }

  .btn-secondary {
    height: 48px;
    padding: 12px 24px;
    border-radius: 4px;
    background-color: #f5f5f5;
    border: none;
    color: #404040;
    font-weight: 700;
  }

  .btn-secondary-icon {
    width: 48px;
    height: 48px;
    padding: 12px;
    border: none;
    border-radius: 4px;
    background-color: #f5f5f5;
  }

  .header-title {
    font-weight: 600;
    font-size: 24px;
    line-height: 36px;
    color: #1c1c1c;

    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    padding: inherit;

    display: flex;
    justify-content: center;
    align-items: center;

    pointer-events: none;
  }

  .header-actions {
    display: flex;
    align-items: center;
  }

  .btn-trans {
    height: 48px;
    padding: 12px 24px;
    border-radius: 4px;
    border: none;
    color: #ffffff;
    font-weight: 700;

    &:not(:last-child) {
      margin-right: 16px;
    }

    &:disabled {
      background-color: #e6e6e6 !important;
      color: #bababa;
    }
  }

  .btn-reject {
    background-color: #e04d61;
    min-width: 98px;
  }

  .btn-approve {
    background-color: #2e6be5;
    min-width: 115px;
  }

  .btn-refund {
    background-color: #eb8612;
    min-width: 105px;
  }

  .btn-void {
    background-color: #6f57e9;
    min-width: 84px;
  }

  .btn-capture {
    background-color: #00ae56;
    min-width: 112px;
  }

  // .block-0 {
  //   margin-bottom: 24px;
  //   height: auto;
  // }

  // .block-1 {
  //   margin-bottom: 24px;
  //   height: auto;
  // }

  // .block-2 {
  //   height: auto;
  // }

  // .block-3 {
  //   height: auto;
  // }

  .detail-status {
    padding: 8px 16px;
    border-radius: 100px;
    border: 1px solid;
  }

  .detail-text-transaction-successful {
    border-color: #8ae2b5;
    background-color: #e6f9ef;
    color: #00bf5f;
  }

  .detail-text-transaction-merchant-approved {
    border-color: #8ae2b5;
    background-color: #e6f9ef;
    color: #00bf5f;
  }

  .detail-text-transaction-onepay-approved {
    border-color: #8ae2b5;
    background-color: #e6f9ef;
    color: #00bf5f;
  }

  .detail-text-transaction-failed {
    border-color: #ffeff0;
    background-color: #ed98a4;
    color: #d82039;
  }

  .detail-text-transaction-onepay-rejected {
    border-color: #ffeff0;
    background-color: #ed98a4;
    color: #d82039;
  }

  .detail-text-transaction-merchant-rejected {
    border-color: #ed98a4;
    background-color: #ffeff0;
    color: #d82039;
  }

  .detail-text-transaction-processing {
    border-color: #fef4ea;
    background-color: #fef4ea;
    color: #f38713;
  }

  .detail-text-transaction-incomplete {
    border-color: #fef4ea;
    background-color: #fef4ea;
    color: #f38713;
  }

  .detail-text-transaction-await-merchant-approval {
    border-color: #fef4ea;
    background-color: #fef4ea;
    color: #f38713;
  }

  .detail-text-transaction-await-onepay-approvval {
    border-color: #fef4ea;
    background-color: #fef4ea;
    color: #f38713;
  }

  .asynccontent-wrapper {
    padding: 5rem 1rem 1rem;
    > .card--error {
      flex: 1;
    }
  }

  .container-error--102 {
    margin-top: 5rem;
  }
}

.modal-action-trans {
  padding: 24px;
  width: 480px;

  .p-dialog-header {
    padding: 0;

    .p-dialog-header-icons {
      display: none !important;
    }

    .trans-action-header {
      font-weight: 600;
      font-size: 24px;
      line-height: 36px;
      color: #1c1c1c;
    }
  }

  .p-dialog-content {
    padding: 0;
    margin-top: 24px;

    .action-refund {
      .available-to-refund {
        padding: 10px 0;
        height: 40px;

        .merchain-currency {
          display: flex;
          justify-content: space-between;
        }
      }

      .refund-amount {
        margin-top: 16px;

        .refund-value {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .refund-input {
            width: 14rem;
            text-align: right;
          }
        }
      }
    }

    .action-capture {
      .available-to-capture {
        padding: 10px 0;
        height: 40px;

        .merchain-currency {
          display: flex;
          justify-content: space-between;
        }
      }

      .capture-amount {
        margin-top: 16px;

        .capture-value {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .capture-input {
            width: 14rem;
            text-align: right;
          }
        }
      }
    }
  }

  .p-dialog-footer {
    padding: 0;
    margin-top: 24px;
    display: flex;
    justify-content: space-between;

    .btn-cancel {
      min-width: 208px;
      height: 48px;
      padding: 12px 24px;
      border-radius: 8px;
      background-color: #f5f5f5;
      border: none;
      color: #404040;
      font-weight: 700;
      text-align: center;
    }

    .btn-action {
      min-width: 208px;
      height: 48px;
      padding: 12px 24px;
      border-radius: 8px;
      background-color: #2e6be5;
      border: none;
      color: #ffffff;
      font-weight: 700;
      text-align: center;
    }
  }
}

.transaction-actions-loading {
  .component-loading {
    position: fixed;
    top: 0;
    z-index: 9999;
    background-color: black;
    opacity: 0.75;
  }
}

.btn-action-disabled {
  background-color: #e6e6e6 !important;
  color: #bababa !important;
  pointer-events: none;
}

.popover-PaymentAmountPanel {
  .p-overlaypanel-content {
    padding: 1rem;
  }

  .item-content-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .item-content {
    display: flex;
    gap: 2.5rem;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    
    .item-label {
      //width: 7rem; // en thì cần 7rem, trong khi vi thì cần 4rem
      //flex: 1 1 auto; // dùng flex này thì sẽ căn 2 bên như justify-content: space-between

      color: var(--text-tertiary, #6c6c6c);
      /* Inter/B2/14_Regular */
      font-size: var(--Typeface-size-md, 0.875rem);
      font-style: normal;
      font-weight: 400;
      line-height: 1.25rem; /* 142.857% */
    }

    .item-value {
      //width: 10rem;

      color: var(--text-body, #404040);
      /* Inter/B2/14_Medium */
      font-size: var(--Typeface-size-md, 0.875rem);
      font-style: normal;
      font-weight: 500;
      line-height: 1.25rem; /* 142.857% */;
    }
  }
}
</style>
