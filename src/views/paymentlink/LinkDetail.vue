<script setup lang="ts">
import UploadFileIcon from "@/assets/icons/payment-link-upload-file-icon.svg"
import CheckIcon from "@assets/icons/check.svg"
import CloseIcon from "@assets/icons/close-currentColor.svg"
import CopyIcon from "@assets/icons/copy.svg"
import Calendar from "@assets/icons/dashboard-calendar.svg"
import DownloadQRCodeIcon from "@assets/icons/download-qrcode.svg"
import DownloadIcon from "@assets/icons/download.svg"
import EditIconDisabled from "@assets/icons/edit-pen-disabled.svg"
import EditIcon from "@assets/icons/edit-pen.svg"
import InfoIcon from "@assets/icons/info.svg"
import LinkIconDisabled from "@assets/icons/link-disabled.svg"
import LinkIcon from "@assets/icons/link.svg"
import MoreVerticalIcon from "@assets/icons/more-vertical.svg"

import { EnumExportType, InfoFieldOptions } from "@/app.const"
import APPCONFIG from "@/appConfig"
import { PermissionEnum } from "@/enums/permissions"
import { AppRouteNames } from "@/enums/routers"
import { EventCommandEnum, usePaymentLinkEventBus } from "@/events/eventBus"
import { useAuthService } from "@/services/authService"
import { useConfirmDialogService } from "@/services/confirmService"
import { usePaymentCommonService, type PaymentMethods } from "@/services/paymentCommonService"
import { useToastService } from "@/services/toastService"
import type { PresetAmountJson } from "@/types/apis"
import type { DateRangModel, IPagedRequest } from "@/types/base"
import { formatCurrency, formatDateTime, formatExchangeRate, formatNumber } from "@/utils/formatUtil"
import {
  usePaymentLinkService,
  type GetByIdPaymentLinkOutput,
  type GetListPaymentLinkTransItem,
} from "@services/paymentLinkService"
import { useClipboard, useDebounceFn, useToggle } from "@vueuse/core"
import moment from "moment"
import { DataTableRowClickEvent } from "primevue/datatable"
import type OverlayPanel from "primevue/overlaypanel"
import { ref, watch } from "vue"
import { useAsyncTask } from "vue-concurrency"
import { useI18n } from "vue-i18n"
import { useRouter } from "vue-router"

import type { DownloadQrDialogValueType } from "@/components/DownloadQrDialog.vue"
import { caculatePaymentAmount } from "@/services/paymentLinkService.utils"

const { t } = useI18n()
const router = useRouter()
const { copy } = useClipboard()
const toastService = useToastService()
const confirmService = useConfirmDialogService()
const { hasPermission } = useAuthService()
const eventBus = usePaymentLinkEventBus()
const {
  exportPaymentLinkTransTask,
  setEnableStatusTask,
  getByIdPaymentLinkTask,
  getListPaymentLinkTransTask,
  formatPaymentLinkStatus,
  formatPaymentLinkTransType,
  formatPaymentLinkTransStatus,
  getPaymentLinkFullRouterUri,
  formatLinkType,
} = usePaymentLinkService()

const { downloadTask } = usePaymentLinkService()

const paymentCommonService = usePaymentCommonService()

const props = withDefaults(
  defineProps<{
    linkId: uuid | undefined
    isPageView?: boolean
  }>(),
  {}
)

const emit = defineEmits<{
  // <eventName>: [<expected arguments>] // named tuple syntax
  close: [id: uuid]
  edit: [id: uuid]
  switchLinkEnabled: [id: uuid]
}>()

const [visibleModel, visibleModelModifiers] = defineModel<boolean>("visible", {
  default: false,
})

const paymentMethods = ref<PaymentMethods[]>([])

const formData = ref<{
  link?: GetByIdPaymentLinkOutput
}>({})

//#region link information

const fullRouterUri = ref<string>("")

watch(
  () => formData,
  (formData) => {
    const value = formData.value

    fullRouterUri.value = value.link?.routerUri ? getPaymentLinkFullRouterUri(value.link?.routerUri) : ""
  },
  {
    deep: true,
    immediate: true,
  }
)

async function copyLinkUri(input?: string) {
  const link = input ?? fullRouterUri.value

  console.log(`Copy '${link}'`)

  await copy(link)

  toastService.copySuccess({})
}

const refMoreOP = ref<InstanceType<typeof OverlayPanel> | null>(null)
const [isVisibledMoreOP, toggleVisibledMoreOP] = useToggle()

const [isShowDownloadQrDialog, toggleShowDownloadQrDialog] = useToggle()
const dataDownloadQrDialog = ref<DownloadQrDialogValueType>()

function openMoreActions(event: Event) {
  refMoreOP.value?.toggle(event)
}

function onClickDownloadQR() {
  if (!formData.value.link) return

  dataDownloadQrDialog.value = {
    name: formData.value.link.name,
    routerUri: formData.value.link.routerUri,
  }
  toggleShowDownloadQrDialog(true)
  toggleVisibledMoreOP(false)
}

async function onClickEditLink() {
  if (!hasPermission(PermissionEnum.EditLink)) {
    toastService.error({
      summary: t("common.toast.action-not-permission"),
    })
    return
  }

  if (!props.linkId) return

  //visibleModel.value = false
  //emit("edit", props.linkId)

  await router.push({
    name: AppRouteNames.PAYMENTLINK_EDIT,
    params: {
      linkId: props.linkId,
    },
  })
}

const onChangeLinkEnabledStatusTask = useAsyncTask(async (signal, input: void) => {
  const id = props.linkId ?? formData.value.link?.id

  if (!id) return
  if (getByIdPaymentLinkTask.isRunning) return
  if (setEnableStatusTask.isRunning) return

  const enabled = formData.value.link?.enabled ?? false

  if (enabled) {
    const resLinkDetailUpdated = await getByIdPaymentLinkTask.perform(id)
    const inprogressPayments = resLinkDetailUpdated.inprogressPayments

    if (inprogressPayments && inprogressPayments > 0) {
      const isConfirmed = await confirmService.confirm({
        header: t("page-LinkManagement-detail.confirms.change-link-enabled.header"),
        message: t("page-LinkManagement-detail.confirms.change-link-enabled.message", { sessions: inprogressPayments }),
      })

      if (!isConfirmed) return
    }
  }

  await setEnableStatusTask.perform({
    id: id,
    enabled: !enabled,
  })

  await fetchData()

  emit("switchLinkEnabled", id)
})
const onChangeLinkEnabledStatus = () => onChangeLinkEnabledStatusTask.perform()

//#endregion link information

//#region paymentAmount

const paymentAmount_enable = ref<boolean>(false)
const paymentAmount_amount = ref<number>()
const paymentAmount_currency = ref<string>()
const paymentAmount_totalAmount = ref<number>()
const paymentAmount_exchangeRate = ref<number>()
const paymentAmount_paymentAmount = ref<number>()
const paymentAmount_paymentCurrency = ref<string>()

const paymentAmount_enableDiscount = ref<boolean>(false)
const paymentAmount_discount = ref<number>()
const paymentAmount_discountIsPercent = ref<boolean>()
const paymentAmount_discountValue = ref<number>()

const paymentAmount_enableTax = ref<boolean>(false)
const paymentAmount_tax = ref<number>()
const paymentAmount_taxValue = ref<number>()

const paymentAmount_enableOtherFee = ref<boolean>(false)
const paymentAmount_otherFee = ref<number>()
const paymentAmount_otherFeeLabel = ref<string>()
const paymentAmount_otherFeeIsPercent = ref<boolean>()
const paymentAmount_otherFeeValue = ref<number>()

watch(
  formData,
  (formData) => {
    paymentAmount_enable.value = formData.link?.hasPresetAmount ?? false
    // Trường hợp không có presetAmountJson
    if (!formData.link?.presetAmountJson) {
      paymentAmount_enable.value = false
      paymentAmount_amount.value = undefined

      paymentAmount_enableDiscount.value = false
      paymentAmount_discount.value = undefined
      paymentAmount_discountIsPercent.value = undefined
      paymentAmount_discountValue.value = undefined

      paymentAmount_enableTax.value = false
      paymentAmount_tax.value = undefined
      paymentAmount_taxValue.value = undefined

      paymentAmount_enableOtherFee.value = false
      paymentAmount_otherFeeLabel.value = undefined
      paymentAmount_otherFee.value = undefined
      paymentAmount_otherFeeIsPercent.value = undefined
      paymentAmount_otherFeeValue.value = undefined
      return
    }

    const presetAmountJson = JSON.parse(formData.link.presetAmountJson) as PresetAmountJson
    const currency = formData.link.currency
    const totalAmount = formData.link.totalAmount
    const exchangeRate = formData.link.exchangeRate ?? 0
    const paymentAmount = formData.link.paymentAmount
    const paymentCurrency = formData.link.paymentCurrency

    // WARN: Công thức sau được lặp lại tương ứng ở
    // - MerchantWeb: LinkDetail + AddPaymentAmount + LinkPaymentAmount
    // - ClientWeb: paymentLinkViewStore + paymentLinkViewResultStore
    const caculateRes = caculatePaymentAmount({
      presetAmountJson: presetAmountJson,
      otherInfo: {
        currency: currency,
        exchangeRate: exchangeRate,
        paymentCurrency: paymentCurrency,
      },
    })

    // Chú ý: riêng màn Detail
    // Giá trị amount/totalAmount/paymentAmount được lấy từ DB để xem chuẩn data
    paymentAmount_amount.value = presetAmountJson?.amount ?? undefined
    paymentAmount_currency.value = currency
    paymentAmount_totalAmount.value = totalAmount
    paymentAmount_exchangeRate.value = exchangeRate
    paymentAmount_paymentAmount.value = paymentAmount
    paymentAmount_paymentCurrency.value = paymentCurrency

    paymentAmount_enableDiscount.value = caculateRes.enableDiscount
    paymentAmount_discount.value = caculateRes.discount
    paymentAmount_discountIsPercent.value = caculateRes.discountIsPercent
    paymentAmount_discountValue.value = caculateRes.discountResult

    paymentAmount_enableTax.value = caculateRes.enableTax
    paymentAmount_tax.value = caculateRes.tax
    paymentAmount_taxValue.value = caculateRes.taxResult

    paymentAmount_enableOtherFee.value = caculateRes.enableOtherFee
    paymentAmount_otherFeeLabel.value = caculateRes.otherFeeLabel
    paymentAmount_otherFee.value = caculateRes.otherFee
    paymentAmount_otherFeeIsPercent.value = caculateRes.otherFeeIsPercent
    paymentAmount_otherFeeValue.value = caculateRes.otherFeeResult
  },
  {
    deep: true,
    immediate: true,
  }
)

//#endregion paymentAmount

//#region paymentLimit

const paymentLimit_enable = ref<boolean>(false)
const paymentLimit_maxPaymentQty = ref<number | null>(null)
const paymentLimit_inprogressPayments = ref<number | null>(null)
const paymentLimit_totalPaymentQty = ref<number | null>(null)

watch(
  formData,
  (formData) => {
    paymentLimit_enable.value = formData.link?.hasLimitPayment ?? false
    if (paymentLimit_enable.value) {
      paymentLimit_maxPaymentQty.value = formData.link?.maxPaymentQty ?? 0
      paymentLimit_inprogressPayments.value = formData.link?.inprogressPayments ?? 0
      paymentLimit_totalPaymentQty.value = formData.link?.totalPaymentQty ?? 0
    } else {
      paymentLimit_maxPaymentQty.value = null
      paymentLimit_inprogressPayments.value = null
      paymentLimit_totalPaymentQty.value = null
    }
  },
  {
    deep: true,
    immediate: true,
  }
)

//#endregion paymentLimit

//#region options

const options_enableName = ref<boolean>(false)
const options_requireName = ref<boolean>(false)

const options_enablePhone = ref<boolean>(false)
const options_requirePhone = ref<boolean>(false)

const options_enableEmail = ref<boolean>(false)
const options_requireEmail = ref<boolean>(false)

const options_enableAddress = ref<boolean>(false)
const options_requireAddress = ref<boolean>(false)

const options_enableNotes = ref<boolean>(false)
const options_requireNotes = ref<boolean>(false)

const options_customerNotes = ref<
  {
    locale: string
    value: string
  }[]
>([])

watch(
  formData,
  (formData) => {
    const data = formData.link
    const infoFieldOptions = data?.infoFieldOptions ?? 0
    const customerNoteLckey = data?.customerNoteLckey ?? []

    options_enableName.value = (infoFieldOptions & InfoFieldOptions.enableName) > 0
    options_requireName.value = (infoFieldOptions & InfoFieldOptions.requireName) > 0

    options_enablePhone.value = (infoFieldOptions & InfoFieldOptions.enablePhone) > 0
    options_requirePhone.value = (infoFieldOptions & InfoFieldOptions.requirePhone) > 0

    options_enableEmail.value = (infoFieldOptions & InfoFieldOptions.enableEmail) > 0
    options_requireEmail.value = (infoFieldOptions & InfoFieldOptions.requireEmail) > 0

    options_enableAddress.value = (infoFieldOptions & InfoFieldOptions.enableAddress) > 0
    options_requireAddress.value = (infoFieldOptions & InfoFieldOptions.requireAddress) > 0

    options_enableNotes.value = (infoFieldOptions & InfoFieldOptions.enableNotes) > 0
    options_requireNotes.value = (infoFieldOptions & InfoFieldOptions.requireNotes) > 0

    options_customerNotes.value = customerNoteLckey.map((item) => {
      return {
        locale: item.locale ?? "",
        value: item.value ?? "",
      }
    })
  },
  {
    deep: true,
    immediate: true,
  }
)

//#endregion options

//#region trans

const trans_query = ref<IPagedRequest>({})
const trans_data = ref<GetListPaymentLinkTransItem[]>([])
const trans_total = ref(0)

const fetchDataTask = useAsyncTask(async (signal, id?: uuid) => {
  id ??= props.linkId

  if (!id) return

  const data = await getByIdPaymentLinkTask.perform(id)
  formData.value.link = data

  const res = await getListPaymentLinkTransTask.perform({
    paymentLinkId: id,
    start: 0,
    length: 5,
  })
  trans_data.value = res.data ?? []
  trans_total.value = res.total
}).keepLatest()
const fetchData = useDebounceFn(async (id?: uuid) => await fetchDataTask.perform(id))

async function clickViewTranDetail(event: DataTableRowClickEvent) {
  const id = props.linkId
  const data = event.data as GetListPaymentLinkTransItem
  const tranId = data.id
  if (!id || !tranId) return

  await router.push({
    name: AppRouteNames.PAYMENTLINK_DETAIL__TRANSDETAIL,
    params: {
      linkId: id,
      tranId: tranId,
    },
  })
}

async function clickViewTranAll() {
  if (!formData.value.link) return
  if (!trans_data.value.length) return

  await router.push({
    path: "/transaction",
    // hash: urlHash.toUrlHashParameters({
    //   linkId: formData.value.link.id,
    //   linkName: formData.value.link.routerUri,
    //   //fromDate: formatDateTime(trans_data.value.at(trans_data.value.length - 1)?.transDate),
    //   //endDate: formatDateTime(trans_data.value.at(0)?.transDate)
    //   // Dựa theo logic hiện tại:
    //   // Logic cắt: từ ngày T năm N đến ngày T-1 năm N+1. Ví dụ: chọn 01/04/2023 đến 05/06/2024, cắt thành 01/04/2023 đến 31/03/2024
    //   fromDate: moment().add(-1, "year").add(1, "day").startOf("date").toISOString(true),
    //   endDate: moment().endOf("date").toISOString(true),
    // }),
    query: {
      linkId: formData.value.link.id,
      linkName: formData.value.link.routerUri,
      //fromDate: formatDateTime(trans_data.value.at(trans_data.value.length - 1)?.transDate),
      //endDate: formatDateTime(trans_data.value.at(0)?.transDate)
      // Dựa theo logic hiện tại:
      // Logic cắt: từ ngày T năm N đến ngày T-1 năm N+1. Ví dụ: chọn 01/04/2023 đến 05/06/2024, cắt thành 01/04/2023 đến 31/03/2024
      fromDate: moment().add(-1, "year").add(1, "day").startOf("date").toISOString(true),
      endDate: moment().endOf("date").toISOString(true),
    },
  })
}

async function clickExportTransData() {
  if (!trans_data.value.length) return

  // Set default filter
  // filterDateRange.value = undefined
  filterDateRange.value = {
    // Khi mở popup, mặc định filter lấy 6 tháng gần nhất. Lưu ý làm tròn tháng
    // Ví dụ: hiện tại ngày 22/12, khi mở popup filter sẵn 01/07/2023 đến 22/12/2023
    startDate: moment()
      .add(-6 + 1, "month")
      .startOf("month")
      .startOf("day")
      .toDate(),
    endDate: moment().endOf("day").toDate(),
  }

  toggleVisibleDownloadTranDialog(true)
}

async function exportTransData(filter: DateRangModel) {
  if (!props.linkId || !filter.startDate || !filter.endDate) {
    return
  }

  // Do export
  await exportPaymentLinkTransTask.perform({
    exportType: EnumExportType.CSV,
    paymentLinkId: props.linkId,
    dateFrom: filter.startDate,
    dateTo: filter.endDate,
  })

  toggleVisibleDownloadTranDialog(false)
}

//#endregion trans

//#region export trans

const [isVisibleDownloadTranDialog, toggleVisibleDownloadTranDialog] = useToggle()
const filterDateRange = ref<DateRangModel>()

//#endregion

async function onSidebarShow() {
  paymentMethods.value = await paymentCommonService.getAllPaymentMethods()

  // Start load data when page init completed.
  await fetchData()
}

async function onHideSidebar() {
  if (!props.linkId) return

  emit("close", props.linkId)

  await router.push({
    name: AppRouteNames.PAYMENTLINK_LIST,
  })

  eventBus.emit({
    command: EventCommandEnum.UpdateList,
  })
}

const downloadFile = async () => {
  if (!hasPermission(PermissionEnum.DownloadTransactions)) {
    toastService.error({
      summary: t("common.toast.action-not-permission"),
    })
    return
  }

  if (!formData.value.link) return
  if (!formData.value.link.fileId) return

  await downloadTask.perform(formData.value.link.fileId)
}

function onShowExportTransactionsDialog() {}
</script>

<template>
  <AppSidebar
    v-model:visible="visibleModel"
    position="full"
    class="page-linkmanagement-detail-dialog"
    :loading="fetchDataTask.isRunning || onChangeLinkEnabledStatusTask.isRunning"
    @show="onSidebarShow"
    @hide="onHideSidebar"
  >
    <template #header>
      <div class="linkmanagement-detail-dialog-header flex-grow-1 d-flex align-items-center">
        <div class="mx-auto text-center page-title">{{ $t("page-LinkManagement-detail.page-title") }}</div>
        <Button severity="secondary" class="p-sidebar-icon btn-more" @click="openMoreActions">
          <template #icon>
            <span class="p-button-icon">
              <SvgIcon :src="MoreVerticalIcon" />
            </span>
          </template>
        </Button>
        <Button severity="copy-link" :label="$t('common.buttons.copy-link')" @click="copyLinkUri()"></Button>

        <OverlayPanel
          ref="refMoreOP"
          v-model:visible="isVisibledMoreOP"
          class="linkmanagement-detail-popover-more-actions"
        >
          <div class="list-menu">
            <div
              v-tooltip.bottom="{
                value: !hasPermission(PermissionEnum.EditLink) ? $t('common.tooltip.action-not-permission') : '',
              }"
              :class="{ 'no-permission': !hasPermission(PermissionEnum.EditLink) }"
              :disabled="!hasPermission(PermissionEnum.EditLink)"
              class="list-menu-item clickable"
              @click="onClickEditLink()"
            >
              <SvgIcon
                :src="!hasPermission(PermissionEnum.EditLink) ? EditIconDisabled : EditIcon"
                class="list-menu-item-icon"
              />
              <span class="list-group-item-text">{{ $t("page-LinkManagement-detail.actions.edit") }}</span>
            </div>
            <div class="list-menu-item clickable" @click="onClickDownloadQR()">
              <SvgIcon :src="DownloadQRCodeIcon" class="list-menu-item-icon" />
              <span class="list-group-item-text">{{ $t("page-LinkManagement-detail.actions.download-qr") }}</span>
            </div>
            <div
              v-tooltip.bottom="{
                value: !hasPermission(PermissionEnum.EditLink) ? $t('common.tooltip.action-not-permission') : '',
              }"
              :class="{ 'no-permission': !hasPermission(PermissionEnum.EditLink) }"
              :disabled="!hasPermission(PermissionEnum.EditLink)"
              class="list-menu-item"
            >
              <SvgIcon
                :src="!hasPermission(PermissionEnum.EditLink) ? LinkIconDisabled : LinkIcon"
                class="list-menu-item-icon"
              />
              <span class="list-menu-item-text">{{ $t("page-LinkManagement-detail.actions.status") }}</span>
              <InputSwitch
                class="ml-auto"
                :model-value="formData.link?.enabled ?? false"
                :disabled="!hasPermission(PermissionEnum.EditLink) || onChangeLinkEnabledStatusTask.isRunning"
                @change="onChangeLinkEnabledStatus"
              />
            </div>
          </div>
        </OverlayPanel>
      </div>
    </template>

    <div v-if="formData.link?.id && getByIdPaymentLinkTask.lastSuccessful" class="page-linkmanagement-detail">
      <div class="group-link-status panel-link-status panel-group-type-status">
        <div class="link-type">
          {{ formatLinkType(formData.link.linkType) }}
        </div>

        <Button
          v-tooltip.bottom="formatPaymentLinkStatus(formData.link).tooltip"
          :class="formatPaymentLinkStatus(formData.link).itemClass"
          :label="formatPaymentLinkStatus(formData.link).label"
        ></Button>
      </div>

      <div class="group-link-col group-link-groups">
        <div class="group-link-row">
          <div class="group-link-col group-link-information-options">
            <div class="card card-link-information">
              <div class="card-header">{{ $t("page-LinkManagement-detail.link-info.title") }}</div>
              <div class="card-body pt-0">
                <div class="card-content">
                  <div class="info-group">
                    <div class="info-title link-info-title">{{ $t("page-LinkManagement-detail.link-info.name") }}</div>
                    <div class="info-detail">
                      {{ formData.link.name }}
                    </div>
                  </div>

                  <div class="info-group panel-download-qr-routerUri">
                    <InputText
                      size="small"
                      class="txt-link-routerUri"
                      type="text"
                      :model-value="fullRouterUri"
                      readonly
                    />
                    <SvgIcon :src="CopyIcon" class="ml-auto user-clickable" @click.stop="copyLinkUri()" />
                  </div>

                  <div class="info-group">
                    <div class="info-title link-info-title">
                      {{ $t("page-LinkManagement-detail.link-info.description") }}
                    </div>
                    <div class="info-detail" style="max-width: 744px; text-wrap: wrap">
                      {{ formData.link?.description }}
                    </div>
                  </div>

                  <div class="info-group">
                    <div class="info-title link-info-title">
                      {{ $t("page-LinkManagement-detail.link-info.payment-method") }}
                    </div>
                    <div class="info-detail">
                      <ul class="list-paymentMethods list-unstyled">
                        <li
                          v-for="item in paymentMethods"
                          :key="item.id"
                          :class="{ selected: (item.id & formData.link.paymentMethods) > 0 }"
                        >
                          <SvgIcon
                            v-if="(item.id & formData.link.paymentMethods) > 0"
                            :src="CheckIcon"
                            width="20"
                            height="20"
                          />
                          <SvgIcon v-else :src="CloseIcon" width="20" height="20" />
                          {{ $t(`components-paymentLink-LinkInfomation.payment-method.${item.code}`) }}
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <Panel header="Options" toggleable class="card-link-options">
              <div class="card-content">
                <div class="">
                  <p class="mb-12px">{{ $t("page-LinkManagement-detail.options.required-customer-info") }}</p>
                  <ul class="list-group">
                    <li class="list-group-item" :class="{ enabled: options_enableName }">
                      <SvgIcon :src="options_enableName ? CheckIcon : CloseIcon" class="list-group-item-checked" />
                      <span class="list-group-item-label">{{ $t("common.payment-link-options.labels.name") }}</span>
                      <span class="list-group-item-state">
                        {{
                          options_requireName
                            ? $t("common.payment-link-options.states.required")
                            : $t("common.payment-link-options.states.optional")
                        }}
                      </span>
                    </li>
                    <li class="list-group-item" :class="{ enabled: options_enablePhone }">
                      <SvgIcon :src="options_enablePhone ? CheckIcon : CloseIcon" class="list-group-item-checked" />
                      <span class="list-group-item-label">{{ $t("common.payment-link-options.labels.phone") }}</span>
                      <span class="list-group-item-state">
                        {{
                          options_requirePhone
                            ? $t("common.payment-link-options.states.required")
                            : $t("common.payment-link-options.states.optional")
                        }}
                      </span>
                    </li>
                    <li class="list-group-item" :class="{ enabled: options_enableEmail }">
                      <SvgIcon :src="options_enableEmail ? CheckIcon : CloseIcon" class="list-group-item-checked" />
                      <span class="list-group-item-label">{{ $t("common.payment-link-options.labels.email") }}</span>
                      <span class="list-group-item-state">
                        {{
                          options_requireEmail
                            ? $t("common.payment-link-options.states.required")
                            : $t("common.payment-link-options.states.optional")
                        }}
                      </span>
                    </li>
                    <li class="list-group-item" :class="{ enabled: options_enableAddress }">
                      <SvgIcon :src="options_enableAddress ? CheckIcon : CloseIcon" class="list-group-item-checked" />
                      <span class="list-group-item-label">{{ $t("common.payment-link-options.labels.address") }}</span>
                      <span class="list-group-item-state">
                        {{
                          options_requireAddress
                            ? $t("common.payment-link-options.states.required")
                            : $t("common.payment-link-options.states.optional")
                        }}
                      </span>
                    </li>
                    <li class="list-group-item" :class="{ enabled: options_enableNotes }">
                      <SvgIcon :src="options_enableNotes ? CheckIcon : CloseIcon" class="list-group-item-checked" />
                      <span class="list-group-item-label" style="max-width: 38.125rem">
                        {{ $t("common.payment-link-options.labels.notes") }}
                        <ul v-if="options_enableNotes" class="list-unstyled">
                          <li v-for="item in options_customerNotes" :key="item.locale">
                            <AppSvgNamedIcon :name="item.locale" area="locales" class="list-group-item-checked" />
                            <span class="list-group-item-label-item">{{ item.value }}</span>
                          </li>
                        </ul>
                      </span>
                      <span class="list-group-item-state">
                        {{
                          options_requireNotes
                            ? $t("common.payment-link-options.states.required")
                            : $t("common.payment-link-options.states.optional")
                        }}
                      </span>
                    </li>
                  </ul>

                  <div v-if="formData.link.dueDate">
                    <p class="mt-16px mb-0">{{ $t("page-LinkManagement-detail.duedate") }}</p>
                    <p class="have-due-date mt-8px mb-0">
                      <SvgIcon :src="Calendar" class="have-due-date-icon" />
                      {{ formatDateTime(formData.link.dueDate) }}
                    </p>
                  </div>

                  <div v-if="formData.link.fileId">
                    <p class="mt-16px mb-0">{{ $t("page-LinkManagement-detail.attachment") }}</p>
                    <p class="mt-8px mb-0">
                      <a class="have-attachment" href="javascript:void(0)" @click="downloadFile">
                        <SvgIcon :src="UploadFileIcon" class="have-attachment-icon" />
                        <span class="text-truncate">{{ formData.link.fileName }}</span>
                      </a>
                    </p>
                  </div>
                </div>
              </div>
            </Panel>
          </div>

          <div class="group-link-col group-link-detail">
            <div class="card card-link-createupdate">
              <div class="card-body">
                <div class="card-content">
                  <div class="info-group">
                    <div class="info-title">{{ $t("page-LinkManagement-detail.last-updated") }}</div>
                    <div class="info-detail">
                      <span class="info-detail-createupdatedate">
                        {{ formatDateTime(formData.link.lastModifyDate) + " by " }}
                      </span>
                      <span class="info-detail-user">
                        {{ formData.link.lastModifyByUser?.email ?? formData.link.lastModifyByUserId }}
                      </span>
                    </div>
                  </div>

                  <div class="info-group">
                    <div class="info-title">{{ $t("page-LinkManagement-detail.created-date") }}</div>
                    <div class="info-detail">
                      <span class="info-detail-createupdatedate">
                        {{ formatDateTime(formData.link.createDate) + " by " }}
                      </span>
                      <span class="info-detail-user">
                        {{ formData.link.createByUser?.email ?? formData.link.createByUserId }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div v-if="paymentAmount_enable" class="card card-link-payment-amount">
              <div class="card-header">{{ $t("page-LinkManagement-detail.payment-amount.title") }}</div>
              <div class="card-body pt-0">
                <div class="card-content">
                  <div class="info-group info-group-row info-group--amount">
                    <div class="info-title">{{ $t("page-LinkManagement-detail.payment-amount.amount") }}</div>
                    <div class="info-detail">
                      {{ formatCurrency(paymentAmount_amount, paymentAmount_currency) }}
                    </div>
                  </div>
                  <div v-if="paymentAmount_enableDiscount" class="info-group info-group-row info-group--discount">
                    <div class="info-title">
                      {{ $t("page-LinkManagement-detail.payment-amount.discount") }}
                      {{ paymentAmount_discountIsPercent ? ` (${paymentAmount_discount}%)` : " " }}
                    </div>
                    <div class="info-detail">
                      {{ "-" + formatCurrency(paymentAmount_discountValue, paymentAmount_currency) }}
                    </div>
                  </div>
                  <div v-if="paymentAmount_enableTax" class="info-group info-group-row info-group--tax">
                    <div class="info-title">
                      {{ $t("page-LinkManagement-detail.payment-amount.tax") }}
                      <span class="info-title-percent">
                        &nbsp;({{ paymentAmount_tax }}{{ $t("page-LinkManagement-detail.payment-amount.percent") }})
                      </span>
                    </div>
                    <div class="info-detail">
                      {{ formatCurrency(paymentAmount_taxValue, paymentAmount_currency) }}
                    </div>
                  </div>
                  <div v-if="paymentAmount_enableOtherFee" class="info-group info-group-row info-group--otherFee">
                    <div class="info-title">
                      <span
                        v-tooltip.bottom="paymentAmount_otherFeeLabel"
                        class="text-truncate"
                        style="max-width: 6.875rem"
                      >
                        {{ paymentAmount_otherFeeLabel || $t("components-paymentLink-LinkPaymentAmount.other-fee") }}
                      </span>
                      <span v-if="paymentAmount_otherFeeIsPercent" class="info-title-percent">
                        &nbsp;({{ paymentAmount_otherFee
                        }}{{ $t("page-LinkManagement-detail.payment-amount.percent") }})
                      </span>
                    </div>
                    <div class="info-detail">
                      {{ formatCurrency(paymentAmount_otherFeeValue, paymentAmount_currency) }}
                    </div>
                  </div>

                  <hr v-if="paymentAmount_currency != paymentAmount_paymentCurrency" class="m-0" />
                  <template v-if="paymentAmount_currency != paymentAmount_paymentCurrency">
                    <div class="info-group">
                      <div class="info-group-row">
                        <div class="info-title">{{ $t("page-LinkManagement-detail.payment-amount.total-amount") }}</div>
                        <div class="info-detail info-detail-paymentamount">
                          {{ formatCurrency(paymentAmount_totalAmount, paymentAmount_currency) }}
                        </div>
                      </div>
                      <div class="info-group-row">
                        <div class="info-detail info-detail-paymentamount-exchange">
                          <span
                            v-tooltip.bottom="{
                              escape: false,
                              autoHide: false,
                              hideDelay: 300,
                              value: $htmlSafe(
                                $t('common.tooltip.exchange-rate', { url: APPCONFIG.APP_LINK_EXCHANGERATECONFIG })
                              ),
                            }"
                          >
                            {{
                              //`1 ${paymentAmount_currency} = ${formatCurrency(paymentAmount_exchangeRate, paymentAmount_paymentCurrency)}`
                              formatExchangeRate(
                                1,
                                paymentAmount_currency,
                                paymentAmount_exchangeRate,
                                paymentAmount_paymentCurrency
                              )
                            }}
                            <SvgIcon :src="InfoIcon" />
                          </span>
                        </div>
                      </div>
                    </div>
                  </template>

                  <hr class="m-0" />
                  <div class="info-group info-group-row">
                    <div class="info-title">
                      {{ $t("page-LinkManagement-detail.payment-amount.payment-amount-number") }}
                    </div>
                    <div class="info-detail info-detail-paymentamount">
                      {{ formatCurrency(paymentAmount_paymentAmount, paymentAmount_paymentCurrency) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div v-if="paymentLimit_enable" class="card card-link-payment-limit">
              <div class="card-header">{{ $t("page-LinkManagement-detail.limit-payment.title") }}</div>
              <div class="card-body pt-0">
                <div class="card-content">
                  <div class="info-group info-group-row">
                    <div class="info-title">{{ $t("page-LinkManagement-detail.limit-payment.max-payment") }}</div>
                    <div class="info-detail">
                      {{ formatNumber(paymentLimit_maxPaymentQty, 0) }}
                    </div>
                  </div>
                  <div class="info-group info-group-row">
                    <div class="info-title">
                      {{ $t("page-LinkManagement-detail.limit-payment.successful-payment") }}
                    </div>
                    <div class="info-detail">
                      {{ formatNumber(paymentLimit_totalPaymentQty, 0) }}
                    </div>
                  </div>
                  <div class="info-group info-group-row">
                    <div class="info-title">
                      {{ $t("page-LinkManagement-detail.limit-payment.inprogress-payment") }}
                    </div>
                    <div class="info-detail">
                      {{ formatNumber(paymentLimit_inprogressPayments, 0) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="group-link-row group-link-transactions">
          <div class="card card-link-payment-transactions">
            <div class="card-header d-flex align-items-center">
              <span>{{ $t("page-LinkManagement-detail.recent-transactions") }}</span>
              <div class="m-auto"></div>
              <Button
                severity="secondary"
                size="small"
                :label="$t('common.buttons.view-all')"
                :disabled="!trans_data.length"
                @click="clickViewTranAll()"
              ></Button>
              <Button
                severity="download"
                class="btn-download"
                style="width: 2.25rem; height: 2.25rem; margin-left: 0.75rem"
                :disabled="!trans_data.length"
                @click="clickExportTransData()"
              >
                <template #icon>
                  <span class="p-button-icon">
                    <SvgIcon :src="DownloadIcon" />
                  </span>
                </template>
              </Button>
            </div>
            <div class="card-body pt-0">
              <AppDataTable
                ref="refDataTable"
                v-model:query="trans_query"
                class="dtPaymentLink"
                :paginator="false"
                :value="trans_data"
                :total-records="trans_total"
                :loading="getListPaymentLinkTransTask?.isRunning"
                @row-click="clickViewTranDetail"
              >
                <!-- <Column field="id" header="Id"></Column> -->

                <Column field="transID" :header="$t('common.transactionId')">
                  <template #body="slotProps">
                    <span>
                      {{ slotProps.data.transID || "-" }}
                    </span>
                  </template>
                </Column>
                <Column field="customerName" :header="$t('common.customerName')">
                  <template #body="slotProps">
                    <div
                      v-tooltip.bottom="{ value: slotProps.data.customerName }"
                      class="text-nowrap text-truncate"
                      style="max-width: 128px"
                    >
                      {{ slotProps.data.customerName }}
                    </div>
                  </template>
                </Column>
                <Column
                  field="amount"
                  :header="$t('common.amount')"
                  class="text-nowrap text-right"
                  header-style="width: 160px"
                >
                  <template #body="slotProps">
                    <span :data-currency="slotProps.data.currency">
                      {{
                        // slotProps.data.transaction && slotProps.data.currency
                        //   ? formatCurrency(slotProps.data.transaction.amount, slotProps.data.transaction.currency)
                        //   : formatCurrency(slotProps.data.totalAmount, slotProps.data.currency)
                        formatCurrency(slotProps.data.amount, slotProps.data.currency)
                      }}
                    </span>
                  </template>
                </Column>
                <Column field="transDate" :header="$t('common.date')" class="text-nowrap" header-style="width: 160px">
                  <template #body="slotProps">
                    <span>
                      {{ formatDateTime(slotProps.data.transDate) }}
                    </span>
                  </template>
                </Column>
                <Column field="paymentMethod" :header="$t('common.paymentMethod')"></Column>
                <Column field="cardNo" :header="$t('common.cardNumber')"></Column>
                <Column field="transType" :header="$t('common.transType')">
                  <template #body="slotProps">
                    <span
                      v-tooltip.bottom="{
                        value: formatPaymentLinkTransType(slotProps.data.transType).tooltip,
                        class: 'tooltip-PaymentLinkTransStatus',
                      }"
                      :data-trans-type="slotProps.data.transType"
                      class="tdPaymentLinkTransStatus"
                      :class="formatPaymentLinkTransType(slotProps.data.transType).itemClass"
                    >
                      {{ formatPaymentLinkTransType(slotProps.data.transType).label }}
                    </span>
                  </template>
                </Column>
                <Column field="status" :header="$t('common.status')">
                  <template #body="slotProps">
                    <span
                      v-tooltip.bottom="{
                        value: formatPaymentLinkTransStatus(slotProps.data.status).tooltip,
                        class: 'tooltip-PaymentLinkTransStatus',
                      }"
                      :data-trans-status="slotProps.data.status"
                      class="tdPaymentLinkTransStatus"
                      :class="formatPaymentLinkTransStatus(slotProps.data.status).itemClass"
                    >
                      {{ formatPaymentLinkTransStatus(slotProps.data.status).label }}
                    </span>
                  </template>
                </Column>
              </AppDataTable>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppSidebar>

  <Lazy>
    <DownloadQrDialog v-model:visible="isShowDownloadQrDialog" :value="dataDownloadQrDialog" />
    <ExportTransactionsDialog
      v-model:visible="isVisibleDownloadTranDialog"
      v-model:model-value="filterDateRange"
      @show="onShowExportTransactionsDialog"
      @click-download="exportTransData"
    ></ExportTransactionsDialog>
  </Lazy>
</template>

<style lang="scss">
@use "../../assets/scss/variables" as v;

.page-linkmanagement-detail-dialog {
  .p-sidebar-content {
    background: var(--color-neutral-75_240, #f0f0f0);
  }

  .linkmanagement-detail-dialog-header {
    .btn-more {
      margin-right: 1rem;
    }
    .p-button-copy-link {
      padding: 0.75rem 1.5rem;

      font-size: var(--Typeface-size-lg, 1rem);
      font-style: normal;
      font-weight: 700;
      line-height: 1.5rem; /* 150% */
    }
  }
}

.linkmanagement-detail-popover-more-actions {
  width: 13.5rem;

  .list-menu {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;

    .list-menu-item {
      display: flex;
      align-items: center;
      gap: 0.75rem;

      //.list-menu-item-icon {
      //  margin-right: 0.75rem;
      //}
    }
  }
}

.page-linkmanagement-detail {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 75rem;
  margin: 0 auto;

  .text-header-1 {
    color: var(--text-headings, #1c1c1c);
    /* Inter/H2/20_Semibold */
    font-family: var(--Typeface-family-Inter, Inter);
    font-size: var(--Typeface-size-xl, 20px);
    font-style: normal;
    font-weight: 600;
    line-height: 28px; /* 140% */
  }

  .text-header-2 {
    color: var(--text-headings, #1c1c1c);
    /* Inter/B1/16_Semibold */
    font-family: var(--Typeface-family-Inter, Inter);
    font-size: var(--Typeface-size-lg, 16px);
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
  }

  .text-sub-header-1 {
    color: var(--text-tertiary, #6c6c6c);
    /* Inter/B2/14_Regular */
    font-family: var(--Typeface-family-Inter, Inter);
    font-size: var(--Typeface-size-md, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */
  }

  .text-sub-header-2 {
    color: var(--text-body, #404040);
    /* Inter/B2/14_Medium */
    font-family: var(--Typeface-family-Inter, Inter);
    font-size: var(--Typeface-size-md, 14px);
    font-style: normal;
    font-weight: 500;
    line-height: 20px; /* 142.857% */
  }

  //#region groups

  .group-link-row,
  .group-link-col {
    display: flex;
    gap: 1.5rem;
  }
  .group-link-row {
    flex-direction: row;
  }
  .group-link-col {
    flex-direction: column;
  }

  .group-link-status {
    padding: 1rem 0;

    .paymentLink-Status-Inactive {
      padding: 8px 16px;
      border-radius: 100px;
      border: 1px solid #ed98a4;
      background-color: #ffeff0;
      color: #d82039;
    }

    .paymentLink-Status-Expired {
      padding: 8px 16px;
      border-radius: 100px;
      border: 1px solid #ed98a4;
      background-color: #ffeff0;
      color: #d82039;
    }

    .paymentLink-Status-LimitReached {
      padding: 8px 16px;
      border-radius: 100px;
      border: 1px solid #ed98a4;
      background-color: #ffeff0;
      color: #d82039;
    }

    .paymentLink-Status-Active {
      padding: 8px 16px;
      border-radius: 100px;
      border: 1px solid #8ae2b5;
      background-color: #e6f9ef;
      color: #00bf5f;
    }

    .paymentLink-Status-Unknown {
      padding: 8px 16px;
      border-radius: 100px;
      border: 1px solid #f9c892;
      background-color: #fef4ea;
      color: #f38713;
    }
  }

  .group-link-groups {
    max-width: 75rem;

    .group-link-information-options {
      width: 792px; // nhớ sửa cả màn Add/Edit
      flex: 1 0;
    }

    .group-link-detail {
      flex: 0 1 24rem;
    }

    .group-link-transactions {
      margin-bottom: 45px;
      flex: 1 1 auto;
    }
  }

  //#endregion groups

  //#region cards

  .card,
  .p-panel {
    .card-content {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
  }

  .card-link-information {
    border: none;

    .card-header {
      @extend .text-header-1;
    }

    .info-group .info-title {
      @extend .text-sub-header-1;
    }

    .list-paymentMethods {
      margin-bottom: 0;
      li {
        margin-top: 0.5rem;
      }

      li:not(.selected) {
        color: var(--text-disabled2, #bababa);
      }
    }

    .panel-download-qr-routerUri {
      max-width: 35.25rem;
      display: flex;
      flex-direction: row;
      align-items: center;

      .txt-link-routerUri {
        border-color: #8ae2b5;
        background-color: #e6f9ef;
        color: var(--text-success, #00bf5f);
        margin-right: 0.75rem;
      }
    }
  }

  .card-link-createupdate {
    border: none;

    .info-group .info-title {
      @extend .text-sub-header-2;
    }
  }

  .card-link-payment-amount {
    border: none;

    .card-header {
      @extend .text-header-2;
    }

    .info-title,
    .info-detail {
      flex-basis: 50%;
    }
  }

  .card-link-payment-limit {
    border: none;

    .card-header {
      @extend .text-header-2;
    }
  }

  .card-link-options {
    border: none;

    .p-panel-header {
      @extend .text-header-2;
    }

    .list-group {
      border-radius: 0;
    }
    .list-group-item {
      padding: 0.625rem 1rem;
      display: flex;
      justify-content: start;
      align-items: stretch;

      &:not(.enabled) {
        color: var(--text-disabled2, #bababa);
      }

      .list-group-item-checked {
        margin-right: 0.5rem;
      }

      .list-group-item-label-item {
        @include v.allow_line_break();
      }

      .list-group-item-state {
        margin-left: auto;
        color: var(--text-disabled2, #bababa);
      }
    }

    .have-due-date {
      display: flex;
      align-items: center;

      &-icon {
        margin-right: 0.5rem;
      }
    }

    .have-attachment {
      display: flex;
      align-items: center;
    }

    .have-attachment-icon {
      width: 1rem;
      height: 1rem;
      min-width: 1rem;
      margin-right: 0.25rem;
    }
  }

  .card-link-payment-transactions {
    border: none;
    flex: 1;

    .card-header {
      @extend .text-header-2;
    }
  }

  //#endregion panels

  //#region info-group

  .info-group {
    font-size: var(--Typeface-size-md, 0.875rem);
    font-weight: 400;
    line-height: 1.25rem; /* 142.857% */
    color: #404040;

    display: flex;
    align-items: start;
    justify-items: center;
    flex-direction: column;
    gap: 0.25rem;

    .info-title {
      display: flex;

      .info-title-percent {
        flex-shrink: 1;
      }
    }

    .info-detail {
      text-wrap: wrap;
      @include v.allow_line_break();
    }

    .info-detail-createupdatedate {
      color: #6c6c6c;
    }
    .info-detail-user {
      color: #404040;
      font-weight: 500;
    }
    .info-detail-paymentamount {
      font-size: var(--Typeface-size-xl, 1.25rem);
      font-weight: 600;
      line-height: 1.75rem; /* 140% */
      color: #1c1c1c;
      white-space: nowrap;
    }
    .info-detail-paymentamount-exchange {
      font-size: var(--Typeface-size-sm, 0.75rem);
      font-style: normal;
      font-weight: 400;
      line-height: 1rem; /* 133.333% */
      color: var(--text-tertiary, #6c6c6c);

      span {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
      }
    }
  }

  .info-group-row {
    width: 100%;
    display: flex;
    flex-direction: row;

    .info-title {
      align-self: center;
      margin-bottom: 0;
    }
    .info-detail {
      flex-grow: 1;
      text-align: right;
    }
  }

  //#endregion info-group
}

.paymentLink-Trans-Status-Successful {
  color: #00bf5f;
}

.paymentLink-Trans-Status-Failed {
  color: #d82039;
}
.paymentLink-Trans-Status-AwaitTransactionResult {
  color: #f38713;
}

.link-info-title {
  font-weight: 600 !important;
  color: #404040 !important;
}

.panel-group-type-status {
  display: flex;
  align-items: center;

  .link-type {
    background-color: #ffffff;
    border: 1px solid #dcdcdc;
    height: 40px;
    min-width: 104px;
    border-radius: 100px;
    padding: 8px 16px;
    margin-right: 12px;
    text-align: center;
  }
}
</style>
