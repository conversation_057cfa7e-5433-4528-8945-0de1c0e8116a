<script setup lang="ts">
import ChevronDownIcon from "@assets/icons/chevron-down-for-filter.svg"
import CopyIcon from "@assets/icons/copy.svg"
import DownloadIcon from "@assets/icons/download.svg"
import ReloadIcon from "@assets/icons/reload.svg"
import StarCheckedIcon from "@assets/icons/star-checked.svg"
import StarUnCheckIcon from "@assets/icons/star-uncheck.svg"

import { GetByIdPaymentLinkOutput, GetListPaymentLinkItem } from "@/apis/paymentLinkApi"
import APPCONFIG from "@/appConfig"
import { EnumExportType, EnumPaymentLinkType, EnumSearchPaymentLinkStatus } from "@/app.const"
import { PermissionEnum } from "@/enums/permissions"
import { AppRouteNames } from "@/enums/routers"
import { EventCommandEnum, usePaymentLinkEventBus } from "@/events/eventBus"
import { useAuthService } from "@/services/authService"
import { usePaymentLinkService } from "@/services/paymentLinkService"
import { useToastService } from "@/services/toastService"
import { DateRangModel, IPagedRequest, SelectListItem } from "@/types/base"
import { formatCurrency, formatDateTime, formatRangeDateOnly, formatTimeZone } from "@/utils/formatUtil"
import { enumToSelectListItem } from "@utils/enumUtil"
import { useClipboard, useDebounceFn, useResizeObserver, useToggle, watchDeep } from "@vueuse/core"
import moment from "moment"
import type { DataTableRowClickEvent } from "primevue/datatable"
import OverlayPanel from "primevue/overlaypanel"
import { computed, defineAsyncComponent, onMounted, ref } from "vue"
import { useAsyncTask } from "vue-concurrency"
import { useI18n } from "vue-i18n"
import { onBeforeRouteUpdate, useRouter } from "vue-router"

import type { DownloadQrDialogValueType } from "@/components/DownloadQrDialog.vue"
import type AppDataTable from "@components/ui/AppDataTable.vue"
import type AppMultipleSelectOverlayPanel from "@components/ui/AppMultipleSelectOverlayPanel.vue"
import type DateTimePickerOverlayPanel from "@components/ui/DateTimePickerOverlayPanel.vue"

//import Edit from '@/components/paymentLink/Edit.vue';
const Edit = defineAsyncComponent(() => import("@/components/paymentLink/Edit.vue"))
//import AddNew from "@/components/paymentLink/AddNew.vue"
const AddNew = defineAsyncComponent(() => import("@/components/paymentLink/AddNew.vue"))
//import AddSuccessPayment from "@/components/paymentLink/AddSuccessPayment.vue"
const AddSuccessPayment = defineAsyncComponent(() => import("@/components/paymentLink/AddSuccessPayment.vue"))
//import LinkDetail from "@/views/paymentlink/LinkDetail.vue"
const LinkDetail = defineAsyncComponent(() => import("@/views/paymentlink/LinkDetail.vue"))

const { t } = useI18n()
const router = useRouter()
const { copy } = useClipboard()
const toastService = useToastService()
const { hasPermission } = useAuthService()
const {
  getListPaymentLinkTask,
  pinPaymentLinkTask,
  exportPaymentLinkTask,
  getByIdPaymentLinkTask,
  formatPaymentLinkStatus,
  correctPaymentLinkRouterUri,
  getPaymentLinkFullRouterUri,
  formatLinkType,
} = usePaymentLinkService()
const eventBus = usePaymentLinkEventBus()
//const bus = useEventBus<string>("ADD_EDIT_PAYMENT_LINK_EVENT")
const isSelectAllStatus = ref(true)

console.log("LinkManagement page inited")

// To test page await
//await new Promise((resolve) => setTimeout(resolve, 3000))

interface DataFilterType {
  keywords?: string
  linkStatus: any[]
  dateRange?: DateRangModel
  dataTableQuery: IPagedRequest
  linkType: number
}

function getDefaultFilters(value?: DataFilterType): DataFilterType {
  const defaultValue = {
    linkStatus: [],
    dataTableQuery: {
      start: 0,
      length: APPCONFIG.DEFAULT_TABLE_ITEM_PER_PAGE,
    },
    linkType: 0,
  }
  if (value) {
    for (const [key, val] of Object.entries(defaultValue)) {
      value[key] = val
    }
    return value
  }
  return defaultValue
}

const dataFilters = ref<DataFilterType>(getDefaultFilters())
const data = ref<GetListPaymentLinkItem[]>([])
const dataTotal = ref(0)

const fetchDataTask = useAsyncTask(async (signal, input: void) => {
  const res = await getListPaymentLinkTask.perform({
    keywords: dataFilters.value.keywords,
    linkStatus: dataFilters.value.linkStatus,
    dateFrom: dataFilters.value.dateRange?.startDate,
    dateTo: dataFilters.value.dateRange?.endDate,
    start: dataFilters.value.dataTableQuery?.start,
    length: dataFilters.value.dataTableQuery?.length,
    linkType: dataFilters.value.linkType,
  })
  data.value = res.data
  dataTotal.value = res.total
}).keepLatest()
const fetchData = useDebounceFn(async () => await fetchDataTask.perform())

watchDeep(dataFilters, () => {
  fetchData()
})

// trigger now
onMounted(async () => {
  // Start load data when page init completed.
  fetchData()
})
onBeforeRouteUpdate(() => {
  fetchData()
})

async function exportFile() {
  toggleShowExportFileDialog(true)

  await exportPaymentLinkTask.perform({
    exportType: EnumExportType.CSV,
    keywords: dataFilters.value.keywords,
    linkStatus: dataFilters.value.linkStatus,
    dateFrom: dataFilters.value.dateRange?.startDate,
    dateTo: dataFilters.value.dateRange?.endDate,
  })

  toggleShowExportFileDialog(false)
}

//#region Dialogs

const [isShowDownloadQrDialog, toggleShowDownloadQrDialog] = useToggle()
const dataDownloadQrDialog = ref<DownloadQrDialogValueType>()

const [isShowExportFileDialog, toggleShowExportFileDialog] = useToggle()

const [isShowDetailDialog, toggleShowDetailDialog] = useToggle()
const dataDetailDialog = ref<uuid>()

//#endregion

//#region DataTable

//const refDataTable = ref<InstanceType<typeof AppDataTable>>()
const refDataTableWrapper = ref<HTMLElement>()
const heightDataTable = ref<string>()

useResizeObserver(refDataTableWrapper, (entries) => {
  const entry = entries[0]
  const { height } = entry.contentRect
  const value = height - 61 - 5 //61: is paginator component. 5: is buffer

  heightDataTable.value = value > 400 ? `${value}px` : ""
})

function onDataTableRowClick(event: DataTableRowClickEvent) {
  console.log("onDataTableRowClick", event.data)
  detailItem(event.data as GetListPaymentLinkItem)
}

function onClickClearFilter() {
  dataFilters.value = getDefaultFilters()
  isSelectAllStatus.value = true
  //fetchData()
}

function formatDispayRouterUri(input: GetListPaymentLinkItem | string) {
  let routerUri = typeof input === "string" ? input : input.routerUri
  routerUri = correctPaymentLinkRouterUri(routerUri)
  return `...${routerUri}`
}

async function copyLinkUri(routerUri: string) {
  console.log(`Copy '${routerUri}'`)

  await copy(routerUri)
  toastService.copySuccess({})
}

const setPinTask = useAsyncTask(async (signal, item: GetListPaymentLinkItem, state: boolean) => {
  console.log(`Set Favorited '${item.routerUri}': ${state}`)

  const res = await pinPaymentLinkTask.perform({
    id: item.id,
    pin: !item.isPinned,
  })

  await fetchData()
}).keepLatest()
//async function setPin(item: GetListPaymentLinkItem, state: boolean) {
const setPin = (item: GetListPaymentLinkItem, state: boolean) => setPinTask.perform(item, state)

function detailItem(item: GetListPaymentLinkItem) {
  //dataDetailDialog.value = item.id
  //toggleShowDetailDialog(true)

  router.push({
    name: AppRouteNames.PAYMENTLINK_DETAIL,
    params: {
      linkId: item.id,
    },
  })
}

function createNew() {
  if (!hasPermission(PermissionEnum.CreateLink)) {
    toastService.error({
      summary: t("common.toast.action-not-permission"),
    })
    return
  }

  //toggleShowAddNewPopup(true)

  router.push({
    name: AppRouteNames.PAYMENTLINK_CREATE,
    params: {},
  })
}

async function editItem(item: GetListPaymentLinkItem | uuid) {
  if (!hasPermission(PermissionEnum.EditLink)) {
    toastService.error({
      summary: t("common.toast.action-not-permission"),
    })
    return
  }

  let id: uuid
  if (typeof item === "string") {
    id = item as uuid
  } else {
    id = item.id
  }
  console.log(`Edit Item '${id}'`)
  //paymentId.value = id
  //toggleShowEditPopup.value = true

  router.push({
    name: AppRouteNames.PAYMENTLINK_EDIT,
    params: {
      linkId: id,
    },
  })
}

async function openDownloadQrDialog(item: GetListPaymentLinkItem) {
  console.log(item)
  console.log(`Open DownloadQrDialog`, {
    name: item.name,
    routerUri: item.routerUri,
  })

  dataDownloadQrDialog.value = {
    name: item.name,
    routerUri: item.routerUri,
    paymentAmout: item.paymentAmount,
    paymentCurrency: item.paymentCurrency,
    dueDate: formatDateTime(item.dueDate),
  }

  toggleShowDownloadQrDialog(true)
}

//#endregion DataTable

//#region FilterLastUpdated

const refFilterLastUpdated = ref<InstanceType<typeof DateTimePickerOverlayPanel> | null>(null)
const [isVisibledFilterLastUpdated] = useToggle()
function toggleFilterLastUpdated(event) {
  refFilterLastUpdated.value?.toggle(event)
}
function onApplyLastUpdated() {
  //fetchList()
}

//#endregion

//#region FilterLinkStatus

const refFilterLinkStatus = ref<InstanceType<typeof AppMultipleSelectOverlayPanel> | null>(null)
const [isVisibledFilterLinkStatus] = useToggle()
function toggleFilterLinkStatus(event) {
  refFilterLinkStatus.value?.toggle(event)
}
function onApplyLinkStatus() {
  //fetchList()
}

const listItemsLinkStatus = computed<SelectListItem[]>(() =>
  enumToSelectListItem(EnumSearchPaymentLinkStatus).map((item) => {
    return {
      value: item.value,
      label: t(`common.payment-link-status.${item.label}`),
    }
  })
)
const labelLinkStatus = computed(() =>
  dataFilters.value.linkStatus.length == 0
    ? t("common.payment-link-status.SearchAll")
    : listItemsLinkStatus.value
        .filter((item) => dataFilters.value.linkStatus.includes(item.value))
        .map((item) => item.label)
        .join(", ")
)

//#endregion FilterLinkStatus

// filter link type
const refFilterLinkType = ref<InstanceType<typeof OverlayPanel> | null>(null)
const isVisibledFilterLinkType = ref(false)
function toggleFilterLinkType(event: Event) {
  refFilterLinkType.value?.toggle(event)
}

const listItemsLinkType = ref<SelectListItem[]>([])
listItemsLinkType.value = enumToSelectListItem(EnumPaymentLinkType).map((item) => {
  return {
    value: item.value,
    label: item.label,
    selected: false,
  }
})
listItemsLinkType.value.unshift({
  label: "All",
  value: 0,
  selected: true,
})

const labelLinkType = ref<string | undefined>(listItemsLinkType.value.find((item) => item.selected)?.label)
function onApplyrefFilterLinkType(linkType: any) {
  dataFilters.value.linkType = linkType.value
  linkType.selected = true
  listItemsLinkType.value.forEach((item) => {
    if (item.value !== linkType.value) {
      item.selected = false
    }
  })
  refFilterLinkType.value?.hide()
  labelLinkType.value = linkType.label
  //fetchList()
}

function openPanelType() {
  isVisibledFilterLinkType.value = true
}

function hidePanelType() {
  isVisibledFilterLinkType.value = false
}

// end filter link type

const modelSuccess = ref<GetByIdPaymentLinkOutput>()
const [isShowAddNewDialog, toggleShowAddNewPopup] = useToggle()
const [isShowSuccessDialog, toggleShowSuccessPopup] = useToggle()
const toggleShowEditPopup = ref(false)
const paymentId = ref("")

const onCloseAddNewPopup = () => {
  isShowAddNewDialog.value = false
}

const onCloseEditPopup = () => {
  toggleShowEditPopup.value = false
}

// bus.on(async (data) => {
//   if (data) {
//     const { getById } = usePaymentLinkService()

//     await getById(data).then((data) => {
//       modelSuccess.value = data
//       showSuccess.value = true
//     })

//     fetchList()
//   } else {
//     fetchList()
//   }
// })
eventBus.on(async (e) => {
  switch (e.command) {
    case EventCommandEnum.UpdateList:
      fetchData()
      break
    case EventCommandEnum.Created: {
      if (!e.data?.id) {
        return
      }
      const data = await getByIdPaymentLinkTask.perform(e.data.id)
      modelSuccess.value = data
      toggleShowSuccessPopup(true)
      break
    }
    case EventCommandEnum.Edited: {
      toastService.success({})
      break
    }
    default:
      break
  }
})
</script>

<template>
  <div class="page-linkmanagement panel">
    <div class="panel-header">
      <div class="panel-header-title">
        <div class="page-title">{{ $t("page-LinkManagement.page-title") }}</div>
        <div class="mx-auto"></div>
        <Button
          v-tooltip.bottom="{
            value: !hasPermission(PermissionEnum.CreateLink) ? $t('common.tooltip.action-not-permission') : '',
          }"
          :class="{ 'no-permission': !hasPermission(PermissionEnum.CreateLink) }"
          severity="create"
          :label="$t('page-LinkManagement.btn-create')"
          :disabled="!hasPermission(PermissionEnum.CreateLink)"
          @click="createNew()"
        />
      </div>

      <div class="panel-header-body">
        <div class="panel-filter">
          <div class="filter-item filter-item--keywords">
            <InputTextFilterClearable
              v-model="dataFilters.keywords"
              :placeholder="$t('page-LinkManagement.input-filter-placeholder')"
              :maxlength="200"
            />
          </div>
          <div class="divider divider-vertical"></div>
          <div
            class="filter-item filter-item--lastupdated"
            :class="{ 'show-menu': isVisibledFilterLastUpdated }"
            @click="toggleFilterLastUpdated"
          >
            <div class="filter-item-header">
              <span>{{ $t("page-LinkManagement-detail.last-updated") }}</span>
              <SvgIcon :src="ChevronDownIcon" />
            </div>
            <div class="filter-item-body">
              {{ formatRangeDateOnly(dataFilters.dateRange?.startDate, dataFilters.dateRange?.endDate) }}
            </div>
            <DateTimePickerOverlayPanel
              ref="refFilterLastUpdated"
              v-model:modelValue="dataFilters.dateRange"
              :max-date="moment().endOf('day').toDate()"
              :limit-days="APPCONFIG.MAX_SELECTED_DAYS_IN_DATERANGE"
              @apply="onApplyLastUpdated"
              @toggle="(isVisible) => (isVisibledFilterLastUpdated = isVisible)"
            />
          </div>
          <div class="divider divider-vertical"></div>

          <div
            class="filter-item filter-item--linktype"
            :class="{ 'show-menu': isVisibledFilterLinkType }"
            @click="toggleFilterLinkType"
          >
            <div class="filter-item-header">
              <span>{{ $t("components-paymentLink-add.link-type") }}</span>
              <SvgIcon :src="ChevronDownIcon" />
            </div>

            <div class="filter-item-body text-truncate">
              {{ $t(`payment-link-type.${labelLinkType}`) }}
            </div>

            <OverlayPanel
              ref="refFilterLinkType"
              class="single-select-panel"
              @show="openPanelType"
              @hide="hidePanelType"
            >
              <div
                v-for="(item, index) of listItemsLinkType"
                :key="index"
                class="link-type-option"
                :class="{ 'link-type-focus': item.selected }"
                @click="onApplyrefFilterLinkType(item)"
              >
                <span>{{ $t(`payment-link-type.${item.label}`) }}</span>
              </div>
            </OverlayPanel>
          </div>
          <div class="divider divider-vertical"></div>

          <div
            v-tooltip.bottom="labelLinkStatus.replaceAll(',', '\n')"
            class="filter-item filter-item--linkstatus"
            :class="{ 'show-menu': isVisibledFilterLinkStatus }"
            @click="toggleFilterLinkStatus"
          >
            <div class="filter-item-header">
              <span>{{ $t("page-LinkManagement-detail.actions.status") }}</span>
              <SvgIcon :src="ChevronDownIcon" />
            </div>
            <div class="filter-item-body text-truncate">
              {{ labelLinkStatus }}
            </div>
            <DropdownMultipleSelectOverlayPanel
              ref="refFilterLinkStatus"
              v-model:modelValue="dataFilters.linkStatus"
              v-model:visible="isVisibledFilterLinkStatus"
              v-model:is-seclected-all="isSelectAllStatus"
              :options="listItemsLinkStatus"
              option-label="label"
              option-value="value"
              :select-all-allowed="true"
              :select-all-label="$t('common.payment-link-status.SearchAll')"
              :select-all-value="[]"
              :auto-apply="false"
              @apply="onApplyLinkStatus"
            ></DropdownMultipleSelectOverlayPanel>
          </div>
          <div class="divider divider-vertical"></div>
          <div class="filter-item filter-item--clearfilter">
            <Button
              class="btn-clearfilter"
              :label="$t('page-Transaction-Management.all-trans.clear-filter')"
              text
              @click="onClickClearFilter()"
            >
              <template #icon>
                <span class="p-button-icon-left">
                  <SvgIcon :src="ReloadIcon" />
                </span>
              </template>
            </Button>
          </div>
          <div class="divider divider-vertical invisible mr-0"></div>
          <div class="filter-actions ml-auto">
            <Button
              v-tooltip.bottom="{
                value: !hasPermission(PermissionEnum.DownloadLinks) ? $t('common.tooltip.action-not-permission') : '',
              }"
              :class="{ 'no-permission': !hasPermission(PermissionEnum.DownloadLinks) }"
              :disabled="!hasPermission(PermissionEnum.DownloadLinks)"
              severity="download"
              class="btn-exportdata"
              @click="exportFile()"
            >
              <template #icon>
                <span class="p-button-icon">
                  <SvgIcon :src="DownloadIcon" />
                </span>
              </template>
            </Button>
          </div>
        </div>
      </div>
    </div>

    <div ref="refDataTableWrapper" class="panel-body">
      <AppDataTable
        v-model:query="dataFilters.dataTableQuery"
        class="datatable-PaymentLink table-nowrap"
        :value="data"
        :total-records="dataTotal"
        :loading="fetchDataTask.isRunning || setPinTask.isRunning"
        scrollable
        :scroll-height="heightDataTable"
        table-style="min-width: 50rem"
        @query-change="fetchData"
        @row-click="onDataTableRowClick"
      >
        <!-- <Column field="id" header="Id"></Column> -->
        <Column header="#" header-style="width: 42px">
          <template #body="slotProps">
            <span>{{ slotProps.index + (dataFilters.dataTableQuery.start ?? 0) + 1 }}</span>
          </template>
        </Column>
        <!-- Column-Pin: Content:20px = width:20px -->
        <Column field="isPinned" header="" class="px-0" header-style="width: 1.25rem">
          <template #body="slotProps">
            <SvgIcon
              v-if="slotProps.data.isPinned"
              :src="StarCheckedIcon"
              class="ml-auto user-clickable tdPaymentLinkFavoriteIcon checked"
              @click.stop="setPin(slotProps.data, false)"
            />
            <SvgIcon
              v-else
              :src="StarUnCheckIcon"
              class="ml-auto user-clickable tdPaymentLinkFavoriteIcon unchecked"
              @click.stop="setPin(slotProps.data, true)"
            />
          </template>
        </Column>
        <Column field="name" :header="$t('common.link-name')">
          <template #body="slotProps">
            <div class="d-flex align-items-center">
              <div v-tooltip.bottom="slotProps.data.name" class="flex-fill text-truncate" style="width: 12rem">
                {{ slotProps.data.name }}
              </div>
            </div>
          </template>
        </Column>
        <Column field="routerUri" :header="$t('common.link-url')">
          <template #body="slotProps">
            <div class="d-flex align-items-center">
              <div
                v-tooltip.bottom="getPaymentLinkFullRouterUri(slotProps.data.routerUri)"
                class="flex-fill text-truncate"
                style="width: 7rem"
                :data-routerUri="slotProps.data.routerUri"
              >
                {{ formatDispayRouterUri(slotProps.data) }}
              </div>
            </div>
          </template>
        </Column>
        <!-- Column-CopyRouter: Content:20px = width:20px -->
        <Column field="routerUri" header="" class="px-0" header-style="width: 1.25rem">
          <template #body="slotProps">
            <SvgIcon
              :src="CopyIcon"
              size="1.25rem"
              class="user-clickable"
              @click.stop="copyLinkUri(getPaymentLinkFullRouterUri(slotProps.data.routerUri))"
            />
          </template>
        </Column>
        <!-- Column-LinkType(Multi-use/One-time): Content:54px + padding:16px*2 = width:82px -->
        <Column field="linkType" :header="$t('page-LinkManagement.link-type')" header-style="width: 5.375rem">
          <template #body="slotProps">
            <div :data-linkType="slotProps.data.linkType">
              {{ formatLinkType(slotProps.data.linkType) }}
            </div>
          </template>
        </Column>
        <!-- Column-Money(9,999,999,999.99 USD): Content:136px + padding:16px*2 = max-width:168px -> min-width: 128px -->
        <Column field="paymentAmount" :header="$t('common.amount')" class="text-right" header-style="width: 8rem">
          <template #body="slotProps">
            <div :data-currency="slotProps.data.currency">
              {{ formatCurrency(slotProps.data.totalAmount, slotProps.data.currency, { valueIfZero: "-" }) }}
            </div>
          </template>
        </Column>
        <!-- Column-DateTime: Content:128px + padding:16px*2 = width:160px -->
        <Column field="lastModifyDate" :header="$t('common.data-table.last-updated')" header-style="">
          <template #body="slotProps">
            <div :data-timezone="formatTimeZone(slotProps.data.lastModifyDate)">
              {{ formatDateTime(slotProps.data.lastModifyDate) }}
            </div>
          </template>
        </Column>
        <!-- Column-Status: Content:105px + padding:16px*2 = width:160px -->
        <Column field="status" :header="$t('common.data-table.status')" header-style="">
          <template #body="slotProps">
            <div
              v-tooltip.bottom="{
                value: formatPaymentLinkStatus(slotProps.data).tooltip,
                class: 'tooltip-PaymentLinkStatus',
              }"
              class="tdPaymentLinkStatus"
              :class="formatPaymentLinkStatus(slotProps.data).itemClass"
            >
              {{ formatPaymentLinkStatus(slotProps.data).label }}
            </div>
          </template>
        </Column>
        <!-- Column-Actions: items-width:36px*2 + gap:12px*1 + padding-right:10px = 94px -->
        <Column body-class="p-col-actions" header-style="width: 94px">
          <template #body="slotProps">
            <div class="d-inline-block">
              <Button
                v-tooltip.bottom="{
                  value: !hasPermission(PermissionEnum.EditLink)
                    ? $t('common.tooltip.action-not-permission')
                    : $t('common.edit'),
                }"
                :class="{ 'no-permission': !hasPermission(PermissionEnum.EditLink) }"
                :disabled="!hasPermission(PermissionEnum.EditLink)"
                icon="pi pi-edit"
                rounded
                severity="secondary"
                @click.stop="editItem(slotProps.data)"
              ></Button>
              <Button
                v-tooltip.top="$t('common.download-qr')"
                icon="pi pi-download-qrcode"
                rounded
                severity="secondary"
                @click.stop="openDownloadQrDialog(slotProps.data)"
              ></Button>
            </div>
          </template>
        </Column>
      </AppDataTable>
    </div>
  </div>

  <RouterView v-slot="{ Component, route }">
    <component :is="Component" :key="route.fullPath"></component>
  </RouterView>

  <Lazy>
    <DownloadQrDialog v-model:visible="isShowDownloadQrDialog" :value="dataDownloadQrDialog" />
    <!-- <AddNew v-if="isShowAddNewDialog" :visible="isShowAddNewDialog" @close-popup-add="onCloseAddNewPopup" /> -->
    <!-- <Edit
      v-if="toggleShowEditPopup"
      :payment-id="paymentId"
      :visible="toggleShowEditPopup"
      @close-popup-edit="onCloseEditPopup"
    /> -->
    <AddSuccessPayment
      v-if="isShowSuccessDialog"
      v-model="modelSuccess"
      :visible="isShowSuccessDialog"
      @close-popup-success="toggleShowSuccessPopup(false)"
    />
    <!-- <LinkDetail
      v-model:visible="isShowDetailDialog"
      :link-id="dataDetailDialog"
      @edit="(id) => editItem(id)"
      @close="fetchData"
    ></LinkDetail> -->
    <Dialog
      v-model:visible="isShowExportFileDialog"
      class="dialog-download-export"
      modal
      :header="$t('page-LinkManagement.export-file.title')"
    >
      <!-- Tạm bỏ theo yc của TA do chức năng quản lý file đang download mình chưa code -->
      <!-- <div class="dialog-download-export-text">
        {{ $t("page-LinkManagement.export-file.running-export") }}
      </div> -->
      <div class="dialog-download-export-progress">
        <ProgressBar mode="indeterminate"></ProgressBar>
      </div>
    </Dialog>
  </Lazy>
</template>

<style lang="scss">
.page-linkmanagement {
  display: flex;
  flex-direction: column;
  height: 100%;

  .panel-header {
    padding: 20px 24px 12px;
    border-bottom: 1px solid #e6e6e6;
  }

  .panel-body {
    padding: 16px 24px;

    flex: 1 1 auto;
    overflow: auto;
  }

  .panel-header-title {
    display: flex;
    margin-bottom: 16px;
  }

  .panel-header-body {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .panel-filter {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    gap: 0rem;
    width: 100%;

    .filter-item {
      display: flex;
      flex-direction: column;
      justify-content: center;

      .filter-item-header {
        display: inline-flex;
        align-items: center;
        gap: var(--layout-spacing-spacing-xxs, 4px);

        color: var(--text-tertiary, #6c6c6c);
        /* Inter/B3/12_Regular */
        font-size: var(--Typeface-size-sm, 0.75rem);
        font-style: normal;
        font-weight: 400;
        line-height: 1rem; /* 133.333% */

        > svg {
          color: inherit;
          stroke: currentColor;
          transition: transform 0.3s;
        }
      }

      &.show-menu {
        .filter-item-header,
        .filter-item-button {
          color: var(--text-info, #2e6be5);
        }
        .filter-item-header {
          > svg {
            transform: rotate(180deg);
          }
        }
      }
    }

    .filter-actions {
      display: inline-flex;
      gap: 1rem;

      color: #2e6be5;
    }

    .filter-item--keywords {
      width: 100%;
      max-width: 15.625rem;
      @media (min-width: 1441px) {
        max-width: 20rem;
      }
    }
  }

  .btn-clearfilter,
  .btn-morefilter {
    margin-left: -1rem;
    margin-right: -1rem;
  }

  .datatable-PaymentLink {
    .p-datatable-tbody > tr:not(:hover) {
      .tdPaymentLinkFavoriteIcon.unchecked {
        visibility: hidden;
      }
    }

    .p-col-actions {
      padding-left: 0;
      padding-right: 0.625rem;
    }

    .tdPaymentLinkStatus {
      &.paymentLink-Status-Inactive {
        color: var(--text-body, #404040);
      }
      &.paymentLink-Status-Expired,
      &.paymentLink-Status-LimitReached {
        color: var(--text-danger, #d82039);
      }
      &.paymentLink-Status-Active {
        color: var(--text-success, #00bf5f);
      }
    }
  }
}

.dialog-download-export {
  width: 30rem;

  .p-dialog-header {
    justify-content: center;

    .p-dialog-header-icons {
      display: none;
    }
  }

  .dialog-download-export-text {
    font-size: var(--Typeface-size-md, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 1.25rem; /* 142.857% */
  }
  .dialog-download-export-progress {
    margin-top: 1.5rem;

    .p-progressbar {
      height: 0.5rem;
    }
  }
}

.tooltip-PaymentLinkStatus {
  width: 10rem;
}

.single-select-panel {
  .p-overlaypanel-content {
    padding: 12px;

    .link-type-option {
      padding: 8px;
      border-radius: 4px;

      &:not(:last-child) {
        margin-bottom: 8px;
      }

      &:hover {
        background-color: #f5f5f5;
      }
    }
  }
}

.link-type-focus {
  background: rgba(59, 130, 246, 0.24);
}

.filter-item--linkstatus {
  max-width: 89px;
}
</style>
