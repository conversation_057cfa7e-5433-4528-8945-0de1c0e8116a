<script setup lang="ts">
import DataNotFoundIcon from "@assets/icons/data-not-found.svg"
import ReloadIcon from "@assets/icons/reload.svg"
import TrashIcon from "@assets/icons/trash-alt.svg"

import { GetListRoleInput, GetListRoleItem } from "@/apis/types/roleTypes"
import APPCONFIG from "@/appConfig"
import { AppRouteNames } from "@/enums/routers"
import { roleManagementEventBusKey } from "@/events/eventBus"
import { useConfirmDialogService } from "@/services/confirmService"
import { useNavigatorService } from "@/services/navigatorService"
import { useRoleAndPermissionService } from "@/services/roleAndPermissionService"
import { useToastService } from "@/services/toastService"
import { IPagedResult } from "@/types/base"
import { useDebounceFn, useEventBus, useResizeObserver } from "@vueuse/core"
import { onActivated, onMounted, reactive, ref, watch } from "vue"
import { useAsyncTask } from "vue-concurrency"
import { useRoute, useRouter } from "vue-router"

const props = withDefaults(
  defineProps<{
    showDialog?: string
  }>(),
  {}
)

const route = useRoute()
const router = useRouter()
const eventBus = useEventBus(roleManagementEventBusKey)
const navigatorService = useNavigatorService()
const roleService = useRoleAndPermissionService()
const toastService = useToastService()
const confirmDialogService = useConfirmDialogService()

const { getListRoleTask, deleteRoleTask } = roleService

const getDataFiltersDefault = (): GetListRoleInput => {
  return {
    // Filter props
    name: "",
    // IPagedRequest props
    start: 0,
    length: APPCONFIG.DEFAULT_TABLE_ITEM_PER_PAGE,
  }
}
const resetDataFiltersToDefault = () => {
  for (const [key, value] of Object.entries(getDataFiltersDefault())) {
    dataFilters[key] = value
  }
}
const dataFilters = reactive(getDataFiltersDefault())
const dataResult = ref<IPagedResult<any>>({ data: [], total: 0 })

const fetchDataTask = useAsyncTask(async (signal, data: void) => {
  const result = await getListRoleTask.perform(dataFilters)

  dataResult.value = {
    data:
      result.data?.map((item) => {
        return {
          id: item.id,
          name: item.name,
          description: item.description,
          permissions: item.permissions?.map((permission) => permission.name).join(", "),
        }
      }) ?? [],
    total: result.total,
  }

  return result
}).keepLatest()
const fetchData = useDebounceFn(() => fetchDataTask.perform())

watch(dataFilters, (dataFilters) => fetchData(), {
  deep: true,
})

const fetchViewDataTask = useAsyncTask(async (signal, data: void) => {
  // TODO: Load view
}).keepLatest()
const fetchViewData = useDebounceFn(() => fetchViewDataTask.perform())

// trigger now
onMounted(() => {
  fetchViewData()
  fetchData()
})
onActivated(() => {
  fetchViewData()
  fetchData()
})

const refDataTableWrapper = ref<HTMLElement>()
const heightDataTable = ref<string>()
useResizeObserver(refDataTableWrapper, (entries) => {
  const entry = entries[0]
  const { height } = entry.contentRect
  const value = height - 61 - 5 //61: is paginator component. 5: is buffer

  heightDataTable.value = value > 400 ? `${value}px` : ""
})

function onClickClearFilter() {
  resetDataFiltersToDefault()
}

function onChangeStatus(id: any, active: any) {}

function onClickAddRole() {
  router.push({
    name: AppRouteNames.ADMIN_ROLEMANAGEMENT_ADD,
    params: {
      roleId: undefined,
      mode: "Add",
    },
  })
}

async function onClickDeleteRole(item: GetListRoleItem) {
  const result = await confirmDialogService.confirm({
    message: "Do you want to delete this record?",
  })
  if (result) {
    await deleteRoleTask.perform({ id: item.id })

    toastService.success({
      summary: "Delete success",
    })

    await fetchData()
  }
}

function onClickEditRole(item: GetListRoleItem) {
  router.push({
    name: AppRouteNames.ADMIN_ROLEMANAGEMENT_EDIT,
    params: {
      roleId: item.id,
      mode: "Edit",
    },
  })
}
</script>

<template>
  <div class="page-RoleManagement panel">
    <div class="panel-header">
      <div class="panel-header-title">
        <div class="page-title">{{ $t("page-RoleManagement.page-title") }}</div>
        <div class="mx-auto"></div>
        <Button severity="create" :label="$t('common.buttons.create')" @click="onClickAddRole" />
      </div>

      <div class="panel-header-body">
        <div class="panel-filter">
          <div class="filter-item filter-item--keywords">
            <InputTextFilterClearable
              v-model:model-value="dataFilters.name"
              placeholder="Filter"
            />
          </div>
          <div class="divider divider-vertical"></div>
          <div class="filter-item filter-item--clearfilter">
            <Button class="btn-clearfilter" label="Clear filter" text @click="onClickClearFilter()">
              <template #icon>
                <span class="p-button-icon-left">
                  <SvgIcon :src="ReloadIcon" />
                </span>
              </template>
            </Button>
          </div>
          <div class="divider divider-vertical invisible mr-0"></div>
          <div class="filter-actions ml-auto">
            <!-- <Button severity="download" class="btn-exportdata" @click="exportFile()">
                  <template #icon>
                    <span class="p-button-icon">
                      <SvgIcon :src="DownloadIcon" />
                    </span>
                  </template>
                </Button> -->
          </div>
        </div>
      </div>
    </div>

    <div ref="refDataTableWrapper" class="panel-body">
      <AppDataTable
        :query="dataFilters"
        :value="fetchDataTask.last?.value?.data"
        :total-records="fetchDataTask.last?.value?.total"
        :loading="fetchDataTask.isRunning"
        scrollable
        :scroll-height="heightDataTable"
        table-style="min-width: 50rem"
        @query-change="(q) => Object.assign(dataFilters, q)"
      >
        <template #empty>
          <div class="panel-no-data-found">
            <SvgIcon :src="DataNotFoundIcon" />
          </div>
        </template>

        <!-- <Column field="id" header="Id"></Column> -->
        <Column header="#" header-style="width: 42px">
          <template #body="slotProps">
            <span>{{ slotProps.index + (dataFilters.start ?? 0) + 1 }}</span>
          </template>
        </Column>
        <Column field="name" header="Name">
          <template #body="slotProps">
            <div class="text-nowrap">
              {{ slotProps.data.name }}
            </div>
          </template>
        </Column>
        <Column field="description" header="Description">
          <template #body="slotProps">
            <div class="text-nowrap">
              {{ slotProps.data.description }}
            </div>
          </template>
        </Column>
        <Column field="permissions" header="Permissions">
          <template #body="slotProps">
            <div 
              v-tooltip.bottom="{
                value: slotProps.data.permissions?.map((r) => r.name).join('\n'),
                class: 'tooltip-permissions',
              }"
              class="text-truncate"
              style="max-width: 31.25rem"
            >
              {{ slotProps.data.permissions?.map((r) => r.name).join(", ") }}
            </div>
          </template>
        </Column>
        <Column body-class="p-col-actions">
          <template #body="slotProps">
            <div class="d-inline-block text-nowrap">
              <Button
                v-tooltip.bottom="$t('common.buttons.remove')"
                icon="pi pi-delete"
                rounded
                severity="secondary"
                :loading="deleteRoleTask.isRunning"
                @click.stop="onClickDeleteRole(slotProps.data)"
              >
                <template #icon>
                  <span class="p-button-icon-left">
                    <SvgIcon :src="TrashIcon" />
                  </span>
                </template>
              </Button>
              <Button
                v-tooltip.bottom="$t('common.edit')"
                icon="pi pi-edit"
                rounded
                severity="secondary"
                @click.stop="onClickEditRole(slotProps.data)"
              ></Button>
            </div>
          </template>
        </Column>
      </AppDataTable>
    </div>
  </div>
</template>

<style lang="scss">
.page-RoleManagement {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;

  .panel-header {
    padding: 20px 24px 12px;
    border-bottom: 1px solid #e6e6e6;
  }

  .panel-body {
    padding: 16px 24px;

    flex: 1 1 auto;
    overflow: auto;
  }

  .panel-header-title {
    display: flex;
    margin-bottom: 16px;
  }

  .panel-header-body {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .panel-filter {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    gap: 0rem;
    width: 100%;

    .filter-item {
      display: flex;
      flex-direction: column;
      justify-content: center;

      .filter-item-header {
        display: inline-flex;
        align-items: center;
        gap: var(--layout-spacing-spacing-xxs, 4px);

        color: var(--text-tertiary, #6c6c6c);
        /* Inter/B3/12_Regular */
        font-size: var(--Typeface-size-sm, 0.75rem);
        font-style: normal;
        font-weight: 400;
        line-height: 1rem; /* 133.333% */

        > svg {
          color: inherit;
          stroke: currentColor;
          transition: transform 0.3s;
        }
      }

      &.show-menu {
        .filter-item-header,
        .filter-item-button {
          color: var(--text-info, #2e6be5);
        }
        .filter-item-header {
          > svg {
            transform: rotate(180deg);
          }
        }
      }
    }

    .filter-actions {
      display: inline-flex;
      gap: 1rem;

      color: #2e6be5;
    }

    .filter-item--keywords {
      max-width: 15.625rem;
    }
  }
}
.tooltip-permissions {
  max-width: 500px;
}
</style>
