<script setup lang="ts">
import { PermissionGroupItem, PermissionItem } from "@/apis/types/permissionTypes"
import { EditRoleInput, GetDetailRoleOutput } from "@/apis/types/roleTypes"
import { PermissionSimpleInfo } from "@/apis/userApis"
import APPCONFIG from "@/appConfig"
import { EnumUserPermissionType } from "@/app.const"
import { AppRouteNames } from "@/enums/routers"
import { roleDetailEventBusKey } from "@/events/eventBus"
import { useConfirmDialogService } from "@/services/confirmService"
import { useNavigatorService } from "@/services/navigatorService"
import { useRoleAndPermissionService } from "@/services/roleAndPermissionService"
import { useToastService } from "@/services/toastService"
import { useDebounceFn, useEventBus } from "@vueuse/core"
import { useAsyncValidator } from "@vueuse/integrations/useAsyncValidator"
import { Rules } from "async-validator"
import { computed, onMounted, ref, watch } from "vue"
import { useAsyncTask } from "vue-concurrency"
import { useI18n } from "vue-i18n"
import { useRouter } from "vue-router"

const props = withDefaults(
  defineProps<{
    roleId: uuid | undefined
    mode: "Add" | "Edit"
  }>(),
  {
    roleId: undefined,
  }
)

const isAddMode = computed(() => props.mode == "Add" && !props.roleId)
const isEditMode = computed(() => props.mode == "Edit" && !!props.roleId)

const { t } = useI18n()
const router = useRouter()
const eventBus = useEventBus(roleDetailEventBusKey)
const navigatorService = useNavigatorService()
const toastService = useToastService()
const confirmDialogService = useConfirmDialogService()
const roleAndPermissionService = useRoleAndPermissionService()

const role = ref<GetDetailRoleOutput>()
const rolePermissions = ref<PermissionSimpleInfo[]>([])

const formData = ref<EditRoleInput>()

const permissions = ref<PermissionItem[]>([])
const permissionGroups = ref<PermissionGroupItem[]>([])

//#region FormData

watch(
  [role, rolePermissions],
  ([role, rolePermissions]) => {
    if (!role) return
    formData.value = {
      id: role.id,
      name: role.name,
      description: role.description,
      permissions: rolePermissions
        .map((rp) => {
          return {
            id: rp.id,
            name: rp.name,
          }
        })
        .removeDuplicateKeepFirstByKey(JSON.stringify),
    }
  },
  {
    deep: true,
  }
)

const rules = computed<Rules>(() => {
  return {
    name: [
      {
        required: true,
      },
      {
        asyncValidator: useDebounceFn(async (rule, value) => {
          if (!value || value.length < 0) return

          const checkRoleExistResult = (await roleAndPermissionService.checkRoleExistTask.perform({
            id: props.roleId,
            name: value,
          }))

          return checkRoleExistResult.exist
            ? Promise.reject(t("components-paymentLink-LinkInfomation.validate-input.existed-url"))
            : Promise.resolve()
        }, APPCONFIG.DEBOUNCED_DELAY_INPUT),
      },
      {
        pattern: /[a-zA-Z0-9_-]+/gi,
        message: () => t("components-paymentLink-LinkInfomation.validate-input.invalid-url"),
      },
    ],
  }
})
const { pass: passValidate, isFinished: isValidateFinished, errorFields, execute: executeValidate } = useAsyncValidator(formData, rules)

//#endregion

//#region Tasks

const fetchDataViewTask = useAsyncTask(async (signal, data: void) => {
  const getAllPermissionResult = await roleAndPermissionService.getAllPermissionTask.perform({})
  permissions.value = getAllPermissionResult.permissions
  permissionGroups.value = getAllPermissionResult.permissionGroups
}).keepLatest()

const fetchDataTask = useAsyncTask(async (signal, data: void) => {
  if (isAddMode.value) {
    role.value = {
      id: "",
      name: "",
      description: "",
      permissions: [],
    }
    return role.value
  }
  if (!props.roleId) return {} as GetDetailRoleOutput

  const result = await roleAndPermissionService.getDetailRoleTask.perform({
    id: props.roleId,
  })

  console.log("fetchDataTask", result)

  role.value = result
  rolePermissions.value = result.permissions ?? []

  return result
}).keepLatest()
const fetchData = () => fetchDataTask.perform()

const addOrEditRoleTask = useAsyncTask(async (signal, data: void) => {
  const requestData = formData.value

  if (!requestData) {
    toastService.error({
      summary: "No data to process",
    })
    return
  }

  const validateResult = await executeValidate()
  if (!validateResult.pass) return

  if (isAddMode.value) {
    const result = await roleAndPermissionService.addRoleTask.perform(requestData)

    toastService.success({
      summary: "Add successful",
    })

    await router.push({
      name: AppRouteNames.ADMIN_ROLEMANAGEMENT_EDIT,
      params: {
        roleId: result.id,
        mode: "Edit",
      },
    })
  } else if (isEditMode.value) {
    const result = await roleAndPermissionService.editRoleTask.perform(requestData)

    toastService.success({
      summary: "Edit successful",
    })

    await fetchData()
  }
})

//#endregion Tasks

onMounted(async () => {
  // WARN: Chỉ chạy khi sử dụng KeepAlive
  console.log("RoleDetail onMounted", props.roleId)

  await Promise.all([fetchDataViewTask.perform(), fetchData()])
})

async function onClickCancel() {
  await router.push({
    name: AppRouteNames.ADMIN_ROLEMANAGEMENT,
  })
}

async function onClickReload() {
  //layoutService.reload()
  navigatorService.refresh()
}

async function onClickDelete() {
  if (!props.roleId) return

  const result = await confirmDialogService.confirm({
    message: "Do you want to delete this record?",
  })
  if (result) {
    await roleAndPermissionService.deleteRoleTask.perform({ id: props.roleId })

    toastService.success({
      summary: "Delete success",
    })

    await router.push({
      name: AppRouteNames.ADMIN_ROLEMANAGEMENT,
    })
  }
}

async function onClickSave() {
  await addOrEditRoleTask.perform()
}

//#region

function onChangeRolePermissions(permission: PermissionItem, checked: boolean) {
  if (checked) {
    // Add role
    rolePermissions.value.push({
      id: permission.id,
      name: permission.name,
      type: EnumUserPermissionType.RolePermission,
    })
  } else {
    // Remove role
    rolePermissions.value = rolePermissions.value.filter((up) => up.id != permission.id)
  }
}
function getPermissionsOfGroups(permissionGroupItem: PermissionGroupItem | undefined) {
  const permissionsOfGroup = permissionGroupItem
    ? permissions.value.filter((p) => p.permissionGroupId == permissionGroupItem.id)
    : permissions.value.filter((p) => permissionGroups.value.all((pg) => pg.id != p.permissionGroupId))
  return permissionsOfGroup
}
function getStateOfPermissionGroups(permissionGroupItem: PermissionGroupItem | undefined) {
  //.all(p => rolePermissions.any(up => up.id == p.id))
  const permissionsOfGroup = getPermissionsOfGroups(permissionGroupItem)

  if (permissionsOfGroup.all((p) => rolePermissions.value.any((up) => up.id == p.id)))
    return true // có chứa tất cả
  else if (permissionsOfGroup.any((p) => rolePermissions.value.any((up) => up.id == p.id)))
    return false // chỉ chứa một phần
  else return null // không có chứa
}
function onChangeStateOfPermissionGroups(
  permissionGroupItem: PermissionGroupItem | undefined,
  checked: Nullable<boolean>
) {
  const permissionsOfGroup = getPermissionsOfGroups(permissionGroupItem)

  if (checked) {
    permissionsOfGroup.forEach((permission) => {
      rolePermissions.value.push({
        id: permission.id,
        name: permission.name,
        type: EnumUserPermissionType.UserPermission,
      })
    })
  } else {
    const permissionIdsOfGroup = permissionsOfGroup.map((p) => p.id)
    rolePermissions.value = rolePermissions.value.filter((up) => permissionIdsOfGroup.indexOf(up.id) == -1)
  }
}
//#endregion
</script>

<template>
  <AsyncContent :task="fetchDataTask">
    <div class="page-UserDetail panel" v-if="role">
      <div class="panel-header">
        <div class="panel-header-title">
          <div class="page-title">
            {{
              isAddMode
                ? $t("page-RoleDetail.page-title-add")
                : $t("page-RoleDetail.page-title-edit", { roleName: role.name })
            }}
          </div>
          <div class="mx-auto"></div>
          <div class="d-inline-flex gap-3">
            <Button severity="cancel" :label="$t('common.buttons.cancel')" @click="onClickCancel"></Button>
            <Button v-if="isEditMode" severity="danger" :label="$t('common.buttons.delete')" @click="onClickDelete"></Button>
            <Button severity="help" :label="$t('common.buttons.reload')" @click="onClickReload"></Button>
            <Button
              severity="create"
              :label="$t('common.buttons.save')"
              :loading="addOrEditRoleTask.isRunning"
              :disabled="!passValidate || !isValidateFinished"
              @click="onClickSave"
            ></Button>
          </div>
        </div>
      </div>

      <div class="panel-body d-flex h-100">
        <div class="flex-grow-1">
          <form class="">
            <div class="mb-3">
              <label for="staticEmail" class="col-form-label">{{ $t('page-RoleDetail.fields.name') }}</label>
              <div class="align-self-center">
                <InputTextClearable v-model="role.name" />
                <div v-if="errorFields?.name?.length" text-red>
                  {{ errorFields.name[0].message }}
                </div>
              </div>
            </div>
            <div class="mb-3">
              <label for="staticEmail" class="col-form-label">{{ $t('page-RoleDetail.fields.description') }}</label>
              <div class="align-self-center">
                <Textarea v-model="role.description" class="w-100"></Textarea>
                <div v-if="errorFields?.description?.length" text-red>
                  {{ errorFields.description[0].message }}
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="divider divider-vertical"></div>
        <div class="flex-grow-1">
          <div class="groupPanel-permissions border">
            <template v-for="pg in permissionGroups" :key="pg.id">
              <div class="panel-permissionGroups">
                <span>{{ pg.name }}</span>
                <TriStateCheckbox
                  :model-value="getStateOfPermissionGroups(pg)"
                  @update:model-value="(checked) => onChangeStateOfPermissionGroups(pg, checked)"
                >
                  <!-- <template #nullableicon></template> -->
                </TriStateCheckbox>
              </div>
              <template v-for="p in getPermissionsOfGroups(pg)" :key="p.id">
                <div class="panel-permissions">
                  <span>{{ p.name }}</span>
                  <Checkbox
                    :model-value="rolePermissions.any((up) => up.id == p.id)"
                    :binary="true"
                    @update:model-value="(checked) => onChangeRolePermissions(p, checked)"
                  ></Checkbox>
                </div>
              </template>
            </template>
            <template v-if="getPermissionsOfGroups(undefined).length > 0">
              <div class="panel-permissionGroups">
                <span>Others</span>
                <TriStateCheckbox
                  :model-value="getStateOfPermissionGroups(undefined)"
                  @update:model-value="(checked) => onChangeStateOfPermissionGroups(undefined, checked)"
                >
                  <!-- <template #nullableicon></template> -->
                </TriStateCheckbox>
              </div>
              <template v-for="p in getPermissionsOfGroups(undefined)" :key="p.id">
                <div class="panel-permissions">
                  <span>{{ p.name }}</span>
                  <Checkbox
                    :model-value="rolePermissions.any((up) => up.id == p.id)"
                    :binary="true"
                    @update:model-value="(checked) => onChangeRolePermissions(p, checked)"
                  ></Checkbox>
                </div>
              </template>
            </template>
            <div v-if="permissionGroups.length == 0 && permissions.length == 0">NO DATA FOUND!!!</div>
          </div>
        </div>
      </div>
    </div>
  </AsyncContent>
</template>

<style lang="scss">
.page-UserDetail {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;

  .panel-header {
    padding: 20px 24px 12px;
    border-bottom: 1px solid #e6e6e6;
  }

  .panel-body {
    padding: 16px 24px;

    flex: 1 1 auto;
    overflow: auto;
  }

  .panel-header-title {
    display: flex;
    //margin-bottom: 16px;
  }

  .panel-header-body {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .panel-permissionGroups {
    background: #e6e6e6;
    padding: 4px 8px;

    display: flex;
    justify-content: space-between;
  }
  .panel-permissions {
    background: #f5f5f5;
    padding: 4px 8px;

    display: flex;
    justify-content: space-between;
  }
}
</style>
