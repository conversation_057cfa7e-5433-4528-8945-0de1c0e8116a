<script setup lang="ts">
import DataNotFoundIcon from "@assets/icons/data-not-found.svg"
import ReloadIcon from "@assets/icons/reload.svg"

import { GetListUserInput, GetListUserItem } from "@/apis/userApis"
import APPCONFIG from "@/appConfig"
import { AppRouteNames } from "@/enums/routers"
import { useMerchantProfileService } from "@/services/merchantProfileService"
import { useNavigatorService } from "@/services/navigatorService"
import { useToastService } from "@/services/toastService"
import { useUserService } from "@/services/userService"
import { IPagedResult, SelectListItem } from "@/types/base"
import { formatDateTime } from "@/utils/formatUtil"
import { useDebounceFn, useResizeObserver } from "@vueuse/core"
import { onActivated, onMounted, reactive, ref, watch } from "vue"
import { useAsyncTask } from "vue-concurrency"
import { useRoute, useRouter } from "vue-router"

const props = withDefaults(
  defineProps<{
    showDialog?: string
  }>(),
  {}
)

const route = useRoute()
const router = useRouter()
const toastService = useToastService()
const navigatorService = useNavigatorService()
const userService = useUserService()
const merchantProfileService = useMerchantProfileService()

const { getListUserTask, activeUserTask } = userService

const getDataFiltersDefault = (): GetListUserInput => {
  return {
    // Filter props
    keywords: "",
    merchantProfile: null,
    status: 0,
    // IPagedRequest props
    start: 0,
    length: APPCONFIG.DEFAULT_TABLE_ITEM_PER_PAGE,
  }
}
const resetDataFiltersToDefault = () => {
  for (const [key, value] of Object.entries(getDataFiltersDefault())) {
    dataFilters[key] = value
  }
}
const dataFilters = reactive(getDataFiltersDefault())
const dataResult = ref<IPagedResult<any>>({ data: [], total: 0 })

const fetchDataTask = useAsyncTask(async (signal, data: void) => {
  const result = await getListUserTask.perform(dataFilters)

  dataResult.value = {
    data:
      result.data?.map((item) => {
        return item
      }) ?? [],
    total: result.total,
  }

  return result
}).keepLatest()
const fetchData = useDebounceFn(async () => await fetchDataTask.perform())

watch(dataFilters, (dataFilters) => fetchData(), {
  deep: true,
})

const listMerchantProfile = ref<SelectListItem[]>([])
const listStatus = ref<SelectListItem[]>([])

const fetchViewDataTask = useAsyncTask(async (signal, data: void) => {
  // Load merchants
  const getListMerchantProfileResult = await merchantProfileService.getListMerchantProfileTask.perform({})
  listMerchantProfile.value = getListMerchantProfileResult.data.map((m) => {
    return {
      label: m.name,
      value: m.id,
    }
  })

  listStatus.value = [
    {
      label: "All",
      value: 0,
    },
    {
      label: "Active",
      value: 1,
    },
    {
      label: "InActive",
      value: 2,
    },
  ]
}).keepLatest()
const fetchViewData = useDebounceFn(async () => await fetchViewDataTask.perform())

// trigger now
onMounted(() => {
  fetchViewData()
  fetchData()
})
onActivated(() => {
  fetchViewData()
  fetchData()
})

// const bus = useEventBus<string>("USERMANAGEMENT")
// bus.on(async (event, data) => {
//   await nextTick()
//   console.log("UserManagerment bus on", event, data)

//   if (event == "cancel") visibleDialogFaker.value = true
// })
// const visibleDialogFaker = ref(false)

const refDataTableWrapper = ref<HTMLElement>()
const heightDataTable = ref<string>()
useResizeObserver(refDataTableWrapper, (entries) => {
  const entry = entries[0]
  const { height } = entry.contentRect
  const value = height - 61 - 5 //61: is paginator component. 5: is buffer

  heightDataTable.value = value > 400 ? `${value}px` : ""
})

async function onClickClearFilter() {
  resetDataFiltersToDefault()

  await fetchData()
}

async function onChangeUserStatus(item: GetListUserItem) {
  await activeUserTask.perform({
    id: item.id,
    active: !item.active,
  })

  toastService.success({
    summary: "Update user status success",
  })

  fetchData()
}

function onClickEditUser(item: GetListUserItem) {
  router.push({
    name: AppRouteNames.ADMIN_USERMANAGEMENT_EDIT,
    params: {
      userSId: item.userSId,
    },
  })
}
</script>

<template>
  <div class="page-UserManagement panel">
    <div class="panel-header">
      <div class="panel-header-title">
        <div class="page-title">{{ $t("page-UserManagement.page-title") }}</div>
        <div class="mx-auto"></div>
        <!-- <Button severity="create" :label="'Create'" /> -->
      </div>

      <div class="panel-header-body">
        <div class="panel-filter">
          <div class="filter-item filter-item--keywords">
            <InputTextFilterClearable
              v-model="dataFilters.keywords"
              placeholder="Filter"
            />
          </div>
          <div class="divider divider-vertical"></div>
          <div class="filter-item filter-item--merchants" style="width: 160px">
            <FloatLabel class="w-100 md:w-20rem">
              <MultiSelect
                v-model="dataFilters.merchantProfile"
                :options="listMerchantProfile"
                option-value="value"
                option-label="label"
                show-clear
                filter
                :max-selected-labels="3"
                class="w-100"
              >
                <template #option="slotProps">
                  <div class="multiSelect-MerchantProfile-item">
                    <div class="text-truncate">{{ slotProps.option.label }}</div>
                  </div>
                </template>
              </MultiSelect>
              <label for="ms-cities">Merchant profile</label>
            </FloatLabel>
          </div>
          <div class="divider divider-vertical"></div>
          <div class="filter-item filter-item--status" style="width: 150px">
            <FloatLabel class="w-100">
              <Dropdown
                v-model="dataFilters.status"
                :options="listStatus"
                option-value="value"
                option-label="label"
                :max-selected-labels="3"
                class="w-100"
              />
              <label for="ms-cities">Status</label>
            </FloatLabel>
          </div>
          <div class="divider divider-vertical"></div>
          <div class="filter-item filter-item--clearfilter">
            <Button class="btn-clearfilter" label="Clear filter" text @click="onClickClearFilter()">
              <template #icon>
                <span class="p-button-icon-left">
                  <SvgIcon :src="ReloadIcon" />
                </span>
              </template>
            </Button>
          </div>
          <div class="divider divider-vertical invisible mr-0"></div>
          <div class="filter-actions ml-auto">
            <!-- <Button severity="download" class="btn-exportdata" @click="exportFile()">
                  <template #icon>
                    <span class="p-button-icon">
                      <SvgIcon :src="DownloadIcon" />
                    </span>
                  </template>
                </Button> -->
          </div>
        </div>
      </div>
    </div>

    <div ref="refDataTableWrapper" class="panel-body">
      <AppDataTable
        :query="dataFilters"
        :value="fetchDataTask.last?.value?.data"
        :total-records="fetchDataTask.last?.value?.total"
        :loading="fetchDataTask.isRunning"
        scrollable
        :scroll-height="heightDataTable"
        table-style="min-width: 50rem"
        @query-change="(q) => Object.assign(dataFilters, q)"
      >
        <template #empty>
          <div class="panel-no-data-found">
            <SvgIcon :src="DataNotFoundIcon" />
          </div>
        </template>

        <!-- <Column field="id" header="Id"></Column> -->
        <Column header="#" header-style="width: 42px">
          <template #body="slotProps">
            <span>{{ slotProps.index + (dataFilters.start ?? 0) + 1 }}</span>
          </template>
        </Column>
        <Column field="email" header="Username">
          <template #body="slotProps">
            <div class="text-nowrap" :class="{ 'text-secondary': !slotProps.data.userSId }">
              {{ slotProps.data.email }}
            </div>
          </template>
        </Column>
        <Column field="userSId" header="SID">
          <template #body="slotProps">
            <div class="text-nowrap">
              {{ slotProps.data.userSId }}
            </div>
          </template>
        </Column>
        <Column field="fullName" header="Full name">
          <template #body="slotProps">
            <div class="text-nowrap">
              {{ slotProps.data.fullName }}
            </div>
          </template>
        </Column>
        <Column field="roles" header="Roles">
          <template #body="slotProps">
            <div 
              v-tooltip.bottom="{
                value: slotProps.data.roles?.map((r) => r.name).join('\n'),
                class: 'tooltip-merchants',
              }"
              class="text-truncate"
              style="width: 14rem"
            >
              {{ slotProps.data.roles?.map((r) => r.name).join(", ") }}
            </div>
          </template>
        </Column>
        <Column field="merchants" header="Merchant profiles">
          <template #body="slotProps">
            <div
              v-tooltip.bottom="{
                value: slotProps.data.merchants?.map((r) => r.name).join('\n'),
                class: 'tooltip-merchants',
              }"
              class="text-truncate"
              style="width: 14rem"
            >
              {{ slotProps.data.merchants?.map((r) => r.name).join(", ") }}
            </div>
          </template>
        </Column>
        <Column field="lastModifyDate" :header="$t('common.data-table.last-updated')">
          <template #body="slotProps">
            <div class="text-nowrap">
              {{ formatDateTime(slotProps.data.lastModifyDate) }}
            </div>
          </template>
        </Column>
        <Column field="status" :header="$t('common.data-table.status')">
          <template #body="slotProps">
            <InputSwitch :model-value="slotProps.data.active" @change="onChangeUserStatus(slotProps.data)" />
          </template>
        </Column>
        <Column body-class="p-col-actions">
          <template #body="slotProps">
            <div class="d-inline-block text-nowrap">
              <Button
                v-tooltip.bottom="$t('common.edit')"
                icon="pi pi-edit"
                rounded
                severity="secondary"
                :disabled="!slotProps.data.userSId"
                @click.stop="onClickEditUser(slotProps.data)"
              ></Button>
            </div>
          </template>
        </Column>
      </AppDataTable>
    </div>
  </div>
</template>

<style lang="scss">
.page-UserManagement {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;

  .panel-header {
    padding: 20px 24px 12px;
    border-bottom: 1px solid #e6e6e6;
  }

  .panel-body {
    padding: 16px 24px;

    flex: 1 1 auto;
    overflow: auto;
  }

  .panel-header-title {
    display: flex;
    margin-bottom: 16px;
  }

  .panel-header-body {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .panel-filter {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    gap: 0rem;
    width: 100%;

    .filter-item {
      display: flex;
      flex-direction: column;
      justify-content: center;

      .filter-item-header {
        display: inline-flex;
        align-items: center;
        gap: var(--layout-spacing-spacing-xxs, 4px);

        color: var(--text-tertiary, #6c6c6c);
        /* Inter/B3/12_Regular */
        font-size: var(--Typeface-size-sm, 0.75rem);
        font-style: normal;
        font-weight: 400;
        line-height: 1rem; /* 133.333% */

        > svg {
          color: inherit;
          stroke: currentColor;
          transition: transform 0.3s;
        }
      }

      &.show-menu {
        .filter-item-header,
        .filter-item-button {
          color: var(--text-info, #2e6be5);
        }
        .filter-item-header {
          > svg {
            transform: rotate(180deg);
          }
        }
      }
    }

    .filter-actions {
      display: inline-flex;
      gap: 1rem;

      color: #2e6be5;
    }

    .filter-item--keywords {
      max-width: 15.625rem;
    }
  }
}

.tooltip-merchants,
.tooltip-roles {
  max-width: 500px;
}

.multiSelect-MerchantProfile-item {
  max-width: 500px;
}
</style>
