<script setup lang="ts">
import { MerchantProfileItem } from "@/apis/types/merchantTypes"
import { PermissionGroupItem, PermissionItem } from "@/apis/types/permissionTypes"
import { GetListRoleItem as RoleItem, RoleSimpleInfo } from "@/apis/types/roleTypes"
import { EditUserInput, GetUserOutput, MerchantSimpleInfo, PermissionSimpleInfo, UserSimpleInfo } from "@/apis/userApis"
import { EnumUserPermissionType } from "@/app.const"
import { AppRouteNames } from "@/enums/routers"
import { useLayoutService } from "@/services/layoutService"
import { useMerchantProfileService } from "@/services/merchantProfileService"
import { useNavigatorService } from "@/services/navigatorService"
import { useRoleAndPermissionService } from "@/services/roleAndPermissionService"
import { useToastService } from "@/services/toastService"
import { useUserService } from "@/services/userService"
import { urlHash } from "@/utils/urlHash"
import { useEventBus } from "@vueuse/core"
import { computed, nextTick, onMounted, ref, watch } from "vue"
import { useAsyncTask } from "vue-concurrency"
import { useRouter } from "vue-router"

const props = withDefaults(
  defineProps<{
    userSId: uuid | undefined
    mode: "Add" | "Edit"
  }>(),
  {
    userSId: undefined,
    mode: "Edit", // NOTE: chưa hỗ trợ add
  }
)
const isAddMode = computed(() => props.mode == "Add" && !props.userSId)
const isEditMode = computed(() => props.mode == "Edit" && !!props.userSId)

const router = useRouter()
const navigatorService = useNavigatorService()
const layoutService = useLayoutService()
const toastService = useToastService()
const userService = useUserService()
const roleAndPermissionService = useRoleAndPermissionService()
const merchantProfileService = useMerchantProfileService()

const { refresh } = navigatorService
const { getUserBySIdTask } = userService

const user = ref<UserSimpleInfo>()
const userMerchants = ref<MerchantSimpleInfo[]>([])
const userRoles = ref<RoleSimpleInfo[]>([])
/**
 * Permission của user (type == EnumUserPermissionType.UserPermission == 1)
 * 
 * Cần lọc ngay từ getById
 */
const userPermissions = ref<PermissionSimpleInfo[]>([])

const formData = ref<EditUserInput>()

const merchants = ref<MerchantProfileItem[]>([])
const roles = ref<RoleItem[]>([])
const permissions = ref<PermissionItem[]>([])
const permissionGroups = ref<PermissionGroupItem[]>([])

const merchantsPickList = ref<[MerchantProfileItem[], MerchantProfileItem[]]>([[], []])
const rolesPickList = ref<[RoleItem[], RoleItem[]]>([[], []])

const bus = useEventBus<string>("USERMANAGEMENT")

//#region Merchants

let isMerchantsPickListUpdating = false
watch([merchants, userMerchants], ([merchants, userMerchants]) => {
  isMerchantsPickListUpdating = true
  merchantsPickList.value = [
    [
      ...merchants
        .filter((r) => userMerchants.map((ur) => ur.id).indexOf(r.id) == -1)
        .sort((a, b) => a.name.localeCompare(b.name)),
    ],
    [...userMerchants.sort((a, b) => a.name.localeCompare(b.name))],
  ]
  nextTick(() => (isMerchantsPickListUpdating = false))
})

// update ngược merchantsPickList -> userMerchants
watch(
  () => merchantsPickList.value[1],
  (merchantsPicked) => {
    if (!isMerchantsPickListUpdating) {
      userMerchants.value = merchantsPicked.map((m) => {
        return {
          id: m.id,
          name: m.name,
          pin: false,
        }
      })
    }
  },
  {
    deep: true,
  }
)

//#endregion Merchants

//#region Roles

let isRolesPickListUpdating = false
watch([roles, userRoles], ([roles, userRoles]) => {
  isRolesPickListUpdating = true
  rolesPickList.value = [
    [
      ...roles
        .filter((r) => userRoles.map((ur) => ur.id).indexOf(r.id) == -1)
        .sort((a, b) => a.name.localeCompare(b.name)),
    ],
    [...userRoles.sort((a, b) => a.name.localeCompare(b.name))],
  ]
  nextTick(() => (isRolesPickListUpdating = false))
})

// update ngược rolesPickList -> userRoles
watch(
  () => rolesPickList.value[1],
  (rolesPicked) => {
    if (!isRolesPickListUpdating) {
      userRoles.value = rolesPicked
    }
  },
  {
    deep: true,
  }
)

//#endregion Roles

//#region Permissions

//#endregion Permissions

//#region FormData

watch(
  [user, userMerchants, userRoles, userPermissions],
  ([user, userMerchants, userRoles, userPermissions]) => {
    if (!user) return
    formData.value = {
      id: user.id,
      active: user.active,
      merchants: userMerchants
        .map((um) => {
          return {
            id: um.id,
            name: um.name,
          }
        })
        .removeDuplicateKeepFirstByKey(JSON.stringify),
      roles: userRoles
        .map((ur) => {
          return {
            id: ur.id,
            name: ur.name,
          }
        })
        .removeDuplicateKeepFirstByKey(JSON.stringify),
      permissions: userPermissions
        .map((up) => {
          return {
            id: up.id,
            name: up.name,
          }
        })
        .removeDuplicateKeepFirstByKey(JSON.stringify),
    }
  },
  {
    deep: true,
  }
)

//#endregion

onMounted(async () => {
  // WARN: Chỉ chạy khi sử dụng KeepAlive
  console.log("UserDetail onActivated", props.userSId)

  await Promise.all([fetchDataViewTask.perform(), fetchData()])
})

async function onClickCancel() {
  await router.push({
    name: AppRouteNames.ADMIN_USERMANAGEMENT,
  })
}

async function onClickReload() {
  layoutService.reload()
}

async function onClickSave() {
  await editUserTask.perform()
}

const fetchDataViewTask = useAsyncTask(async (signal, data: void) => {
  // Load roles
  const getListRoleResult = await roleAndPermissionService.getListRoleTask.perform({
    start: -1,
    length: -1,
  })
  roles.value = getListRoleResult.data

  // Load permissions
  const getAllPermissionResult = await roleAndPermissionService.getAllPermissionTask.perform({})
  permissions.value = getAllPermissionResult.permissions
  permissionGroups.value = getAllPermissionResult.permissionGroups

  // Load merchants
  const getListMerchantProfileResult = await merchantProfileService.getListMerchantProfileTask.perform({})
  merchants.value = getListMerchantProfileResult.data
}).keepLatest()

const fetchDataTask = useAsyncTask(async (signal, data: void) => {
  if (!props.userSId) return {} as GetUserOutput

  // Load user
  const result = await getUserBySIdTask.perform(props.userSId)

  user.value = result.user
  userMerchants.value = result.merchants ?? []
  userRoles.value = result.roles ?? []
  userPermissions.value = (result.permissions ?? []).filter(p => p.type != EnumUserPermissionType.RolePermission)

  return result
}).keepLatest()
const fetchData = () => fetchDataTask.perform()

const editUserTask = useAsyncTask(async (signal, data: void) => {
  const requestData = formData.value

  if (!requestData) {
    toastService.error({
      summary: "No data to process",
    })
    return
  }

  await userService.editUserTask.perform(requestData)

  toastService.success({
    summary: "Edit successful",
  })

  await fetchData()
})

//#region Control View tabs & type

enum EnumViewTab {
  TabMerchantProfile = 0,
  TabPermission = 1,
}

const tabActiveIndex = ref<number>(Number(urlHash.getUrlHashParameter("tab") ?? EnumViewTab.TabMerchantProfile))
watch(tabActiveIndex, (tabActiveIndex) => urlHash.setUrlHashParameter("tab", String(tabActiveIndex)))

enum EnumViewType {
  ViewByRole = 0,
  ViewByFunction = 1,
}

const viewTypeSelected = ref<number>(Number(urlHash.getUrlHashParameter("type") ?? EnumViewType.ViewByRole))
watch(viewTypeSelected, (viewTypeSelected) => urlHash.setUrlHashParameter("type", String(viewTypeSelected)))

//#endregion Control View type

function onChangeUserPermissions(permission: PermissionItem, checked: boolean) {
  if (checked) {
    userPermissions.value.push({
      id: permission.id,
      name: permission.name,
      type: EnumUserPermissionType.UserPermission,
    })
  } else {
    userPermissions.value = userPermissions.value.filter((up) => up.id != permission.id)
  }
}
function getPermissionsOfGroups(permissionGroupItem: PermissionGroupItem | undefined) {
  const permissionsOfGroup = permissionGroupItem
    ? permissions.value.filter((p) => p.permissionGroupId == permissionGroupItem.id)
    : permissions.value.filter((p) => permissionGroups.value.all((pg) => pg.id != p.permissionGroupId))
  return permissionsOfGroup
}
function getStateOfPermissionGroups(permissionGroupItem: PermissionGroupItem | undefined) {
  const permissionsOfGroup = getPermissionsOfGroups(permissionGroupItem)

  if (permissionsOfGroup.all((p) => userPermissions.value.any((up) => up.id == p.id)))
    return true // có chứa tất cả
  else if (permissionsOfGroup.any((p) => userPermissions.value.any((up) => up.id == p.id)))
    return false // chỉ chứa một phần
  else return null // không có chứa
}
function onChangeStateOfPermissionGroups(
  permissionGroupItem: PermissionGroupItem | undefined,
  checked: Nullable<boolean>
) {
  const permissionsOfGroup = getPermissionsOfGroups(permissionGroupItem)

  if (checked) {
    permissionsOfGroup.forEach((permission) => {
      userPermissions.value.push({
        id: permission.id,
        name: permission.name,
        type: EnumUserPermissionType.UserPermission,
      })
    })
  } else {
    const permissionIdsOfGroup = permissionsOfGroup.map((p) => p.id)
    userPermissions.value = userPermissions.value.filter((up) => permissionIdsOfGroup.indexOf(up.id) == -1)
  }
}
</script>

<template>
  <AsyncContent :task="fetchDataTask">
    <div class="page-UserDetail panel" v-if="user">
      <div class="panel-header">
        <div class="panel-header-title">
          <div class="page-title">
            {{
              isAddMode
                ? $t("page-UserDetail.page-title-add")
                : $t("page-UserDetail.page-title-edit", { userName: user.email })
            }}
          </div>
          <div class="mx-auto"></div>
          <div class="d-inline-flex gap-3">
            <Button severity="cancel" :label="$t('common.buttons.cancel')" @click="onClickCancel"></Button>
            <Button
              severity="create"
              :label="$t('common.buttons.save')"
              :loading="editUserTask.isRunning"
              @click="onClickSave"
            ></Button>
          </div>
        </div>

        <div class="panel-header-body">
          <form class="flex-fill">
            <div class="row">
              <label for="staticEmail" class="col-3 col-form-label">{{ $t("page-UserDetail.fields.email") }}</label>
              <div class="col-9 align-self-center">
                {{ user.email }}
              </div>
            </div>
            <div class="row">
              <label for="staticEmail" class="col-3 col-form-label">{{ $t("page-UserDetail.fields.status") }}</label>
              <div class="col-9 align-self-center">
                <InputSwitch v-model="user.active" />
              </div>
            </div>
          </form>
        </div>
      </div>

      <div class="panel-body">
        <TabView v-model:active-index="tabActiveIndex">
          <TabPanel header="Merchant Profile">
            <PickList
              v-model:model-value="merchantsPickList"
              data-key="id"
              :show-source-controls="false"
              :show-target-controls="false"
              list-style="height:342px"
              breakpoint="400px"
            >
              <template #sourceheader>{{ $t("page-UserDetail.fields.sourceHeader") }}</template>
              <template #targetheader>{{ $t("page-UserDetail.fields.targetHeader") }}</template>
              <template #item="slotProps">
                <div class="picklist-item text-truncate" v-tooltip.left="slotProps.item.name">
                  {{ slotProps.item.name }}
                </div>
              </template>
            </PickList>
          </TabPanel>
          <TabPanel header="Permission">
            <div class="mb-3">
              <div class="d-flex flex-row gap-3">
                <div>{{ $t("page-UserDetail.fields.selectPermissionType") }}</div>
                <div class="d-flex flex-column gap-3">
                  <div class="d-flex align-items-center">
                    <RadioButton
                      v-model:model-value="viewTypeSelected"
                      input-id="rdbViewByRole"
                      name="viewType"
                      :value="EnumViewType.ViewByRole"
                    />
                    <label for="rdbViewByRole" class="ml-2">By Role</label>
                  </div>
                  <div class="d-flex align-items-center">
                    <RadioButton
                      v-model:model-value="viewTypeSelected"
                      input-id="rdbViewByFunction"
                      name="viewType"
                      :value="EnumViewType.ViewByFunction"
                    />
                    <label for="rdbViewByFunction" class="ml-2">By Function</label>
                  </div>
                </div>
              </div>
            </div>

            <div v-if="viewTypeSelected == EnumViewType.ViewByRole">
              <PickList
                v-model:model-value="rolesPickList"
                data-key="id"
                :show-source-controls="false"
                :show-target-controls="false"
                list-style="height:342px"
                breakpoint="400px"
              >
                <template #sourceheader>{{ $t("page-UserDetail.fields.sourceHeader") }}</template>
                <template #targetheader>{{ $t("page-UserDetail.fields.targetHeader") }}</template>
                <template #item="slotProps">
                  <div class="picklist-item text-truncate" v-tooltip.left="slotProps.item.name">
                    {{ slotProps.item.name }}
                  </div>
                </template>
              </PickList>
            </div>
            <div v-else-if="viewTypeSelected == EnumViewType.ViewByFunction">
              <div class="groupPanel-permissions border">
                <template v-for="pg in permissionGroups" :key="pg.id">
                  <div class="panel-permissionGroups">
                    <span>{{ pg.name }}</span>
                    <TriStateCheckbox
                      :model-value="getStateOfPermissionGroups(pg)"
                      @update:model-value="(checked) => onChangeStateOfPermissionGroups(pg, checked)"
                    >
                      <!-- <template #nullableicon></template> -->
                    </TriStateCheckbox>
                  </div>
                  <template v-for="p in getPermissionsOfGroups(pg)" :key="p.id">
                    <div class="panel-permissions">
                      <span>{{ p.name }}</span>
                      <Checkbox
                        :model-value="userPermissions.any((up) => up.id == p.id)"
                        :binary="true"
                        @update:model-value="(checked) => onChangeUserPermissions(p, checked)"
                      ></Checkbox>
                    </div>
                  </template>
                </template>
                <template v-if="getPermissionsOfGroups(undefined).length > 0">
                  <div class="panel-permissionGroups">
                    <span>Others</span>
                    <TriStateCheckbox
                      :model-value="getStateOfPermissionGroups(undefined)"
                      @update:model-value="(checked) => onChangeStateOfPermissionGroups(undefined, checked)"
                    >
                      <!-- <template #nullableicon></template> -->
                    </TriStateCheckbox>
                  </div>
                  <template v-for="p in getPermissionsOfGroups(undefined)" :key="p.id">
                    <div class="panel-permissions">
                      <span>{{ p.name }}</span>
                      <Checkbox
                        :model-value="userPermissions.any((up) => up.id == p.id)"
                        :binary="true"
                        @update:model-value="(checked) => onChangeUserPermissions(p, checked)"
                      ></Checkbox>
                    </div>
                  </template>
                </template>
                <div v-if="permissionGroups.length == 0 && permissions.length == 0">NO DATA FOUND!!!</div>
              </div>
            </div>
          </TabPanel>
        </TabView>
      </div>
    </div>
  </AsyncContent>
</template>

<style lang="scss">
.page-UserDetail {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;

  .panel-header {
    padding: 20px 24px 12px;
    border-bottom: 1px solid #e6e6e6;
  }

  .panel-body {
    padding: 16px 24px;

    flex: 1 1 auto;
    overflow: auto;
  }

  .panel-header-title {
    display: flex;
    margin-bottom: 16px;
  }

  .panel-header-body {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  // FIX bug của PickList component
  .p-picklist {
    .p-icon {
      min-width: 1rem; // fix bug ko hiểu sao base nó ko hiện
    }
  }
  .p-picklist-list-wrapper {
    max-width: 500px;
  }
  .p-picklist-item {
    overflow: hidden;
  }

  .panel-permissionGroups {
    background: #e6e6e6;
    padding: 4px 8px;

    display: flex;
    justify-content: space-between;
  }
  .panel-permissions {
    background: #f5f5f5;
    padding: 4px 8px;

    display: flex;
    justify-content: space-between;
  }
}
</style>
