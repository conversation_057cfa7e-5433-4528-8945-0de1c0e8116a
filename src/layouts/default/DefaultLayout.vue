<script setup lang="ts">
import { useLayoutService } from "@/services/layoutService"
import { breakpointsBootstrapV5, refDebounced, useWindowSize } from "@vueuse/core"
import { storeToRefs } from "pinia"
import { computed, ref } from "vue"

import AppFooter from "./AppFooter.vue"
import AppHeader from "./AppHeader.vue"
import { useAuthService } from "@/services/authService"

defineOptions({
  name: "DefaultLayout",
  inheritAttrs: false,
})

var authService = useAuthService()
var layoutService = useLayoutService()

const { selectMerchantProfileTask } = authService
const { layoutReloadKey } = storeToRefs(layoutService)

console.log(`Default layout is called`)

const { width: windowWidth } = useWindowSize()
const windowWidthDebounced = refDebounced(windowWidth, 50)

const mainMaxWidth = computed(() => {
  const windowWidth = windowWidthDebounced.value
  const value = windowWidth > breakpointsBootstrapV5.lg ? windowWidth : breakpointsBootstrapV5.lg
  return value > 0 ? `${value}px` : ""
})
</script>

<template>
  <div class="tms-layout">
    <header class="layout-header">
      <div class="layout-header-container">
        <AppHeader></AppHeader>
      </div>
    </header>

    <div class="layout-body">
      <main class="layout-body-main">
        <div class="layout-body-main-container">
          <ErrorBoundary>
            <RouterView v-slot="{ Component }">
              <Suspense>
                <template #default>
                  <component :is="Component" :key="`${layoutReloadKey}`"></component>
                </template>

                <template #fallback>
                  <div class="w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                    <AppProgressSpinner></AppProgressSpinner>
                  </div>
                </template>
              </Suspense>
            </RouterView>
          </ErrorBoundary>
        </div>
      </main>
    </div>

    <footer class="layout-footer">
      <div class="layout-footer-container">
        <AppFooter></AppFooter>
      </div>
    </footer>

    <BlockUI full-screen :blocked="selectMerchantProfileTask.isRunning"></BlockUI>
  </div>
</template>

<style lang="scss">
#app {
  display: flex;
}
.tms-layout {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;

  .layout-header .layout-header-container {
    height: 3.5rem;
    border-bottom: 1px solid var(--border-tertiary, #e6e6e6);
  }

  .layout-body {
    flex: 1 1 auto;
    overflow: auto;

    .layout-body-main {
      width: 100%;
      height: 100%;

      .layout-body-main-container {
        height: 100%;
        overflow: auto;
        display: flex;
        flex-direction: column;
        padding: 2rem;
        background-color: #F5F5F5;
      }
    }
  }

  .layout-footer .layout-footer-container {
    height: 1.5rem;
    border-top: 1px solid var(--border-tertiary, #e6e6e6);
  }


  .layout-body {
    flex: 1 1 auto;
    overflow: auto;

    .layout-body-main {
      width: 100%;
      height: 100%;

      .layout-body-main-container {
        height: 100%;
        overflow: auto;
        display: flex;
        flex-direction: column;
        padding: 2rem;
        background-color: #F5F5F5;
      }
    }
  }

  .layout-footer .layout-footer-container {
    height: 1.5rem;
    border-top: 1px solid var(--border-tertiary, #e6e6e6);
  }


}
</style>