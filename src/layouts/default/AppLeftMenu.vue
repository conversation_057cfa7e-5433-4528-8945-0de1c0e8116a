<script setup lang="ts">
import CurrencySettingIcon from "@assets/icons/currency-setting.svg"
import CurrencySettingIconDisabled from "@assets/icons/currency-setting-disabled.svg"
import LeftMenuSwitch from "@assets/icons/left-menu-show-config.svg"
import DashboardIcon from "@assets/icons/leftmenu-dashboard.svg"
import LinkIcon from "@assets/icons/leftmenu-link.svg"
import ReportIcon from "@assets/icons/leftmenu-report.svg"
import TransactionIcon from "@assets/icons/leftmenu-transaction.svg"
import SwitchProfile from "@assets/icons/switch-profile.svg"

import { PermissionEnum } from "@/enums/permissions"
import { AppRouteNames } from "@/enums/routers"
import { useAuthService } from "@/services/authService"
import { useToastService } from "@/services/toastService"
import { getImgSrc } from "@/utils/appUtil"
import { useToggle } from "@vueuse/core"
import { storeToRefs } from "pinia"
import type OverlayPanel from "primevue/overlaypanel"
import { computed, ref } from "vue"
import { useI18n } from "vue-i18n"
import { useLayoutService } from "@/services/layoutService"

// To test left menu await
//await new Promise((resolve) => setTimeout(resolve, 2000))

const { t } = useI18n()
const toastService = useToastService()
const authService = useAuthService()
const layoutService = useLayoutService()

const { hasPermission } = authService
const { authMerchantProfile } = storeToRefs(authService)
const { leftMenuCollapsed } = storeToRefs(layoutService)

const merchantProfileOfUser = computed(() => {
  const m = authMerchantProfile.value
  return {
    id: m?.id,
    icon: getImgSrc(m?.logoFileId),
    label: m?.name,
  }
})

const checkDisabledCurrencyConfig = computed(() => {
  return authMerchantProfile.value?.defaultCurrency === "USD"
})
const isFocus = ref(false)
const opSwitch = ref<InstanceType<typeof OverlayPanel> | null>(null)
const openSwitchPanel = (event: Event) => {
  event.preventDefault()
  event.stopPropagation()
  opSwitch.value?.toggle(event)
}
const onShow = () => {
  isFocus.value = true
}
const onHide = () => {
  isFocus.value = false
}

const [visibleMerchantSelecter, toggleVisibleMerchantSelecter] = useToggle()
function openMerchantSelectorSidebar() {
  toggleVisibleMerchantSelecter(true)
  opSwitch.value?.hide()
}

const [visibleCurrencySetting, toggleVisibleCurrencySetting] = useToggle()
const openCurrencyConfig = () => {
  if (!hasPermission(PermissionEnum.CurrencySetting)) {
    toastService.error({
      summary: t("common.toast.action-not-permission"),
    })
    return
  }

  toggleVisibleCurrencySetting(true)
  opSwitch.value?.hide()
}

console.log("APP LEFT MENU")
</script>

<template>
  <div role="menu" class="layout-menu-left">
    <!-- MECHANISM MENU ITEMS -->
    <ul class="navbar-nav navbar-nav-menu-merchan-current">
      <li class="nav-item not-hover-this">
        <button
          v-tooltip="{
            class: 'merchaint-name-tooltip',
            value: merchantProfileOfUser?.label ?? '',
          }"
          class="nav-link btn-nav-bar"
          role="button"
          aria-controls="offcanvasExample"
          @click="openMerchantSelectorSidebar()"
        >
          <!-- <component :is="selectedMerchan.icon" class="nav-item-icon"></component> -->
          <!-- <SvgIcon :src="selectedMerchan.icon" class="nav-item-icon" /> -->
          <!-- <AppSvgNamedIcon :name="merchantProfileOfUser?.icon" area="merchans" class="nav-item-icon" /> -->
          <img :src="merchantProfileOfUser?.icon" alt="" class="nav-item-icon icon-merchant-logo" />
          <span class="nav-item-text text-truncate" style="width: 154px">{{ merchantProfileOfUser?.label }}</span>
          <div
            class="nav-item-icon-right icon-switch-panel"
            :class="{ 'icon-switch-panel-focus': isFocus }"
            @click="openSwitchPanel"
          >
            <SvgIcon :src="LeftMenuSwitch" />
          </div>
        </button>

        <OverlayPanel
          ref="opSwitch"
          class="panel-switch"
          @show="onShow"
          @hide="onHide"
        >
          <div class="all-switch">
            <div
              v-tooltip.bottom="{
                value: !hasPermission(PermissionEnum.CurrencySetting) ? $t('common.tooltip.action-not-permission') : '',
              }"
              :class="{
                'no-permission': !hasPermission(PermissionEnum.CurrencySetting),
                'disabled-action': checkDisabledCurrencyConfig,
              }"
              :disabled="!hasPermission(PermissionEnum.CurrencySetting)"
              class="currency-setting left-menu-swich"
              @click="() => !hasPermission(PermissionEnum.CurrencySetting) || openCurrencyConfig()"
            >
              <SvgIcon
                :src="!hasPermission(PermissionEnum.CurrencySetting) || checkDisabledCurrencyConfig ? CurrencySettingIconDisabled : CurrencySettingIcon"
              />
              <div class="label">{{ $t("component-app-left-menu.currency-setting") }}</div>
            </div>

            <div class="switch-profile left-menu-swich" @click="openMerchantSelectorSidebar()">
              <SvgIcon :src="SwitchProfile" />
              <div class="label">{{ $t("component-app-left-menu.switch-profile") }}</div>
            </div>
          </div>
        </OverlayPanel>
      </li>
    </ul>
    <!-- LEFT MENU ITEMS -->
    <ul class="navbar-nav navbar-nav-menu-left">
      <li class="nav-item">
        <router-link
          v-tooltip="{
            disabled: leftMenuCollapsed == false,
            value: $t('component-app-left-menu.dashboard'),
          }"
          :to="{ name: AppRouteNames.DASHBOARD }"
          class="nav-link"
        >
          <!-- <DashboardIcon class="nav-item-icon" /> -->
          <SvgIcon :src="DashboardIcon" class="nav-item-icon" />
          <span class="nav-item-text">{{ $t("component-app-left-menu.dashboard") }}</span>
        </router-link>
      </li>
      <li class="nav-item">
        <router-link
          v-tooltip="{
            disabled: leftMenuCollapsed == false,
            value: $t('component-app-left-menu.payment-link'),
          }"
          :to="{ name: AppRouteNames.PAYMENTLINK_LIST }"
          class="nav-link"
        >
          <!-- <LinkIcon class="nav-item-icon" /> -->
          <SvgIcon :src="LinkIcon" class="nav-item-icon" />
          <span class="nav-item-text">{{ $t("component-app-left-menu.payment-link") }}</span>
        </router-link>
      </li>
      <li class="nav-item">
        <router-link
          v-tooltip="{
            disabled: leftMenuCollapsed == false,
            value: $t('component-app-left-menu.trans-management'),
          }"
          :to="{ name: AppRouteNames.TRANSACTIONMANAGEMENT_LIST }"
          class="nav-link"
        >
          <!-- <TransactionIcon class="nav-item-icon" /> -->
          <SvgIcon :src="TransactionIcon" class="nav-item-icon" />
          <span class="nav-item-text">{{ $t("component-app-left-menu.trans-management") }}</span>
        </router-link>
      </li>
      <li class="nav-item">
        <router-link
          v-tooltip="{
            disabled: leftMenuCollapsed == false,
            value: $t('component-app-left-menu.report'),
          }"
          :to="{ name: AppRouteNames.REPORT }"
          class="nav-link"
        >
          <!-- <ReportIcon class="nav-item-icon" /> -->
          <SvgIcon :src="ReportIcon" class="nav-item-icon" />
          <span class="nav-item-text">{{ $t("component-app-left-menu.report") }}</span>
        </router-link>
      </li>
    </ul>

    <!-- <div v-for="index in 1000" :key="index">LEFT MENU</div> -->
  </div>

  <MerchantSelecterSidebar v-model:visible="visibleMerchantSelecter"></MerchantSelecterSidebar>

  <CurrencySettingDialog v-model:visible="visibleCurrencySetting"></CurrencySettingDialog>
</template>

<style lang="scss">
@import "../../assets/scss/variables";

.layout-menu-left {
  display: flex;
  flex-flow: column;
  height: 100%;

  .layout-menu-collapsed & {
    .nav-item-icon {
      margin-right: 0;
    }
    .nav-item-text,
    .nav-item-icon-right {
      display: none;
    }

    .navbar-nav-menu-merchan-current .nav-item {
      padding-left: 1.25rem;
      padding-right: 1.25rem;
    }
  }
}

.navbar-nav-menu-left,
.navbar-nav-menu-merchan-current,
.navbar-nav-menu-merchan-search,
.navbar-nav-menu-merchan-favorites,
.navbar-nav-menu-merchan-allprofiles {
  display: flex;
  flex-flow: column;
  gap: var(--layout-spacing-spacing-xs, 16px);
  padding: var(--layout-spacing-spacing-md, 12px) var(--layout-spacing-spacing-none, 0px);

  .nav-link {
    width: 100%;
    padding: 0;
    display: flex;
    align-items: center;
    color: var(--text-body, #404040);

    &.disabled {
      pointer-events: inherit;
      cursor: default;
      color: var(--text-disabled2, #bababa);
    }

    &.router-link-active {
      font-weight: 600;
      color: var(--text-headings, #1c1c1c);
    }
  }

  .nav-item {
    padding: 0.5rem 0.5rem 0.5rem 1.5rem;
    height: 2.5rem;

    &:hover:not(.no-hover),
    &:has(.nav-link.router-link-active) {
      background-color: #e6e6e6;
    }
  }

  .nav-item-icon,
  .nav-item-icon-right {
    object-fit: contain;
    width: 1.5rem;
    height: 1.5rem;
  }
  .nav-item-icon {
    margin-right: 0.5rem;
  }
  .nav-item-icon-right {
    margin-left: auto;
  }

  .nav-item-text {
    font-size: 14px;
    @include text-truncate();
  }
}

.navbar-nav-menu-merchan-current {
  border-bottom: 1px solid var(--border-tertiary, #e6e6e6);

  .nav-link {
    font-weight: 600;
    color: var(--text-headings, #1c1c1c);
  }

  .nav-item {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    font-weight: 600;
    color: var(--text-headings, #1c1c1c);
  }

  .nav-item-icon {
    padding: 4.85px;
    border-radius: var(--layout-spacing-spacing-xxs, 4px);
    border: 1px solid var(--border-primary, #dcdcdc);
  }
}

.navbar-nav-menu-merchan-search {
  gap: var(--layout-spacing-spacing-xs, 12px);

  .nav-item {
    padding: 0;
  }

  .nav-link {
    color: var(--text-headings, #1c1c1c);
  }

  .nav-item-icon {
    width: 3rem;
    height: 3rem;
  }

  .nav-item-text {
    font-size: 1rem; // up to 16px
    line-height: 1.5;
    font-weight: 600;
  }
}

.navbar-nav-menu-merchan-favorites {
  gap: var(--layout-spacing-spacing-xs, 0.5rem);

  .nav-link {
    gap: 0.5rem;
  }

  .nav-item-icon-right {
    flex: 0 0 1.25rem;
    width: 1.25rem;
    height: 1.25rem;
  }
}

.navbar-nav-menu-merchan-allprofiles {
  gap: var(--layout-spacing-spacing-xs, 0.5rem);

  .nav-link {
    gap: 0.5rem;
  }

  .nav-item-icon-right {
    display: none;
    flex: 0 0 1.25rem;
    width: 1.25rem;
    height: 1.25rem;
  }

  .nav-item:hover {
    .nav-item-icon-right {
      display: block;
    }
  }
}

.panel-switch {
  left: 80px !important;

  ::before {
    border-style: solid;
    border-color: rgba(226, 232, 240, 0);
    border-bottom-color: #d3dce8;
    border-width: 10px;
    margin-left: -10px;
    bottom: 100%;
    right: 1rem;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
  }

  ::after {
    border-style: solid;
    border-color: rgba(255, 255, 255, 0);
    border-bottom-color: #ffffff;
    border-width: 8px;
    margin-left: -8px;
    bottom: 100%;
    right: 1.1rem;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
  }

  .p-overlaypanel-content {
    padding: 12px 0 !important;
    min-width: 171px;
    max-height: 104px;

    .all-switch {
      width: 100%;
      height: 100%;

      .left-menu-swich {
        cursor: pointer;
        display: flex;
        align-items: center;
        padding: 8px 16px;

        .label {
          margin-left: 12px;
        }

        &:hover {
          background-color: #f5f5f5;
        }
      }

      .currency-setting {
        margin-bottom: 8px;
      }
    }
  }
}

.btn-nav-bar {
  .icon-merchant-logo {
    flex: 0 0 2rem;
    object-fit: contain;
    width: 2rem;
    height: 2rem;
  }
  .icon-switch-panel {
    flex: 0 0 2.25rem;
    height: 2.25rem;
    width: 2.25rem;

    display: flex;
    align-items: center;
    justify-content: center;

    margin-left: 0.5rem;

    &:hover {
      background-color: #e6e6e6;
      border-radius: 100px;
    }
  }

  &:focus {
    .icon-switch-panel-focus {
      background-color: #e6e6e6;
      border-radius: 100px;
    }
  }
}

.not-hover-this {
  &:hover {
    background-color: #f5f5f5 !important;
  }
}

.disabled-action {
  pointer-events: none;
  color: #bababa !important;
}

.merchaint-name-tooltip {
  max-width: 25rem;

  .p-tooltip-text {
    width: auto;
  }
}
</style>
