<script setup lang="ts">
import BellIcon from "@assets/icons/bell.svg"
import GridDotIcon from "@assets/icons/gridDot.svg"
import CheckedIcon from "@assets/icons/locales/checked.svg"
import LogoutIcon from "@assets/icons/logout.svg"
import QuestionIcon from "@assets/icons/question.svg"
import UserIcon from "@assets/icons/user.svg"
import LogoIcon from "@assets/logo.svg"

import { useAuthService } from "@/services/authService"
import { useLayoutService } from "@/services/layoutService"
import { useLocaleService } from "@/services/localeService"
import { storeToRefs } from "pinia"
import { computed, ref } from "vue"
import { useI18n } from "vue-i18n"
import { useToggle } from "@vueuse/core"
import OverlayPanel from "primevue/overlaypanel"
import SwitchProfile from "@assets/icons/switch-profile.svg"

const props = withDefaults(
  defineProps<{
    title?: string
  }>(),
  {
    title: undefined,
  }
)

const { t, locale: i18nLocale, availableLocales: i18nAvailableLocales } = useI18n()
const layoutService = useLayoutService()
const localeService = useLocaleService()
const authService = useAuthService()

const { leftMenuCollapsed, showLogoutWithouUser } = storeToRefs(layoutService)
const { isSysAdmin, isAuth, isNoAppPermission } = storeToRefs(authService)
const { authUserMerchantsList } = useAuthService()


const onCollapseMenuClick = () => {
  const oldValue = leftMenuCollapsed.value
  const newValue = !oldValue
  console.log(`onCollapseMenuClick: ${oldValue}->${newValue}`)
  leftMenuCollapsed.value = newValue
}

const onProfileSettingsClick = () => {
  console.warn("Chức năng đang phát triển")
}

const onLogoutClick = async () => {
  //alert("onLogoutClick")

  await authService.logoutTask.perform()

  //https://ma.onepay.vn/accounts/#/logout?continue=
  //window.location.replace(window.location.href)
}

const merchantName = computed(() => {
  if (authUserMerchantsList && authUserMerchantsList.length > 0) {
    return authUserMerchantsList[0].merchant_name
  }
})


interface ILocaleMenuItem {
  id: string
  icon: any
  label: string
  selected: boolean
}
const menuLocale = computed<ILocaleMenuItem[]>(() => {
  return i18nAvailableLocales.map((locale) => {
    return {
      id: locale,
      // icon: markRaw(defineAsyncComponent(() => import(`@assets/locales/${locale}.svg`))),
      //icon: `@assets/locales/${locale}.svg`,
      //icon: new URL(`@assets/locales/${locale}.svg`, import.meta.url).href,
      icon: locale,
      label: t(`locale.${locale}`),
      //label: appServices.locale.i18n.global.t(locale),
      selected: locale == i18nLocale.value,
    }
  })
})

const menuLocaleSelected = computed(
  () => menuLocale.value.find((locale) => locale.id == localeService.locale) ?? menuLocale.value[0]
)

const [visibleMerchantSelecter, toggleVisibleMerchantSelecter] = useToggle()
const isFocus = ref(false)
const opSwitch = ref<InstanceType<typeof OverlayPanel> | null>(null)
const openSwitchPanel = (event: Event) => {
  event.preventDefault()
  event.stopPropagation()
  toggleVisibleMerchantSelecter()
}

const onShow = () => {
  isFocus.value = true
}
const onHide = () => {
  isFocus.value = false
}


function changeLocale(locale: string) {
  localeService.setLocale(locale)
}

const userName = computed(() => authService.authUserData?.fullName || authService.authUserEmail)
const userNameFirstLetter = computed(() => authService.authUserEmail?.substring(0, 2))
const email = computed(() => authService.authUserEmail)

const feature = ref(false)

//console.log('userName', appServices.auth.userName)
</script>

<template>
  <nav class="navbar navbar-page-header navbar-expand-sm">
    <div class="container-fluid">
      <!-- <button type="button" class="btn navbar-left-menu-toggler" @click="onCollapseMenuClick()">
        <span class="navbar-toggler-icon"></span>
      </button> -->

      <RouterLink class="navbar-brand" to="/">
        <!-- <LogoIcon class="navbar-brand-img" /> -->
        <SvgIcon :src="LogoIcon" class="navbar-brand-img" width="32" />
        {{ props.title || $t("component-app-header.title") }}
      </RouterLink>
      <button @click="openSwitchPanel">
              <div class="label">{{ merchantName }}</div>
              <SvgIcon :src="SwitchProfile" />
      </button>
      <div class="mx-auto"></div>

      <div class="">
        <!-- <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarScroll"
          aria-controls="navbarScroll"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button> -->

        <div id="navbarScroll" class="navbar-collapse collapse justify-content-between">
          <ul class="navbar-nav mr-auto flex-row">
            <li v-if="feature" class="nav-item nav-item-notification mr-4">
              <a href="javascript:void(0)" class="nav-link nav-link-icon rounded-circle" style="">
                <!-- <BellIcon class="nav-item-icon" /> -->
                <SvgIcon :src="BellIcon" class="nav-item-icon" />
              </a>
            </li>
            <li v-if="feature" class="nav-item nav-item-question mr-4">
              <a href="javascript:void(0)" class="nav-link nav-link-icon rounded-circle" style="">
                <!-- <QuestionIcon class="nav-item-icon" /> -->
                <SvgIcon :src="QuestionIcon" class="nav-item-icon" />
              </a>
            </li>
            <!-- <li class="nav-item nav-item-question mr-4" v-if="isSysAdmin">
              <RouterLink to="/admin" class="nav-link rounded-circle">
                {{ $t("component-app-header.admin") }}
              </RouterLink>
            </li> -->
            <li class="nav-item nav-item-locales mr-4 dropdown">
              <button id="btnMenuLocales" class="nav-link nav-link-icon" data-bs-toggle="dropdown" aria-expanded="false">
                <!-- <component :is="menuLocaleSelected.icon" class="dropdown-item-icon"></component> -->
                <!-- <SvgIcon :src="menuLocaleSelected.icon" class="nav-item-icon" /> -->
                <AppSvgNamedIcon :name="menuLocaleSelected.icon" area="locales" class="nav-item-icon" />
              </button>
              <ul class="dropdown-menu dropdown-menu-right" aria-labelledby="btnMenuLocales">
                <li v-for="item in menuLocale" :key="item.id">
                  <button class="dropdown-item" @click="changeLocale(item.id)">
                    <!-- <component :is="item.icon" class="dropdown-item-icon"></component> -->
                    <!-- <SvgIcon :src="item.icon" class="dropdown-item-icon" /> -->
                    <AppSvgNamedIcon :name="item.icon" area="locales" class="dropdown-item-icon" />
                    <div class="dropdown-item-title">{{ item.label }}</div>
                    <div v-if="item.id == menuLocaleSelected.id" class="dropdown-item-title ml-auto pl-4">
                      <!-- <CheckedIcon /> -->
                      <SvgIcon :src="CheckedIcon" />
                    </div>
                  </button>
                </li>
              </ul>
            </li>
            <li v-if="feature" class="nav-item nav-item-apps mr-4 dropdown">
              <button
                id="btnMenuApps"
                class="nav-link nav-link-icon rounded-circle"
                data-bs-toggle="dropdown"
                aria-expanded="false"
                style="background-color: #ece9fc"
              >
                <!-- <GridDotIcon class="nav-item-icon" /> -->
                <SvgIcon :src="GridDotIcon" class="nav-item-icon" />
              </button>

              <!-- <Button icon="pi pi-apps" severity="secondary" rounded aria-label="Apps" id="btnMenuApps" class="nav-link rounded-circle" /> -->

              <ul class="dropdown-menu dropdown-menu-right" aria-labelledby="btnMenuApps">
                <li><a class="dropdown-item" href="javascript:void(0)">Action</a></li>
                <li><a class="dropdown-item" href="javascript:void(0)">Another action</a></li>
                <li><a class="dropdown-item" href="javascript:void(0)">Something else here</a></li>
              </ul>
            </li>
            <li v-if="isAuth" class="nav-item nav-item-account dropdown">
              <button
                id="btnMenuAccount"
                class="nav-link nav-link-icon rounded-circle"
                data-bs-toggle="dropdown"
                aria-expanded="false"
                style="background-color: #2e6be5; color: #fff"
              >
                <Avatar
                  class="nav-item-icon text-capitalize"
                  :label="userNameFirstLetter"
                  shape="circle"
                  style="background-color: #2e6be5; color: #fff"
                />
              </button>
              <ul class="dropdown-menu dropdown-menu-right" aria-labelledby="btnMenuAccount">
                <li>
                  <a class="dropdown-item dropdown-item-account-info" href="javascript:void(0)">
                    <Avatar
                      class="dropdown-item-icon text-capitalize"
                      :label="userNameFirstLetter"
                      shape="circle"
                      style="background-color: #2e6be5; color: #fff; margin-right: 12px"
                    />
                    <div class="dropdown-item-title">
                      <div class="dropdown-item-title-main">{{ userName }}</div>
                      <div class="dropdown-item-title-sub">{{ email }}</div>
                    </div>
                  </a>
                </li>
                <!-- <li>
                  <a class="dropdown-item" href="javascript:void(0)" @click="onProfileSettingsClick()">
                    !-- <UserIcon class="dropdown-item-icon" /> --
                    <SvgIcon :src="UserIcon" class="dropdown-item-icon" />
                    <div class="dropdown-item-title">{{ $t("component-app-header.profile-setting") }}</div>
                  </a>
                </li> -->
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a class="dropdown-item" href="javascript:void(0)" @click="onLogoutClick()">
                    <!-- <LogoutIcon class="dropdown-item-icon" /> -->
                    <SvgIcon :src="LogoutIcon" class="dropdown-item-icon" />
                    <div class="dropdown-item-title">{{ $t("component-app-header.logout") }}</div>
                  </a>
                </li>
              </ul>
            </li>
            <li v-else-if="isNoAppPermission || showLogoutWithouUser" class="nav-item nav-item-logout">
              <!-- Trường hợp đặc biệt. ko có user info nhưng vẫn logout được do cơ chế của OP MA-WEB -->
              <button id="btnMenuLogout" class="nav-link nav-link-texticon" @click="onLogoutClick()">
                <SvgIcon :src="LogoutIcon" class="nav-link-icon" />
                <div class="nav-link-text">{{ $t("component-app-header.logout") }}</div>
              </button>

              <!-- <a class="nav-link nav-link-texticon" href="javascript:void(0)" @click="onLogoutClick()">
                !-- <LogoutIcon class="dropdown-item-icon" /> --
                <SvgIcon :src="LogoutIcon" class="nav-link-icon" />
                <div class="dropdown-item-title">{{ $t("component-app-header.logout") }}</div>
              </a> -->
            </li>
          </ul>
        </div>
      </div>
    </div>
  </nav>

  <MerchantSelecterSidebar v-model:visible="visibleMerchantSelecter"></MerchantSelecterSidebar>
</template>

<style lang="scss">
.navbar-page-header {
  height: 100%;

  padding-top: var(--layout-spacing-spacing-xs, 0.5rem);
  padding-bottom: var(--layout-spacing-spacing-xs, 0.5rem);

  .container-fluid {
    padding-left: var(--layout-spacing-spacing-xxl, 1.5rem);
    padding-right: var(--layout-spacing-spacing-xxl, 1.5rem);

    button {
      border-radius: 4px;
      border: 1px solid #E6E6E6;
      display: flex;
      height: 36px;
      padding: 16px;
      align-items: center;
      gap: 8px;
    }
  }

  .navbar-left-menu-toggler {
    width: 2rem;
    height: 2rem;
    margin-left: -0.25rem; // dịch sang trái 4px để cân với icon menu trái
    margin-right: 1.25rem;
    //margin-right: calc(0.5rem + 4px); // bù lại 4px bị dịch sang trái
    padding: 0;
  }

  .navbar-brand {
    height: 2rem;
    padding: 0;
    font-size: 1rem;
    font-weight: 600;
    //line-height: 1.5;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .navbar-brand-img {
    height: 100%;
  }

  .navbar-toggler {
    margin-right: 1.25rem;
    border-radius: 0.25rem;
    border: none;
    box-shadow: none;
  }

  .nav-item {
    min-width: 2rem;
    //text-align: center;
    display: flex;
    align-items: center;

    .nav-link {
      &.nav-link-icon {
        padding: 0;
        width: 2rem;
        height: 2rem;
      }

      &.nav-link-texticon {
        padding: 0;
        height: 2rem;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
      }

      .nav-item-icon {
        width: 1.5rem;
        height: 1.5rem;
        margin: 0.25rem;
      }
    }
  }

  .dropdown-menu {
    // display: flex;
    // flex-direction: column;
    // gap: 1.5rem;
    padding: 0.5rem 0;

    .dropdown-item {
      display: flex;
      align-items: center;
      padding: 0.75rem 1.5rem;
    }

    .dropdown-divider {
      margin: 0.75rem 0;
    }
  }

  .dropdown-item-icon {
    margin-right: 0.75rem;
  }

  .nav-item-account {
    .dropdown-item-icon {
      margin-right: 0.5rem;
    }
  }

  // .dropdown-item-title-main {
  // }
  .dropdown-item-title-sub {
    color: #6c6c6c;
  }
}
</style>
