<script setup lang="ts">
import { useRoute } from "vue-router"

const route = useRoute()

defineOptions({
  inheritAttrs: false,
})

/* WARN: SHOULD NOT USE ASYNC IN LAYOUT.VUE SETUP -> Keep it simples */
</script>

<template>
  <Suspense v-if="route.meta.layoutComponent">
    <component :is="route.meta.layoutComponent"></component>

    <template #fallback>
      <div class="w-100 h-100 d-flex flex-column align-items-center justify-content-center">
        <AppProgressSpinner />
        <h1>View is loading...</h1>
      </div>
    </template>
  </Suspense>
</template>

<style lang="scss"></style>
