<script setup lang="ts">
import { useLayoutService } from "@/services/layoutService"
import { breakpointsBootstrapV5, refDebounced, useWindowSize } from "@vueuse/core"
import { storeToRefs } from "pinia"
import { computed, KeepAlive } from "vue"

import AppFooter from "../default/AppFooter.vue"
import AppHeader from "../default/AppHeader.vue"
import AdminLeftMenu from "./AdminLeftMenu.vue"
import { useRoute } from "vue-router"
import { useNavigatorService } from "@/services/navigatorService"

var layoutService = useLayoutService()
var navigatorService = useNavigatorService()
var route = useRoute()

const { getOrSetComponentKey} = navigatorService
const { listComponentInclude: keepAliveComponents, listComponentExclude: listExclude } = storeToRefs(navigatorService)
const { layoutReloadKey, leftMenuCollapsed } = storeToRefs(layoutService)

console.log(`Admin layout is called`)

const { width: windowWidth } = useWindowSize()
const windowWidthDebounced = refDebounced(windowWidth, 50)

const mainMaxWidth = computed(() => {
  const windowWidth = windowWidthDebounced.value
  const value =
    (windowWidth > breakpointsBootstrapV5.lg ? windowWidth : breakpointsBootstrapV5.lg) -
    (leftMenuCollapsed.value ? 72 : 260)
  return value > 0 ? `${value}px` : ""
})

//#region for test keepalive

// const keepAliveElm = ref<ComponentPublicInstance<typeof KeepAlive>>()

// onMounted(() => {
//   console.log('keepAliveElm', (keepAliveElm.value?.$ as any).__v_cache)
// })

//#endregion
</script>

<template>
  <div class="tms-layout--admin" :class="{ 'layout-menu-collapsed': leftMenuCollapsed }">
    <header class="layout-header">
      <div class="layout-header-container">
        <AppHeader title="Admin Panel"></AppHeader>
      </div>
    </header>

    <div class="layout-body">
      <aside class="layout-body-left">
        <div class="layout-body-left-container">
          <ErrorBoundary>
            <Suspense>
              <!-- main of left menu -->
              <AdminLeftMenu></AdminLeftMenu>

              <!-- loading state -->
              <template #fallback>
                <h4 class="text-center">MENU is loading...</h4>
              </template>
            </Suspense>
          </ErrorBoundary>
        </div>
      </aside>

      <main class="layout-body-right" :style="{ maxWidth: mainMaxWidth }">
        <div class="layout-body-right-container">
          <ErrorBoundary>
            <RouterViewKeepAlive name="AdminLayout" max="5"></RouterViewKeepAlive>
            
            <!-- <RouterView v-slot="{ Component: MainComponent, route: mainRoute }">
              <div
                class="d-none" 
                :data-key="`${mainRoute.matched.map(m => m.path).join('-')}-${layoutReloadKey}`"
              ></div>
              <Suspense>
                !-- main of page --
                <template #default>
                  <KeepAlive :max="1" :exclude="listExclude">
                    <div class="main-page-layout w-100 h-100">
                      <component :is="MainComponent" :key="mainRoute.matched[0].path"></component>
                      !-- <RouterView v-slot="{ Component: SubComponent, route: subRoute }">
                        <template v-if="SubComponent">
                          <component :is="SubComponent" :key="`${subRoute.fullPath}`"></component>
                        </template>

                        <component v-else :is="MainComponent" :key="`${mainRoute.matched[0].path}`"></component>
                      </RouterView> --
                    </div>
                  </KeepAlive>
                </template>

                !-- loading state --
                <template #fallback>
                  <div class="w-100 align-self-center d-flex justify-items-center">
                    !-- View loading --
                    <AppProgressSpinner></AppProgressSpinner>
                  </div>
                </template>
              </Suspense>
            </RouterView> -->
          </ErrorBoundary>
        </div>
      </main>
    </div>

    <footer class="layout-footer">
      <div class="layout-footer-container">
        <AppFooter></AppFooter>
      </div>
    </footer>

    <!-- <BlockUI full-screen :blocked="selectMerchantProfileTask.isRunning"></BlockUI> -->
  </div>
</template>

<style lang="scss">
#app {
  display: flex;
}
.tms-layout--admin {
  flex: 1 1 auto;

  display: flex;
  flex-direction: column;
  //padding-bottom: 2px; // trừ 1 px của header border và trừ 1px của footer border // TODO: xem có dùng được box-sizing hay gì để bỏ border-width của header footer
  //overflow-y: hidden; // dùng thử thay thế padding-bottom

  .layout-header .layout-header-container {
    height: 3.5rem; // sử dụng height của .navbar-page-header
    border-bottom: 1px solid var(--border-tertiary, #e6e6e6);
  }

  .layout-body {
    flex: 1 1 auto;
    overflow: auto; // important để hiển thị scroll trong .layout-body-right

    display: flex;
    flex-direction: row;
    align-items: stretch;

    .layout-body-left {
      flex: 0 1 auto;
      overflow: auto;

      .layout-body-left-container {
        width: 16.25rem;
        height: 100%;

        border-right: 1px solid var(--border-tertiary, #e6e6e6);
        background: var(--surface-ghost-hover, #f5f5f5);
      }
    }

    .layout-body-right {
      width: 100%;

      .layout-body-right-container {
        height: 100%;
        overflow: auto;

        position: relative;
        display: flex;
        flex-direction: column;
      }
    }
  }

  &.layout-menu-collapsed .layout-body .layout-body-left .layout-body-left-container {
    width: 4.5rem;
  }

  .layout-footer .layout-footer-container {
    height: 1.5rem;
    border-top: 1px solid var(--border-tertiary, #e6e6e6);
  }
}
</style>
