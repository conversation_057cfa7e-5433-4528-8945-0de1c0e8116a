<script setup lang="ts">
import CurrencySetting from "@assets/icons/currency-setting.svg"
import LeftMenuSwitch from "@assets/icons/left-menu-show-config.svg"
import DashboardIcon from "@assets/icons/leftmenu-dashboard.svg"
import LinkIcon from "@assets/icons/leftmenu-link.svg"
import ReportIcon from "@assets/icons/leftmenu-report.svg"
import TransactionIcon from "@assets/icons/leftmenu-transaction.svg"
import SwitchProfile from "@assets/icons/switch-profile.svg"

import { AppRouteNames } from "@/enums/routers"
import { useAuthService } from "@/services/authService"
import { useToggle } from "@vueuse/core"
import { storeToRefs } from "pinia"
import type OverlayPanel from "primevue/overlaypanel"
import { computed, ref } from "vue"
import { getImgSrc } from "@/utils/appUtil"

// To test left menu await
//await new Promise((resolve) => setTimeout(resolve, 2000))

console.log("APP LEFT MENU (ADMIN)")
</script>

<template>
  <div role="menu" class="layout-menu-left--admin">
    <!-- MECHANISM MENU ITEMS -->
    <ul class="navbar-nav navbar-nav-menu-merchan-current">
      <li class="nav-item">
        <router-link :to="{ name: AppRouteNames.DASHBOARD }" class="nav-link">
          <!-- <DashboardIcon class="nav-item-icon" /> -->
          <SvgIcon :src="DashboardIcon" class="nav-item-icon" />
          <span class="nav-item-text">Back to Home</span>
        </router-link>
      </li>
    </ul>
    <!-- LEFT MENU ITEMS -->
    <ul class="navbar-nav navbar-nav-menu-left">
      <li class="nav-item">
        <router-link :to="{ name: AppRouteNames.ADMIN_USERMANAGEMENT, cache: false }" class="nav-link">
          <!-- <DashboardIcon class="nav-item-icon" /> -->
          <SvgIcon :src="DashboardIcon" class="nav-item-icon" />
          <span class="nav-item-text">User Management</span>
        </router-link>
      </li>
      <li class="nav-item">
        <router-link :to="{ name: AppRouteNames.ADMIN_ROLEMANAGEMENT, cache: false }" class="nav-link">
          <!-- <LinkIcon class="nav-item-icon" /> -->
          <SvgIcon :src="LinkIcon" class="nav-item-icon" />
          <span class="nav-item-text">Role Management</span>
        </router-link>
      </li>
    </ul>
  </div>

</template>

<style lang="scss">
@import "../../assets/scss/variables";

.layout-menu-left--admin {
  display: flex;
  flex-flow: column;
  height: 100%;

  .layout-menu-collapsed & {
    .nav-item-icon {
      margin-right: 0;
    }
    .nav-item-text,
    .nav-item-icon-right {
      display: none;
    }

    .navbar-nav-menu-merchan-current .nav-item {
      padding-left: 1.25rem;
      padding-right: 1.25rem;
    }
  }
}

.navbar-nav-menu-left,
.navbar-nav-menu-merchan-current,
.navbar-nav-menu-merchan-search,
.navbar-nav-menu-merchan-favorites,
.navbar-nav-menu-merchan-allprofiles {
  display: flex;
  flex-flow: column;
  gap: var(--layout-spacing-spacing-xs, 16px);
  padding: var(--layout-spacing-spacing-md, 12px) var(--layout-spacing-spacing-none, 0px);

  .nav-link {
    width: 100%;
    padding: 0;
    display: flex;
    align-items: center;
    color: var(--text-body, #404040);

    &.disabled {
      pointer-events: inherit;
      cursor: default;
      color: var(--text-disabled2, #BABABA);
    }

    &.router-link-active {
      font-weight: 600;
      color: var(--text-headings, #1c1c1c);
    }
  }

  .nav-item {
    padding: 0.5rem 0.5rem 0.5rem 1.5rem;
    height: 2.5rem;

    &:hover:not(.no-hover),
    &:has(.nav-link.router-link-active) {
      background-color: #e6e6e6;
    }
  }

  .nav-item-icon,
  .nav-item-icon-right {
    width: 1.5rem;
    height: 1.5rem;
  }
  .nav-item-icon {
    margin-right: 0.5rem;
  }
  .nav-item-icon-right {
    margin-left: auto;
  }

  .nav-item-text {
    font-size: 14px;
    @include text-truncate();
  }
}

.navbar-nav-menu-merchan-current {
  border-bottom: 1px solid var(--border-tertiary, #e6e6e6);

  .nav-link {
    font-weight: 600;
    color: var(--text-headings, #1c1c1c);
  }

  .nav-item {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    font-weight: 600;
    color: var(--text-headings, #1c1c1c);
  }

  .nav-item-icon {
    padding: 4.85px;
    border-radius: var(--layout-spacing-spacing-xxs, 4px);
    border: 1px solid var(--border-primary, #dcdcdc);

    width: 2rem;
    min-width: 2rem;
    height: 2rem;
  }
}

.navbar-nav-menu-merchan-search {
  gap: var(--layout-spacing-spacing-xs, 12px);

  .nav-item {
    padding: 0;
  }

  .nav-link {
    color: var(--text-headings, #1c1c1c);
  }

  .nav-item-icon {
    width: 3rem;
    height: 3rem;
  }

  .nav-item-text {
    font-size: 1rem; // up to 16px
    line-height: 1.5;
    font-weight: 600;
  }
}

.navbar-nav-menu-merchan-favorites {
  gap: var(--layout-spacing-spacing-xs, 8px);

  .nav-item-icon-right {
    width: 1.25rem;
    height: 1.25rem;
  }
}

.navbar-nav-menu-merchan-allprofiles {
  gap: var(--layout-spacing-spacing-xs, 8px);

  .nav-item-icon-right {
    visibility: hidden;
    width: 1.25rem;
    height: 1.25rem;
  }

  .nav-item:hover {
    .nav-item-icon-right {
      visibility: visible;
    }
  }
}
</style>
