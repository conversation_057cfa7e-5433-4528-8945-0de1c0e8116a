<script setup lang="ts">
import { useLayoutService } from "@/services/layoutService"
import { breakpointsBootstrapV5, refDebounced, useWindowSize } from "@vueuse/core"
import { storeToRefs } from "pinia"
import { computed, ref } from "vue"

import AppFooter from "../default/AppFooter.vue"
import AppHeader from "../default/AppHeader.vue"
import AppLeftMenu from "../default/AppLeftMenu.vue"
import { useAuthService } from "@/services/authService"

defineOptions({
  name: "ErrorNoMenuLayout",
  inheritAttrs: false,
})

var authService = useAuthService()
var layoutService = useLayoutService()

const { selectMerchantProfileTask } = authService
const { layoutReloadKey, leftMenuCollapsed } = storeToRefs(layoutService)

console.log(`ErrorNoMenuLayout layout is called`)

const hasLeftMenu = ref(false)
const { width: windowWidth } = useWindowSize()
const windowWidthDebounced = refDebounced(windowWidth, 50)

const mainMaxWidth = computed(() => {
  if (hasLeftMenu.value == false) {
    return ""
  }
  const windowWidth = windowWidthDebounced.value
  const value =
    (windowWidth > breakpointsBootstrapV5.lg ? windowWidth : breakpointsBootstrapV5.lg) -
    (leftMenuCollapsed.value ? 72 : 260)
  return value > 0 ? `${value}px` : ""
})
</script>

<template>
  <div class="tms-layout" :class="{ 'layout-menu-collapsed': leftMenuCollapsed }">
    <header class="layout-header">
      <div class="layout-header-container">
        <AppHeader></AppHeader>
      </div>
    </header>

    <div class="layout-body">
      <aside class="layout-body-left" v-if="hasLeftMenu">
        <div class="layout-body-left-container">
          <ErrorBoundary>
            <Suspense>
              <!-- main of left menu -->
              <AppLeftMenu></AppLeftMenu>

              <!-- loading state -->
              <template #fallback>
                <h4 class="text-center">{{ $t("default-layout") }}</h4>
              </template>
            </Suspense>
          </ErrorBoundary>
        </div>
      </aside>

      <main class="layout-body-right" :style="{ maxWidth: mainMaxWidth }">
        <div class="layout-body-right-container">
          <ErrorBoundary>
            <RouterView v-slot="{ Component }" :key="`${$route.fullPath}-${layoutReloadKey}`">
              <!-- <KeepAlive> -->
              <Suspense>
                <!-- main of page -->
                <template #default>
                  <component :is="Component" :key="$route.fullPath"></component>
                </template>

                <!-- loading state -->
                <template #fallback>
                  <div class="w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                    <!-- View loading -->
                    <AppProgressSpinner></AppProgressSpinner>
                  </div>
                </template>
              </Suspense>
              <!-- </KeepAlive> -->
            </RouterView>
          </ErrorBoundary>
        </div>
      </main>
    </div>

    <footer class="layout-footer">
      <div class="layout-footer-container">
        <AppFooter></AppFooter>
      </div>
    </footer>

    <BlockUI full-screen :blocked="selectMerchantProfileTask.isRunning"></BlockUI>
  </div>
</template>

<style lang="scss">
#app {
  display: flex;
}
.tms-layout {
  flex: 1 1 auto;

  display: flex;
  flex-direction: column;
  //padding-bottom: 2px; // trừ 1 px của header border và trừ 1px của footer border // TODO: xem có dùng được box-sizing hay gì để bỏ border-width của header footer
  //overflow-y: hidden; // dùng thử thay thế padding-bottom

  .layout-header .layout-header-container {
    height: 3.5rem; // sử dụng height của .navbar-page-header
    border-bottom: 1px solid var(--border-tertiary, #e6e6e6);
  }

  .layout-body {
    flex: 1 1 auto;
    overflow: auto; // important để hiển thị scroll trong .layout-body-right

    display: flex;
    flex-direction: row;
    align-items: stretch;

    .layout-body-left {
      flex: 0 1 auto;
      overflow: auto;

      .layout-body-left-container {
        width: 16.25rem;
        height: 100%;

        border-right: 1px solid var(--border-tertiary, #e6e6e6);
        background: var(--surface-ghost-hover, #f5f5f5);
      }
    }

    .layout-body-right {
      width: 100%;

      .layout-body-right-container {
        height: 100%;
        overflow: auto;

        display: flex;
        flex-direction: column;
      }
    }
  }

  &.layout-menu-collapsed .layout-body .layout-body-left .layout-body-left-container {
    width: 4.5rem;
  }

  .layout-footer .layout-footer-container {
    height: 1.5rem;
    border-top: 1px solid var(--border-tertiary, #e6e6e6);
  }
}
</style>
