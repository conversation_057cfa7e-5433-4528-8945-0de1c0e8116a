<script lang="ts">
import { defineComponent } from "vue"

export default defineComponent({
  name: "EmptyLayout",
  setup() {
    console.log("Empty layout is called")
  },
})
</script>

<template>
  <div class="tms-layout--empty">

    <main class="layout-body">
      <div class="layout-body-container">
        <!--The <slot> element is a slot outlet that indicates  where the "VIEW" content should be rendered.-->
        <RouterView v-slot="{ Component }" :key="$route.fullPath">
          <Suspense>
            <!-- main of page -->
            <template #default>
              <component :is="Component" :key="$route.path"></component>
            </template>

            <!-- loading state -->
            <template #fallback>
              <div class="w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                <!-- View loading -->
                <AppProgressSpinner></AppProgressSpinner>
              </div>
            </template>
          </Suspense>
        </RouterView>
      </div>
    </main>

    <footer class="layout-footer">
      <div class="layout-footer-container">
        <nav class="navbar navbar-page-footer">
          <div class="container-fluid">
            <div class="mx-auto">Copyright © 2006 - 2024 OnePAY. All rights reserved.</div>
          </div>
        </nav>
      </div>
    </footer>
  </div>
</template>

<style lang="scss">
#app {
  display: flex;
}
.tms-layout--empty {
  flex: 1 1 auto;

  display: flex;
  flex-direction: column;
  //padding-bottom: 2px; // trừ 1 px của header border và trừ 1px của footer border // TODO: xem có dùng được box-sizing hay gì để bỏ border-width của header footer
  //overflow-y: hidden; // dùng thử thay thế padding-bottom

  .layout-header .layout-header-container {
    height: 3.5rem; // sử dụng height của .navbar-page-header
    border-bottom: 1px solid var(--border-tertiary, #e6e6e6);
  }

  .layout-body {
    flex: 1 1 auto;
    overflow: auto; // important để hiển thị scroll trong .layout-body-right
    
    display: flex;
    flex-direction: column;
    align-items: stretch;


    .layout-body-container {
      height: 100%;
      overflow: auto;
  
      display: flex;
      flex-direction: column;
    }
  }

  .layout-footer .layout-footer-container {
    height: 1.5rem;
    border-top: 1px solid var(--border-tertiary, #e6e6e6);
  }

  .navbar-page-footer {
    //height: 1.5rem;
    height: 100%;

    padding-top: 0;
    padding-bottom: 0;
    background: var(--surface-ghost-hover, #f5f5f5);

    .container-fluid {
      padding-left: var(--layout-spacing-spacing-xxl, 1.5rem);
      padding-right: var(--layout-spacing-spacing-xxl, 1.5rem);
    }
  }
}
</style>
