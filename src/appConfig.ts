//import deepFreeze from "deep-freeze-es6"
// Dùng deepFreeze sẽ lỗi khi xử lý với regex -> cần custom
import deepFreeze from "@/utils/deepFreezeUtil"

export const APPCONFIG = deepFreeze({
  MODE: String(import.meta.env.MODE),
  MODE_DEV: <PERSON><PERSON><PERSON>(import.meta.env.DEV),
  /**
   * Mode phục vụ deploy cho server 63 của MIC.
   */
  MODE_DEV63: <PERSON><PERSON><PERSON>(import.meta.env.MODE == "development-63"),
  /**
   * Mode phục vụ cho môi trường của MIC (ko áp dụng cho môi trường OP).
   */
  MODE_DEV_MIC: Boolean(import.meta.env.MODE == "development-63" || import.meta.env.DEV),
  /**
   * Mode phục vụ deploy cho server dev.onepay.vn của OP.
   */
  MODE_DEVOP: Boolean(import.meta.env.MODE == "development-op"),
  MODE_PROD: Bo<PERSON>an(import.meta.env.PROD),

  APP_TITLE: String(import.meta.env.VITE_APP_TITLE),
  APP_HOST: String(import.meta.env.APP_HOST_URI),
  APP_PL_HOST: String(import.meta.env.APP_PL_HOST_URI),
  APP_ML_HOST: String(import.meta.env.APP_ML_HOST_URI),
  APP_API_ENDPOINT: String(import.meta.env.APP_API_ENDPOINT),

  APP_LOGIN_URL: String(import.meta.env.APP_LOGIN_URL),
  APP_LOGOUT_URL: String(import.meta.env.APP_LOGOUT_URL),
  APP_DEV_FAKE_JWT: String(import.meta.env.APP_DEV_FAKE_JWT),
  APP_DEV_FAKE_HAU_CURRENCY_GETALL_URL: String(import.meta.env.VITE_APP_DEV_FAKE_HAU_CURRENCY_GETALL_URL),

  APP_LINK_EXCHANGERATECONFIG: String(import.meta.env.VITE_APP_LINK_EXCHANGERATECONFIG),
  APP_GET_INSTALLMENT_OF_AMOUNT: Number(import.meta.env.APP_GET_INSTALLMENT_OF_AMOUNT),

  APP_USER_ADMIN_MAIN: String(import.meta.env.VITE_APP_USER_ADMIN)
    .split(/[\s,;]+/)
    .map((s) => s.trim()),
  APP_USER_ADMIN: [String(import.meta.env.APP_USER_ADMIN ?? ""), String(import.meta.env.VITE_APP_USER_ADMIN ?? "")]
    .join(",")
    .split(/[\s,;]+/)
    .map((s) => s.trim())
    .filter((s) => s),
  DETAUL_LOCALE: "en",
  DEBOUNCED_DELAY_INPUT: 500,
  /** Value: dd/MM/yyyy */
  FORMAT_DATEONLY: "dd/MM/yyyy",
  /** Value: DD/MM/YYYY */
  FORMAT_DATEONLY_MOMENT: "DD/MM/YYYY",
  /** Value: dd/MM/yyyy hh:mm tt */
  FORMAT_DATETIME: "dd/MM/yyyy hh:mm tt",
  /** Value: DD/MM/YYYY hh:mm A */
  FORMAT_DATETIME_MOMENT: "DD/MM/YYYY hh:mm A",
  /** Value: 20 (items per page) */
  DEFAULT_TABLE_ITEM_PER_PAGE: 20,
  PAY_NOW: 32,
  INSTALLMENT: 64,
  FAKE_X_USER_ID: String(import.meta.env.VITE_FAKE_X_USER_ID),

  MAX_PAYMENTAMOUNT: 9_999_999_999.99, // Do VND ko có thập phân nên coi như cái này chặn được luôn. Tuy nhiên thằng này hay bị js làm tròn lên khi xử lý .toFixed(0)
  /** Value: 9_999_999_999 */
  MAX_PAYMENTAMOUNT_VND: 9_999_999_999,
  /** Value: 9_999_999_999.99 */
  MAX_PAYMENTAMOUNT_USD: 9_999_999_999.99,
  /** Số tiền giới hạn tối thiểu được thanh toán trả ngay (10,000VND) */
  MAX_PAYMENTAMOUNT_PAYNOW: 10000,

  MAX_SELECTED_DAYS_IN_DATERANGE: Number(import.meta.env.APP_MAX_SELECTED_DAYS_IN_DATERANGE),

  MAX_NUMBER_INPUT_INT: 9_999_999_999,
  MAX_NUMBER_INPUT_DEC: 9_999_999_999.99,
  MAX_PAYMENT_QUANTITY: 999_999_999,
})

export default APPCONFIG
