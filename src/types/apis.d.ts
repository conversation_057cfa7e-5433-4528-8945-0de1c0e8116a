export interface PaymentLink {
  id: string
  merchantProfileId: string
  routerUri: string
  name: string
  description?: string
  dueDate?: Date | null
  paymentMethods: number
  installmentSettings?: InstallmentSettings[]
  hasPresetAmount: boolean
  presetAmountJson?: PresetAmountJson
  totalAmount?: number | null
  currency?: string
  exchangeRate?: number
  paymentAmount?: number | null
  paymentCurrency?: string | null
  hasLimitPayment: boolean
  maxPaymentQty: number
  totalPaymentQty: number
  inprogressPayments?: number
  infoFieldOptions: number
  customerNoteLckey?: CustomerNoteLocale[]
  fileId?: string | null
  fileName?: string | null
  createDate: number
  createByUserId: string
  lastModifyDate?: number
  lastModifyByUserId?: string
  enabled: boolean
  status: number
  linkType: number
}

export type InstallmentSettings = {
  bankId: string
  times: number[]
}

export type PresetAmountJson = {
  amount: number | null
  enableDiscount: boolean
  discount: number | null
  discountIsPercent: boolean
  enableTax: boolean
  tax: number | null
  enableOtherFee: boolean
  otherFeeLabel: string
  otherFee: number | null
  otherFeeIsPercent: boolean
}

export type PaymentMethods = {
  id: number
  code: string
  name: string
  ordinal: number
  active: boolean
}

export type PaymentLocale = {
  id: string
  name: string
  iconFileId: string
  ordinal: number
  isDefault?: boolean
}

export type CustomerNoteLocale = {
  locale?: string
  value?: string
}

export type PaymentLinkRequest = {
  merchantProfileId: string
  routerUri: string
  name: string
  description: string | null
  dueDate: Date | null
  paymentMethods: number
  installmentSettings: string
  hasPresetAmount: boolean
  presetAmountJson: string
  totalAmount: number | null
  currency: string | null
  exchangeRate: number | null
  paymentAmount: number | null
  paymentCurrency: string | null
  hasLimitPayment: boolean
  maxPaymentQty: number | null
  totalPaymentQty: number | null
  infoFieldOptions: number
  customerNoteLckey: CustomerNoteLocale[] | []
  fileId: string | null
  fileName: string | null
  enable: boolean | null
  linkType: number
}

export type CurrencyConfig = {
  id: string
  merchantProfileId: string
  currency: string
  updateType: number
  exchangeRate: number
  lastModifyDate: datetime
  enabled: boolean
  lastModifyBy: string
}

export type CurrencyExchangeRate = {
  currency: string
  exchangeRate: number
  imgICon?: string
}

export type OnePayInstallments = {
  bank_id: string
  bank_name: string | null
  cards: OnePayCards[]
  id: string
}

export type OnePayCards = {
  description: string
  name: string
  times: OnePayTimes[]
  type: string
}

export type OnePayTimes = {
  fee_amount: number
  monthly_amount: number
  time: number
}

export interface OnePayInstallmentResponse {
  installments: OnePayInstallments[]
}

export type DataInstallments = {
  bankId: string
  bankName: string
  threeMonth: boolean[]
  sixMonth: boolean[]
  nineMonth: boolean[]
  twelveMonth: boolean[]
  fifteenMonth: boolean[]
  eighteenMonth: boolean[]
  twentyFourMonth: boolean[]
  allTimeOfBank: number[]
  cards: OnePayCards[]
}

export type IsExit = {
  exist: boolean
}

export type CheckIsExit = {
  routerUri: string
  orderId: string | null
}

export interface OneTimeLinkProp {
  linkName?: string
  routerUri?: string
  amount?: number
  description?: string | null
  hasPresetAmount?: boolean
  hasLimitPayment?: boolean
  maxPaymentQty?: number | null
  totalPaymentQty?: number | null
  totalAmount?: number | null
  paymentAmount?: number | null
  currency?: string | null
  paymentCurrency?: string | null,
  linkType?: number
}

export interface CreatePaymentInput {
  merchant_id: string;
  payment_amount: number;
  note: string;
}

export interface CreatePaymentOutput {
  // tuỳ response API trả về cái gì thì bạn define ở đây
  success: boolean;
  message: string;
  payment_url: string;
  data?: any;
}