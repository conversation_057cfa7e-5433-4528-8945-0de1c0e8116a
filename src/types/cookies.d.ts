export interface SetCookiesOptions {
  expireTimes?: string | number | Date
  path?: string
  domain?: string
  secure?: boolean
  sameSite?: string
}

export interface RemoveCookiesOptions {
  path?: string
  domain?: string
}

export interface IAppCookies {
  get(keyName: string): string
  set(keyName: string, value: string, options?: SetCookiesOptions): void
  remove(keyName: string, options?: RemoveCookiesOptions): boolean
  hasKey(keyName: string): boolean
  keys(): string[]
}
