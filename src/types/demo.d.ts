//the types.ts file
export type APIResponse<T> = {
  success: boolean
  content: T
  status?: number
}

export type Student = {
  _id: number
  name: string
  age: number
  schoolID: number
}

export type GetStudentInput = {
  name: string
}

export type CreateStudentInput = {
  name: string
  age: number
  schoolID: number
}

export type UpdateStudentInput = {
  _id: number
  name: string
  age: number
  schoolID: number
}
