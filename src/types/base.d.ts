export interface IPagedResult<T> {
  data: T[]
  total: number
}

export interface IPagedRequest {
  start?: number
  length?: number
  sort?: string
}

export interface KeyValuePair<TKey, TValue> {
  key?: TKey
  value?: TValue
}

export interface SelectListItem {
  label?: string
  value?: string | number | boolean | null
  selected?: boolean
}

export interface DateRangModel {
  startDate?: Date
  endDate?: Date
}
