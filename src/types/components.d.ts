/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AddLimitPayment: typeof import('./../components/paymentLink/AddLimitPayment.vue')['default']
    AddNew: typeof import('./../components/paymentLink/AddNew.vue')['default']
    AddPaymentAmount: typeof import('./../components/paymentLink/AddPaymentAmount.vue')['default']
    AddSuccessPayment: typeof import('./../components/paymentLink/AddSuccessPayment.vue')['default']
    AppConfirmDialogManager: typeof import('./../components/ui/AppConfirmDialogManager.vue')['default']
    AppDataTable: typeof import('./../components/ui/AppDataTable.vue')['default']
    AppFilterInput: typeof import('./../components/ui/input/AppFilterInput.vue')['default']
    AppLoading: typeof import('./../components/ui/AppLoading.vue')['default']
    AppMultipleSelectOverlayPanel: typeof import('./../components/ui/AppMultipleSelectOverlayPanel.vue')['default']
    AppProgressSpinner: typeof import('./../components/ui/AppProgressSpinner.vue')['default']
    AppSidebar: typeof import('./../components/ui/AppSidebar.vue')['default']
    AppSvgNamedIcon: typeof import('./../components/ui/AppSvgNamedIcon.vue')['default']
    AppToastManager: typeof import('./../components/ui/AppToastManager.vue')['default']
    AssetIcon: typeof import('./../components/ui/AssetIcon.vue')['default']
    AsyncContent: typeof import('./../components/ui/AsyncContent.vue')['default']
    Avatar: typeof import('primevue/avatar')['default']
    BlockUI: typeof import('primevue/blockui')['default']
    Button: typeof import('primevue/button')['default']
    CalendarTime: typeof import('./../components/ui/dateui/CalendarTime.vue')['default']
    Checkbox: typeof import('primevue/checkbox')['default']
    Column: typeof import('primevue/column')['default']
    ColumnGroup: typeof import('primevue/columngroup')['default']
    CurrencySettingDialog: typeof import('./../components/CurrencySettingDialog.vue')['default']
    DashboardBody: typeof import('./../components/dashboard/DashboardBody.vue')['default']
    DashboardHeader: typeof import('./../components/dashboard/DashboardHeader.vue')['default']
    DataTable: typeof import('primevue/datatable')['default']
    DateRangPickerInline: typeof import('./../components/ui/dateui/DateRangPickerInline.vue')['default']
    DateTimePickerOverlayPanel: typeof import('./../components/ui/dateui/DateTimePickerOverlayPanel.vue')['default']
    Dialog: typeof import('primevue/dialog')['default']
    DownloadQrDialog: typeof import('./../components/DownloadQrDialog.vue')['default']
    Dropdown: typeof import('primevue/dropdown')['default']
    DropdownMultipleSelect: typeof import('./../components/ui/multiple-select/DropdownMultipleSelect.vue')['default']
    DropdownMultipleSelectOverlayPanel: typeof import('./../components/ui/multiple-select/DropdownMultipleSelectOverlayPanel.vue')['default']
    Edit: typeof import('./../components/paymentLink/Edit.vue')['default']
    Error102ProcessingComponent: typeof import('./../components/errors/Error102ProcessingComponent.vue')['default']
    Error403ForbiddenComponent: typeof import('./../components/errors/Error403ForbiddenComponent.vue')['default']
    Error403NoAppPermissionComponent: typeof import('./../components/errors/Error403NoAppPermissionComponent.vue')['default']
    Error403NoMerchantComponent: typeof import('./../components/errors/Error403NoMerchantComponent.vue')['default']
    Error404NotFoundComponent: typeof import('./../components/errors/Error404NotFoundComponent.vue')['default']
    Error500ServerErrorComponent: typeof import('./../components/errors/Error500ServerErrorComponent.vue')['default']
    ErrorBoundary: typeof import('./../components/ErrorBoundary.vue')['default']
    ExportTransactionsDialog: typeof import('./../components/paymentLink/ExportTransactionsDialog.vue')['default']
    FileUpload: typeof import('primevue/fileupload')['default']
    HelloWorld: typeof import('./../components/HelloWorld.vue')['default']
    InputAmount: typeof import('./../components/ui/input/InputAmount.vue')['default']
    InputDateTimePickerAppliable: typeof import('./../components/ui/dateui/InputDateTimePickerAppliable.vue')['default']
    InputDateTimePickerSelectable: typeof import('./../components/ui/dateui/InputDateTimePickerSelectable.vue')['default']
    InputNumber: typeof import('primevue/inputnumber')['default']
    InputNumberNew: typeof import('./../components/ui/input/InputNumberNew.vue')['default']
    InputSwitch: typeof import('primevue/inputswitch')['default']
    InputText: typeof import('primevue/inputtext')['default']
    InputTextClearable: typeof import('./../components/ui/input/InputTextClearable.vue')['default']
    InputTextFilterClearable: typeof import('./../components/ui/input/InputTextFilterClearable.vue')['default']
    InstallmentSetting: typeof import('./../components/paymentLink/InstallmentSetting.vue')['default']
    Lazy: typeof import('./../components/ui/Lazy.vue')['default']
    LinkInfomation: typeof import('./../components/paymentLink/LinkInfomation.vue')['default']
    LinkLimitPayment: typeof import('./../components/paymentLink/LinkLimitPayment.vue')['default']
    LinkOptions: typeof import('./../components/paymentLink/LinkOptions.vue')['default']
    LinkPaymentAmount: typeof import('./../components/paymentLink/LinkPaymentAmount.vue')['default']
    LoadingOverlay: typeof import('./../components/loading-overlay/LoadingOverlay.vue')['default']
    MerchantSelecterSidebar: typeof import('./../components/MerchantSelecterSidebar.vue')['default']
    MultiSelect: typeof import('primevue/multiselect')['default']
    OneTimeLink: typeof import('./../components/dashboard/OneTimeLink.vue')['default']
    OverlayPanel: typeof import('primevue/overlaypanel')['default']
    Panel: typeof import('./../components/ui/Panel.vue')['default']
    PickList: typeof import('primevue/picklist')['default']
    ProgressBar: typeof import('primevue/progressbar')['default']
    ProgressSpinner: typeof import('./../components/ui/ProgressSpinner.vue')['default']
    QrCode: typeof import('./../components/QrCode.vue')['default']
    RadioButton: typeof import('primevue/radiobutton')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RouterViewKeepAlive: typeof import('./../components/RouterViewKeepAlive.vue')['default']
    Row: typeof import('primevue/row')['default']
    SettingTableColumnComponent: typeof import('./../components/transaction/SettingTableColumnComponent.vue')['default']
    Sidebar: typeof import('primevue/sidebar')['default']
    SvgIcon: typeof import('./../components/ui/SvgIcon.vue')['default']
    TabPanel: typeof import('primevue/tabpanel')['default']
    TabView: typeof import('primevue/tabview')['default']
    TemplateQrCodeDownload: typeof import('./../components/TemplateQrCodeDownload.vue')['default']
    Textarea: typeof import('primevue/textarea')['default']
    TextareaMaxLength: typeof import('./../components/ui/TextareaMaxLength.vue')['default']
    TmsDatePicker: typeof import('./../components/date/TmsDatePicker.vue')['default']
    TmsDateRangePicker: typeof import('./../components/date/TmsDateRangePicker.vue')['default']
    TmsDateTimePicker: typeof import('./../components/date/TmsDateTimePicker.vue')['default']
    TmsMonthPicker: typeof import('./../components/date/TmsMonthPicker.vue')['default']
    TmsWeekPicker: typeof import('./../components/date/TmsWeekPicker.vue')['default']
    TranMoreFilter: typeof import('./../components/transaction/TranMoreFilter.vue')['default']
    TriStateCheckbox: typeof import('primevue/tristatecheckbox')['default']
  }
}
