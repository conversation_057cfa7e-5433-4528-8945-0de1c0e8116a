/**
 * @file Trong quá trình xử lý, có những đối tượng chưa xác định chính xác có được nó có được wrap vào trong các đối tượng con hay không!
 * Ví dụ OP ko xử lý được array[] trực tiếp trong json nên phải wrap vào object { data[] }
 * Hoặc các hàm OP api phía frondend ko có đủ thông tin về tài liệu (tháng 10/2024) để xác định chính xác nó được wrap hay không.
 * Do đó việc tạo ra các hàm unwrap này để giải quyết tạm thời vấn đề trên
 * Nó sẽ thực hiện check xem object có chưa đối tượng wrap không để thực hiện unwrap.
 * 
 * Áp dụng chủ yếu ở bước Convert array data hoặc Call OP api Refund/Void/Capture hoặc Reject/Approval
 * 
 * NOTE: có thể xảy ra unwrap nhầm nếu propery con có chứa tên đối tượng wrap. Cần phải cẩn thận việc api bị update.
 * 
 * <AUTHOR> <<EMAIL>>
 */

import { CaptureResponse, RefundResponse, VoidResponse } from "@/apis/transactionApi"
import { AxiosResponse } from "axios"

function isNullOrUndefined<T>(input: Nullable<T>): input is undefined {
  return input == null || typeof input == "undefined"
}

//#region Array data wrapperd in .data
interface DataObjectWrapper<T> {
  data: T
}

function isWrappedToDataObject<T>(data: T | DataObjectWrapper<T>): data is DataObjectWrapper<T> {
  if (typeof data == "object" && Object.prototype.hasOwnProperty.call(data, "data")) return true
  return false
}

/**
 * Do bên OP có thằng MA-WEB có lỗi. Nó hiện tại chặn ko cho xử lý response trả về có dạng Array.
 * -> phải wrap lại dưới dạng Object { data: [...] }
 * Các hàm này để đản bảo ko bị unwrap nhầm.
 */
export function unwrapDataObjectArray<T = any>(
  res: AxiosResponse<T[] | DataObjectWrapper<T[]>>
): AxiosResponse<T[]> {
  console.debug("unwrapArrayDataObject", "check wrap", res.data)
  let rawData: T[]
  if (isWrappedToDataObject(res.data)) {
    console.info("unwrapArrayDataObject", "data unwrapping!!")
    rawData = res.data.data
    if (isNullOrUndefined(rawData)) {
      rawData = []
    } else if (!Array.isArray(rawData)) {
      console.warn("unwrapArrayDataObject", "rawData of res.data.data is not array", res.data)
    }
  } else {
    rawData = res.data
    if (isNullOrUndefined(rawData)) {
      rawData = []
    } else if (!Array.isArray(rawData)) {
      console.warn("unwrapArrayDataObject", "rawData of res.data is not array", res.data)
    }
  }
  const resUnwrapped = res as AxiosResponse<T[]>
  resUnwrapped.data = rawData
  return resUnwrapped
}
//#endregion

//#region data wrapperd in .refund
export interface RefundObjectWrapper<T> {
  refund: T
}

function isWrappedToRefundObject<T>(input: T | RefundObjectWrapper<T>): input is RefundObjectWrapper<T> {
  if (typeof input == "object" && Object.prototype.hasOwnProperty.call(input, "refund")) return true
  return false
}

export function unwrapRefundObject<T extends RefundResponse>(
  res: AxiosResponse<T | RefundObjectWrapper<T>>
): AxiosResponse<T> {
  console.debug("unwrapRefundObject", "check wrap", res.data)
  let rawData: T
  if (isWrappedToRefundObject(res.data)) {
    console.info("unwrapRefundObject", "data unwrapping!!")
    rawData = res.data.refund
  } else {
    rawData = res.data
  }
  const resUnwrapped = res as AxiosResponse<T>
  resUnwrapped.data = rawData
  return resUnwrapped
}
//#endregion

//#region data wrapperd in .void
export interface VoidObjectWrapper<T> {
  void: T
}

function isWrappedToVoidObject<T>(input: T | VoidObjectWrapper<T>): input is VoidObjectWrapper<T> {
  if (typeof input == "object" && Object.prototype.hasOwnProperty.call(input, "void")) return true
  return false
}

export function unwrapVoidObject<T extends VoidResponse>(
  res: AxiosResponse<T | VoidObjectWrapper<T>>
): AxiosResponse<T> {
  console.debug("unwrapVoidObject", "check wrap", res.data)
  let rawData: T
  if (isWrappedToVoidObject(res.data)) {
    console.info("unwrapVoidObject", "data unwrapping!!")
    rawData = res.data.void
  } else {
    rawData = res.data
  }
  const resUnwrapped = res as AxiosResponse<T>
  resUnwrapped.data = rawData
  return resUnwrapped
}
//#endregion

//#region data wrapperd in .capture
export interface CaptureObjectWrapper<T> {
  capture: T
}

function isWrappedToCaptureObject<T>(input: T | CaptureObjectWrapper<T>): input is CaptureObjectWrapper<T> {
  if (typeof input == "object" && Object.prototype.hasOwnProperty.call(input, "capture")) return true
  return false
}

export function unwrapCaptureObject<T extends CaptureResponse>(
  res: AxiosResponse<T | CaptureObjectWrapper<T>>
): AxiosResponse<T> {
  console.debug("unwrapCaptureObject", "check wrap", res.data)
  let rawData: T
  if (isWrappedToCaptureObject(res.data)) {
    console.info("unwrapCaptureObject", "data unwrapping!!")
    rawData = res.data.capture
  } else {
    rawData = res.data
  }
  const resUnwrapped = res as AxiosResponse<T>
  resUnwrapped.data = rawData
  return resUnwrapped
}
//#endregion
