/// <reference types="vite/client" />

// Tham khảo: https://vitejs.dev/guide/env-and-mode#intellisense-for-typescript
interface ImportMetaEnv {
  // The default env
  //BASE_URL: string
  //MODE: string
  //DEV: boolean
  //PROD: boolean
  //SSR: boolean

  // The custom env
  readonly VITE_APP_BASE_URL: string
  //readonly VITE_APP_API_ENDPOINT: string
  readonly VITE_APP_TITLE: string
  //readonly VITE_APP_OP_INSTALLMENTS_SETTING: string
  //readonly VITE_APP_DEV_FAKE_HAU_CURRENCY_GETALL_URL: string
  readonly VITE_APP_USER_ADMIN: string
  readonly APP_USER_ADMIN: string // Biến ẩn của env.d.ts. tương đương VITE_APP_USER_ADMIN

  // more VITEJS env variables...
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// /* eslint-disable */
// declare module '*.vue' {
//     import type { DefineComponent } from 'vue'
//     const component: DefineComponent<{}, {}, any>
//     export default component
// }
