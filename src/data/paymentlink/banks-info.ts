/* eslint-disable @typescript-eslint/ban-types */
/* eslint-disable no-var */
// import {Injectable, Inject} from '@angular/core';

// @Injectable()
export class banksInfo {
  public banks =
    [
      {
        'id': '1',
        'name': 'Vietcombank',
        'name_dialog': 'Vietcombank',
        'code': 'VCB',
        'swiftCode': 'BFTVVNVX',
        'pattern': '^(970436\\d{13}|686868\\d{10})$',
        'bin': ['970436', '686868'],
        'auth': 'op',
        'index': 1,
        'search': '"vcb\n' +
          'vietcombank\n' +
          'ngoai thuong\n' +
          'ngoaithuong"\n'
      },
      {
        'id': '2',
        'name': 'Techcombank',
        'name_dialog': 'Techcombank',
        'code': 'TCB',
        'swiftCode': 'VTCBVNVX',
        'pattern': '',
        'bin': [''],
        'auth': 'bank',
        'index': 5,
        'search': '"tcb\n' +
          'techcombank\n' +
          'ky thuong\n' +
          'kythuong"\n'
      },
      {
        'id': '3',
        'name': 'TPBank',
        'name_dialog': 'TPBank',
        'code': 'TPB',
        'swiftCode': 'TPBVVNVX',
        'pattern': '^(970423\\d*)$',
        'bin': ['970423'],
        'auth': 'bank',
        'index': 8,
        'search': '"tpbank\n' +
          'tien phong bank\n' +
          'tienphongbank"\n'
      },
      {
        'id': '4',
        'name': 'VietinBank',
        'name_dialog': 'VietinBank',
        'code': 'VIETINBANK',
        'swiftCode': 'ICBVVNVX',
        'pattern': '^(6201\\d{12}|970415\\d{13}|970415\\d{10})$',
        'bin': ['620160', '970415'],
        'auth': 'bank',
        'index': 4,
        'search': '"vtb\n' +
          'vietinbank\n' +
          'cong thuong viet nam\n' +
          'congthuongvietnam"\n'
      },
      {
        'id': '5',
        'name': 'VIB',
        'name_dialog': 'VIB',
        'code': 'VIB',
        'swiftCode': 'VNIBVNVX',
        'pattern': '^(970441\\d*)$',
        'bin': ['970441'],
        'auth': 'bank',
        'index': 11,
        'search': '"vib\n' +
          'quoc te\n' +
          'quocte"\n'
      },
      {
        'id': '6',
        'name': 'DongA Bank',
        'name_dialog': 'DongA Bank',
        'code': 'DAB',
        'swiftCode': 'EACBVNVX',
        'pattern': '',
        'bin': [''],
        'auth': 'bank',
        'index': 10,
        'search': '"dab\n' +
          'dongabank\n' +
          'dong a bank"\n'
      },
      {
        'id': '7',
        'name': 'HDBank',
        'name_dialog': 'HDBank',
        'code': 'HDB',
        'swiftCode': 'HDBCVNVX',
        'pattern': '^(970437\\d*)$',
        'bin': ['970437'],
        'auth': 'bank',
        'index': 15,
        'search': '"hdbank\n' +
          'phat trien nha\n' +
          'phattriennha"\n'
      },
      {
        'id': '8',
        'name': 'MBBank',
        'name_dialog': 'MBBank',
        'code': 'MBBANK',
        'swiftCode': 'MSCBVNVX',
        'pattern': '^(970422\\d*)$',
        'bin': ['970422'],
        'auth': 'op',
        'index': 9,
        'search': '"mbbank\n' +
          'quan doi\n' +
          'quandoi"\n'
      },
      {
        'id': '9',
        'name': 'VietABank',
        'name_dialog': 'VietABank',
        'code': 'VIETA',
        'swiftCode': 'VNACVNVX',
        'pattern': '^(970427\\d*)$',
        'bin': ['970427'],
        'auth': 'bank',
        'index': 25,
        'search': 'vietabank, vab, viet a bank, vietabank'
      },
      {
        'id': '10',
        'name': 'MSB',
        'name_dialog': 'MSB',
        'code': 'MSB',
        'swiftCode': 'MCOBVNVX',
        'pattern': '^(970426\\d*)$',
        'bin': ['970426'],
        'auth': 'op',
        'index': 14,
        'search': '"msb\n' +
          'hang hai\n' +
          'hanghai"\n'
      },
      {
        'id': '11',
        'name': 'Eximbank',
        'name_dialog': 'Eximbank',
        'code': 'EXIM',
        'swiftCode': 'EBVIVNVX',
        'pattern': '^(970431\\d*)$',
        'bin': ['970431'],
        'auth': 'op',
        'index': 12,
        'search': '"eib\n' +
          'exb\n' +
          'eximbank\n' +
          'xuat nhap khau\n' +
          'xuatnhapkhau"\n'
      },
      {
        'id': '12',
        'name': 'SHB',
        'name_dialog': 'SHB',
        'code': 'SHB',
        'swiftCode': 'SHBAVNVX',
        'pattern': '^(970443\\d*)$',
        'bin': ['970443'],
        'auth': 'bank',
        'index': 13,
        'search': '"shb\n' +
          'sai gon ha noi\n' +
          'saigonhanoi"\n'
      },
      {
        'id': '14',
        'name': 'VPBank',
        'name_dialog': 'VPBank',
        'code': 'VPB',
        'swiftCode': 'VPBKVNVX',
        'pattern': '^(970432\\d*)$',
        'bin': ['970432'],
        'auth': 'op',
        'index': 6,
        'search': '"vpbank\n' +
          'viet nam thinh vuong\n' +
          'thinh vuong\n' +
          'thinhvuong"\n'
      },
      {
        'id': '15',
        'name': 'ABBANK',
        'name_dialog': 'ABBANK',
        'code': 'ABB',
        'swiftCode': 'ABBKVNVX',
        'pattern': '^(970425\\d*)$',
        'bin': ['970425'],
        'auth': 'op',
        'index': 17,
        'search': 'abbank, anbinhbank, an binh bank'
      },
      {
        'id': '16',
        'name': 'Sacombank',
        'name_dialog': 'Sacombank',
        'code': 'SACOMBANK',
        'swiftCode': 'SGTTVNVX',
        'pattern': '^(970403\\d*)$',
        'bin': ['970403'],
        'auth': 'op',
        'index': 7,
        'search': '"stb\n' +
          'scb\n' +
          'sacombank\n' +
          'sai gon thuong tin\n' +
          'thuong tin\n' +
          'thuongtin"\n'
      },
      {
        'id': '17',
        'name': 'NAM A BANK',
        'name_dialog': 'NAM A BANK',
        'code': 'NAB',
        'swiftCode': 'NAMAVNVX',
        'pattern': '^(970428\\d*)$',
        'bin': ['970428'],
        'auth': 'op',
        'index': 19,
        'search': 'namabank, nab, nam a bank, nama bank'
      },
      {
        'id': '18',
        'name': 'OceanBank',
        'name_dialog': 'OceanBank',
        'code': 'OCB',
        'swiftCode': 'OJBAVNVX',
        'pattern': '^(970414\\d*)$',
        'bin': ['970414'],
        'auth': 'op',
        'index': 21,
        'search': '"ocb, ojb\n' +
          'oceanbank\n' +
          'dai duong\n' +
          'daiduong"\n'
      },
      {
        'id': '19',
        'name': 'BIDV',
        'name_dialog': 'BIDV',
        'code': 'BIDV',
        'swiftCode': 'BIDVVNVX',
        'pattern': '^(6688\\d{12}|970418\\d*)$',
        'bin': ['970418'],
        'auth': 'bank',
        'index': 2,
        'search': '"bidv\n' +
          'dau tu\n' +
          'dautu"\n'
      },
      {
        'id': '20',
        'name': 'SeABank',
        'name_dialog': 'SeABank',
        'code': 'SAB',
        'swiftCode': 'SEAVVNVX',
        'pattern': '^(970440\\d{13})$',
        'bin': ['970440'],
        'auth': 'op',
        'index': 16,
        'search': '"seabank\n' +
          'dong nam a\n' +
          'dongnama"\n'
      },
      {
        'id': '22',
        'name': 'BAC A BANK',
        'name_dialog': 'BAC A BANK',
        'code': 'BAB',
        'swiftCode': 'NASCVNVX',
        'pattern': '^(970409\\d*)$',
        'bin': ['970409'],
        'auth': 'op',
        'index': 18,
        'search': 'bab, bacabank, bac a bank'
      },
      {
        'id': '23',
        'name': 'NCB',
        'name_dialog': 'NCB',
        'code': 'NCB',
        'swiftCode': 'NVBAVNVX',
        'pattern': '^(970419\\d*)$',
        'bin': ['970419'],
        'auth': 'op',
        'index': 20,
        'search': '"ncb\n' +
          'quoc dan\n' +
          'quocdan"\n'
      },
      {
        'id': '24',
        'name': 'Agribank',
        'name_dialog': 'Agribank',
        'code': 'AGRIBANK',
        'swiftCode': 'VBAAVNVX',
        'pattern': '^(970405\\d*)$',
        'bin': ['970405'],
        'auth': 'op',
        'index': 3,
        'search': '"agribank\n' +
          'nong nghiep\n' +
          'nongnghiep"\n'
      },
      {
        'id': '25',
        'name': 'SCB',
        'name_dialog': 'SCB',
        'code': 'SCB',
        'swiftCode': 'SACLVNVX',
        'pattern': '^(970429\\d*)$',
        'bin': ['970429'],
        'auth': 'bank',
        'index': 23,
        'search': '"scb\n' +
          'sai gon"\n'
      },
      {
        'id': '27',
        'name': 'PVcomBank',
        'name_dialog': 'PVcomBank',
        'code': 'PVCB',
        'swiftCode': 'WBVNVNVX',
        'pattern': '^(970412\\d*)$',
        'bin': ['970412'],
        'auth': 'op',
        'index': 22,
        'search': '"pvcombank\n' +
          'dai chung\n' +
          'daichung"\n'
      },
      {
        'id': '30',
        'name': 'Viet Capital Bank',
        'name_dialog': 'Viet Capital Bank',
        'code': 'VCCB',
        'swiftCode': 'VCBCVNVX',
        'pattern': '^(970454\\d*)$',
        'bin': ['970454'],
        'auth': 'op',
        'index': 24,
        'search': 'vccb\n' +
          'vietcapitalbank\n' +
          'viet capital bank\n' +
          'ban viet\n' +
          'banviet'
      },
      {
        'id': '31',
        'name': 'Viettel Pay',
        'name_dialog': 'Viettel Pay',
        'code': 'VIETTELPAY',
        'swiftCode': '',
        'pattern': '',
        'bin': [''],
        'auth': 'op',
        'index': 25,
        'search': 'viettelpay'
      },
      {
        'id': '33',
        'name': 'ACB',
        'name_dialog': 'ACB',
        'code': 'ACB',
        'swiftCode': 'ASCBVNVX',
        'pattern': '^(970416\\d{10})$',
        'bin': ['970416'],
        'auth': 'bank',
        'index': 26,
        'search': 'acb\n' +
          'a chau\n' +
          'achau'
      }, // TODO: name
      {
        'id': '34',
        'name': 'GPBank',
        'name_dialog': 'GPBank',
        'code': 'GPB',
        'swiftCode': 'GBNKVNVX',
        'pattern': '^(970408\\d{10})$',
        'bin': ['970408'],
        'auth': 'bank',
        'index': 28,
        'search': 'gpbank\n' +
          'dau khi toan cau\n' +
          'daukhitoancau'
      },
      {
        'id': '35',
        'name': 'OCB',
        'name_dialog': 'OCB',
        'code': 'OCB',
        'swiftCode': 'ORCOVNVX',
        'pattern': '^(970448\\d{10})$',
        'bin': ['970448'],
        'auth': 'bank',
        'index': 29,
        'search': 'ocb\n' +
          'oricom\n' +
          'orient\n' +
          'phuong dong\n' +
          'phuongdong'
      },
      {
        'id': '36',
        'name': 'LienVietPostBank',
        'name_dialog': 'LienVietPostBank',
        'code': 'LPB',
        'swiftCode': 'LVBKVNVX',
        'pattern': '^(970449\\d*)$',
        'bin': ['970449'],
        'auth': 'bank',
        'index': 30,
        'search': 'lpb\n' +
          'lvpb\n' +
          'lienvietpostbank\n' +
          'lien viet post bank\n' +
          'buu dien lien viet\n' +
          'buudienlienviet'
      },
      {
        'id': '37',
        'name': 'BAOVIET Bank',
        'name_dialog': 'BAOVIET Bank',
        'code': 'BVB',
        'swiftCode': 'BVBVVNVX',
        'pattern': '^(970438\\d{10})$',
        'bin': ['970438'],
        'auth': 'bank',
        'index': 31,
        'search': 'bvb\n' +
          'bao viet bank\n' +
          'baovietbank'
      },
      {
        'id': '38',
        'name': 'KienlongBank',
        'name_dialog': 'Kienlongbank',
        'code': 'KLB',
        'swiftCode': 'KLBKVNVX',
        'pattern': '^(970452\\d{10})$',
        'bin': ['970452'],
        'auth': 'bank',
        'index': 31,
        'search': 'klb\n' +
          'kienlongbank\n' +
          'kien long bank'
      },
      {
        'id': '39',
        'name': 'VRB',
        'name_dialog': 'VRB',
        'code': 'VRB',
        'swiftCode': 'VRBAVNVX',
        'pattern': '^(970421\\d{10})$',
        'bin': ['970421'],
        'auth': 'bank',
        'index': 32,
        'search': 'vrbank\n' +
          'viet nga\n' +
          'vietnga'
      },
      {
        'id': '40',
        'name': 'Public Bank',
        'name_dialog': 'Public Bank',
        'code': 'PBVN',
        'swiftCode': 'VIDPVNV5',
        'pattern': '^(970439\\d{10})$',
        'bin': ['970439'],
        'auth': 'bank',
        'index': 33,
        'search': 'pbvn\n' +
          'public bank\n' +
          'publicbank'
      },
      {
        'id': '41',
        'name': 'SAIGONBANK',
        'name_dialog': 'SAIGONBANK',
        'code': 'SGB',
        'swiftCode': 'SBITVNVX',
        'pattern': '^(970400\\d{10})$',
        'bin': ['970400'],
        'auth': 'bank',
        'index': 34,
        'search': 'sgb\n' +
          'saigonbank\n' +
          'sai gon bank\n' +
          'sai gon cong thuong\n' +
          'saigoncongthuong'
      },
      {
        'id': '42',
        'name': 'PG Bank',
        'name_dialog': 'PG Bank',
        'code': 'PGB',
        'swiftCode': 'PGBLVNVX',
        'pattern': '^(970430\\d{10})$',
        'bin': ['970430'],
        'auth': 'bank',
        'index': 35,
        'search': 'pgb\n' +
          'pgbank\n' +
          'pg bank\n' +
          'xang dau petrolimex\n' +
          'xangdaupetrolimex'
      },
      {
        'id': '43',
        'name': 'IVB',
        'name_dialog': 'IVB',
        'code': 'IVB',
        'swiftCode': 'IABBVNVX',
        'pattern': '^(970434\\d{10})$',
        'bin': ['970434'],
        'auth': 'bank',
        'index': 36,
        'search': 'ivb\n' +
          'indovina'
      },
      {
        'id': '44',
        'name': 'Wooribank',
        'name_dialog': 'Wooribank',
        'code': 'WOO',
        'swiftCode': 'HVBKVNVX',
        'pattern': '^(970457\\d{10})$',
        'bin': ['970457'],
        'auth': 'bank',
        'index': 37,
        'search': 'wrb\n' +
          'wooribank'
      },
      {
        'id': '45',
        'name': 'UOB',
        'name_dialog': 'UOB',
        'code': 'UOB',
        'swiftCode': 'UOVBVNVX',
        'pattern': '^(970458\\d{10})$',
        'bin': ['970458'],
        'auth': 'bank',
        'index': 38,
        'search': 'uob\n' +
          'united overseas bank\n' +
          'unitedoverseasbank'
      },
      {
        'id': '46',
        'name': 'Shinhan Bank',
        'name_dialog': 'Shinhan Bank',
        'code': 'SVB',
        'swiftCode': 'SHBKVNVX',
        'pattern': '^(970424\\d{10})$',
        'bin': ['970424'],
        'auth': 'bank',
        'index': 39,
        'search': 'shbbvn, shinhan'
      },
      {
        'id': '47',
        'name': 'Vietcombank',
        'name_dialog': 'Vietcombank',
        'code': 'VCB',
        'swiftCode': 'BFTVVNVX',
        'pattern': '^(970436\\d{13}|686868\\d{10})$',
        'bin': ['970436', '686868'],
        'auth': 'op',
        'index': 40,
        'search': '"vcb\n' +
          'vietcombank\n' +
          'ngoai thuong\n' +
          'ngoaithuong"\n'
      },
      {
        'id': '48',
        'name': 'VIB',
        'name_dialog': 'VIB',
        'code': 'VIB',
        'swiftCode': 'VNIBVNVX',
        'pattern': '^(970441\\d*|180909\\d{10}|180906\\d{10})$',
        'bin': ['970441'],
        'auth': 'bank',
        'index': 50,
        'search': '"vib\n' +
          'quoc te\n' +
          'quocte"\n'
      },
      {
        'id': '49',
        'name': 'MBBank',
        'name_dialog': 'MBBank',
        'code': 'MBBANK',
        'swiftCode': 'MSCBVNVX',
        'pattern': '^(970422\\d{10}|193939\\d{10})$',
        'bin': ['970422'],
        'auth': 'op',
        'index': 48,
        'search': '"mbbank\n' +
          'quan doi\n' +
          'quandoi"\n'
      },
      {
        'id': '50',
        'name': 'VietinBank',
        'name_dialog': 'VietinBank',
        'code': 'VIETINBANK',
        'swiftCode': 'ICBVVNVX',
        'pattern': '^(6201\\d{12}|970415\\d{13}|970415\\d{10})$',
        'bin': ['620160', '970415'],
        'auth': 'bank',
        'index': 43,
        'search': '"vtb\n' +
          'vietinbank\n' +
          'cong thuong viet nam\n' +
          'congthuongvietnam"\n'
      },
      {
        'id': '51',
        'name': 'HDBank',
        'name_dialog': 'HDBank',
        'code': 'HDB',
        'swiftCode': 'HDBCVNVX',
        'pattern': '^(970437\\d{10}|970437\\d{10})$',
        'bin': ['970437'],
        'auth': 'bank',
        'index': 54,
        'search': '"hdbank\n' +
          'phat trien nha\n' +
          'phattriennha"\n'
      },
      {
        'id': '52',
        'name': 'NCB',
        'name_dialog': 'NCB',
        'code': 'NCB',
        'swiftCode': 'NVBAVNVX',
        'pattern': '^(970419\\d{10}|818188\\d{10})$',
        'bin': ['970419'],
        'auth': 'op',
        'index': 59,
        'search': '"ncb\n' +
          'quoc dan\n' +
          'quocdan"\n'
      },
      {
        'id': '53',
        'name': 'MSB',
        'name_dialog': 'MSB',
        'code': 'MSB',
        'swiftCode': 'MCOBVNVX',
        'pattern': '^(970426\\d*)$',
        'bin': ['970426'],
        'auth': 'op',
        'index': 53,
        'search': '"msb\n' +
          'hang hai\n' +
          'hanghai"\n'
      },
      {
        'id': '54',
        'name': 'VietABank',
        'name_dialog': 'VietABank',
        'code': 'VIETA',
        'swiftCode': 'VNACVNVX',
        'pattern': '^(970427\\d*)$',
        'bin': ['970427'],
        'auth': 'bank',
        'index': 64,
        'search': 'vietabank, vab, viet a bank, vietabank'
      },
      {
        'id': '55',
        'name': 'OceanBank',
        'name_dialog': 'OceanBank',
        'code': 'OCB',
        'swiftCode': 'OJBAVNVX',
        'pattern': '^(970414\\d*)$',
        'bin': ['970414'],
        'auth': 'op',
        'index': 60,
        'search': '"ocb, ojb\n' +
          'oceanbank\n' +
          'dai duong\n' +
          'daiduong"\n'
      },
      {
        'id': '56',
        'name': 'BAC A BANK',
        'name_dialog': 'BAC A BANK',
        'code': 'BAB',
        'swiftCode': 'NASCVNVX',
        'pattern': '^(970409\\d*)$',
        'bin': ['970409'],
        'auth': 'op',
        'index': 57,
        'search': 'bab, bacabank, bac a bank'
      },
      {
        'id': '57',
        'name': 'DongA Bank',
        'name_dialog': 'DongA Bank',
        'code': 'DAB',
        'swiftCode': 'EACBVNVX',
        'pattern': '^(970406\\d{10}|1792\\{d12})$',
        'bin': [''],
        'auth': 'bank',
        'index': 49,
        'search': '"dab\n' +
          'dongabank\n' +
          'dong a bank"\n'
      },
      {
        'id': '58',
        'name': 'ABBANK',
        'name_dialog': 'ABBANK',
        'code': 'ABB',
        'swiftCode': 'ABBKVNVX',
        'pattern': '^(970425\\d{10}|191919\\d{10})$',
        'bin': ['970425'],
        'auth': 'op',
        'index': 56,
        'search': 'abbank, anbinhbank, an binh bank'
      },
      {
        'id': '59',
        'name': 'BIDV',
        'name_dialog': 'BIDV',
        'code': 'BIDV',
        'swiftCode': 'BIDVVNVX',
        'pattern': '^(6688\\d{12}|970418\\d*)$',
        'bin': ['970418'],
        'auth': 'bank',
        'index': 41,
        'search': '"bidv\n' +
          'dau tu\n' +
          'dautu"\n'
      },
      {
        'id': '60',
        'name': 'SHB',
        'name_dialog': 'SHB',
        'code': 'SHB',
        'swiftCode': 'SHBAVNVX',
        'pattern': '^(970443\\d*)$',
        'bin': ['970443'],
        'auth': 'bank',
        'index': 52,
        'search': '"shb\n' +
          'sai gon ha noi\n' +
          'saigonhanoi"\n'
      },
      {
        'id': '61',
        'name': 'TPBank',
        'name_dialog': 'TPBank',
        'code': 'TPB',
        'swiftCode': 'TPBVVNVX',
        'pattern': '^(970423\\d*)$',
        'bin': ['970423'],
        'auth': 'bank',
        'index': 47,
        'search': '"tpbank\n' +
          'tien phong bank\n' +
          'tienphongbank"\n'
      },
      {
        'id': '62',
        'name': 'Agribank',
        'name_dialog': 'Agribank',
        'code': 'AGRIBANK',
        'swiftCode': 'VBAAVNVX',
        'pattern': '^(970405\\d*)$',
        'bin': ['970405'],
        'auth': 'op',
        'index': 42,
        'search': '"agribank\n' +
          'nong nghiep\n' +
          'nongnghiep"\n'
      },
      {
        'id': '63',
        'name': 'SCB',
        'name_dialog': 'SCB',
        'code': 'SCB',
        'swiftCode': 'SACLVNVX',
        'pattern': '^(970429\\d*)$',
        'bin': ['970429'],
        'auth': 'bank',
        'index': 62,
        'search': '"scb\n' +
          'sai gon"\n'
      },
      {
        'id': '64',
        'name': 'SeABank',
        'name_dialog': 'SeABank',
        'code': 'SAB',
        'swiftCode': 'SEAVVNVX',
        'pattern': '^(970440\\d{13}|970468\\d{13})$',
        'bin': ['970440'],
        'auth': 'op',
        'index': 55,
        'search': '"seabank\n' +
          'dong nam a\n' +
          'dongnama"\n'
      },
      {
        'id': '65',
        'name': 'Nam A Bank',
        'name_dialog': 'NAM A BANK',
        'code': 'NAB',
        'swiftCode': 'NAMAVNVX',
        'pattern': '^(970428\\d*)$',
        'bin': ['970428'],
        'auth': 'op',
        'index': 58,
        'search': 'namabank, nab, nam a bank, nama bank'
      },
      {
        'id': '66',
        'name': 'PVcomBank',
        'name_dialog': 'PVcomBank',
        'code': 'PVCB',
        'swiftCode': 'WBVNVNVX',
        'pattern': '^(970412\\d*)$',
        'bin': ['970412'],
        'auth': 'op',
        'index': 61,
        'search': '"pvcombank\n' +
          'dai chung\n' +
          'daichung"\n'
      },
      {
        'id': '67',
        'name': 'Techcombank',
        'name_dialog': 'Techcombank',
        'code': 'TCB',
        'swiftCode': 'VTCBVNVX',
        'pattern': '^(970407\\d{10}|889988\\d{10}|888899\\d{10})$',
        'bin': [''],
        'auth': 'bank',
        'index': 44,
        'search': '"tcb\n' +
          'techcombank\n' +
          'ky thuong\n' +
          'kythuong"\n'
      },
      {
        'id': '68',
        'name': 'Eximbank',
        'name_dialog': 'Eximbank',
        'code': 'EXIM',
        'swiftCode': 'EBVIVNVX',
        'pattern': '^(970431\\d*|707070\\d{10})$',
        'bin': ['970431'],
        'auth': 'op',
        'index': 51,
        'search': '"eib\n' +
          'exb\n' +
          'eximbank\n' +
          'xuat nhap khau\n' +
          'xuatnhapkhau"\n'
      },
      {
        'id': '69',
        'name': 'Sacombank',
        'name_dialog': 'Sacombank',
        'code': 'SACOMBANK',
        'swiftCode': 'SGTTVNVX',
        'pattern': '^(970403\\d*)$',
        'bin': ['970403'],
        'auth': 'op',
        'index': 46,
        'search': '"stb\n' +
          'scb\n' +
          'sacombank\n' +
          'sai gon thuong tin\n' +
          'thuong tin\n' +
          'thuongtin"\n'
      },
      {
        'id': '70',
        'name': 'VPBank',
        'name_dialog': 'VPBank',
        'code': 'VPB',
        'swiftCode': 'VPBKVNVX',
        'pattern': '^(970432\\d*|981957\\d{10})$',
        'bin': ['970432'],
        'auth': 'op',
        'index': 45,
        'search': '"vpbank\n' +
          'viet nam thinh vuong\n' +
          'thinh vuong\n' +
          'thinhvuong"\n'
      },
      {
        'id': '71',
        'name': 'Viet Capital Bank',
        'name_dialog': 'Viet Capital Bank',
        'code': 'VCCB',
        'swiftCode': 'VCBCVNVX',
        'pattern': '^(970454\\d*)$',
        'bin': ['970454'],
        'auth': 'op',
        'index': 63,
        'search': 'vccb\n' +
          'vietcapitalbank\n' +
          'viet capital bank\n' +
          'ban viet\n' +
          'banviet'
      },
      {
        'id': '72',
        'name': 'HSBC',
        'name_dialog': 'HSBC',
        'code': 'HSBC',
        'swiftCode': 'HSBCVNVX',
        'pattern': '^(\\d*)$',
        'bin': [''],
        'auth': 'op',
        'index': 64,
        'search': 'hsbc\n'
      },
      {
        'id': '73',
        'name': 'Standard Chartered',
        'name_dialog': 'Standard Chartered',
        'code': 'STANDARDCHARACTED',
        'swiftCode': 'SCBLVNVX',
        'pattern': '^(\\d*)$',
        'bin': [''],
        'auth': 'op',
        'index': 65,
        'search': 'standard\n' +
          'standardcharacted\n' +
          'sc\n'
      },
      {
        'id': '74',
        'name': 'FE CREDIT',
        'name_dialog': 'FE Credit',
        'code': 'FECREDIT',
        'swiftCode': 'VPBKVNVXFE',
        'pattern': '^(\\d*)$',
        'bin': [''],
        'auth': 'op',
        'index': 66,
        'search': 'fe\n' +
          'fc\n' +
          'fe credit\n'
      },
      {
        'id': '75',
        'name': 'Citibank',
        'name_dialog': 'CITIBANK',
        'code': 'CITIBANK',
        'swiftCode': 'CITIVNVX',
        'pattern': '^(\\d*)$',
        'bin': [''],
        'auth': 'op',
        'index': 67,
        'search': 'citibank\n'
      },
      {
        'id': '76',
        'name': 'Home Credit',
        'name_dialog': 'HOMECREDIT',
        'code': 'HOMECREDIT',
        'swiftCode': 'HMCRVNVX',
        'pattern': '^(\\d*)$',
        'bin': [''],
        'auth': 'op',
        'index': 68,
        'search': 'homecredit\n' +
                'home credit\n' +
                'hc\n'
      },
      {
        'id': '77',
        'name': 'Keb Hana',
        'name_dialog': 'Keb Hana',
        'code': 'CFC',
        'swiftCode': 'KOEXKRSENP',
        'pattern': '^(970466\\d*|970467\\d{10})$',
        'bin': ['970466', '970467'],
        'card_list': "970466",
        'auth': 'bank',
        'index': 102,
        'search': 'keb hana bank\n' +
          'kebhana\n'
      },
      {
        'id': '78',
        'name': 'Mirae Asset',
        'name_dialog': 'Mirae Asset',
        'code': 'MAFC',
        'swiftCode': 'MIAEKRSENP',
        'pattern': '^(970468\\d*)$',
        'bin': ['970468'],
        'card_list': "970468",
        'auth': 'bank',
        'index': 103,
        'search': 'mirae asset\n' +
          'mafc'
      },
      {
        'id': '999',
        'name': 'BANK_TEST',
        'name_dialog': 'BANKTEST',
        'code': 'BANKTEST',
        'swiftCode': 'BANKTEST',
        'pattern': '^(\\d*)$',
        'bin': [''],
        'auth': 'op',
        'index': 68,
        'search': 'banktest\n'
      }
    ]
    ;

  public getBanks() {
    return this.banks;
  }

  public getBankById(bankId: Number) {
    var b: any;
    this.banks.forEach(e => {
      if (+e.id == bankId) {
        b = e;
      }
    });
    return b;
  }

  public getBankNameById(bankId: Number) {
    var b: any;
    this.banks.forEach(e => {
      if (+e.id == bankId) {
        b = e.name;
      }
    });
    return b;
  }

  public getBankNameDialogById(bankId: Number) {
    var b: any;
    this.banks.forEach(e => {
      if (+e.id == bankId) {
        b = e.name_dialog;
      }
    });
    return b;
  }

  public getBankSwiftById(bankId: Number) {
    var b: any;
    this.banks.forEach(e => {
      if (+e.id == bankId) {
        b = e.swiftCode;
      }
    });
    return b;
  }

  public getBankPatternById(bankId: Number) {
    var b: any;
    this.banks.forEach(e => {
      if (+e.id == bankId) {
        b = e.pattern;
      }
    });
    return b;
  }

  public getBankAuthByBankSwift(bankSwift: any) {
    var b: any;
    this.banks.forEach(e => {
      if (e.swiftCode == bankSwift) {
        b = e.auth;
      }
    });
    return b;
  }

  public getBankIdByBankSwift(bankSwift: any) {
    var b: any;
    this.banks.forEach(e => {
      if (e.swiftCode == bankSwift) {
        b = e.id;
      }
    });
    return b;
  }

  public getIdByBankSwift(bankSwift: any) {
    var b: any;
    this.banks.forEach(e => {
      if(e.swiftCode === bankSwift){
        b = e.id;
      }
    });
    return b;
  }

  public getSearchByBankSwift(bankSwift: any) {
    var b: any;
    this.banks.forEach(e => {
      if(e.swiftCode === bankSwift){
        b = e.search;
      }
    });
    return b;
  }

  public getNameByBankSwift(bankSwift: any) {
    var b: any;
    this.banks.forEach(e => {
      if(e.swiftCode == bankSwift){
        b = e.name;
      }
    });
    return b;
  }

}
