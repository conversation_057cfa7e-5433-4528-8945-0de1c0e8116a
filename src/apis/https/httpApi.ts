//import { useAuthService, useLocaleService } from "@/services"
import APPC<PERSON>FIG from "@/appConfig"
import { useGlobalI18n } from "@/plugins/i18n"
import { useGlobalRouter } from "@/plugins/router"
import { useAuthService } from "@/services/authService"
import { useLocaleService } from "@/services/localeService"
import { useToastService } from "@/services/toastService"
import axios, { AxiosError } from "axios"
import moment from "moment"
import { useloggingInterceptor } from "../interceptors/loggingInterceptor"

// Way 1: Chỉ động vào axios
// https://stackoverflow.com/questions/70689305/customizing-date-serialization-in-axios
const dateTransformer = (data, header) => {
  if (data instanceof Date) {
    // do your specific formatting here
    //return data.toLocaleString()
    return moment(data).format()
  }
  if (Array.isArray(data)) {
    return data.map((val) => dateTransformer(val, header))
  }
  if (typeof data === "object" && data !== null) {
    return Object.fromEntries(Object.entries(data).map(([key, val]) => [key, dateTransformer(val, header)]))
  }
  return data
}

// Way 2: Đồng nhất kiểu trả về của Date.toJSON
// https://blog.lpains.net/posts/2020-02-13-overriding-axios-date-on-posts/
Date.prototype.toJSON = function () {
  // you can use moment or anything else you prefer to format
  // the date here
  return moment(this).format()
}

// TODO: Tạm dùng cách 2 xem phản ứng của hệ thống do mới xây dựng, còn thay đổi dễ được

// Nên đưa vào axios.d.ts
declare module "axios" {
  export interface AxiosRequestConfig {
    /**
     * Cho phép trả về 401, 403 và sẽ bỏ qua ko xử lý tự động authentication và authorization.
     */
    allowAnonymous?: boolean,
    // Nếu true, bỏ qua việc handle tự động
    handleErrorManually?: boolean
  }
}

/**
 * Http Request phải có auth.
 */
const instance = axios.create({
  baseURL: APPCONFIG.APP_API_ENDPOINT,
  withCredentials: true, // cho phép gửi cookies (WARN: phải check cors)
  //transformRequest: [dateTransformer, ...(axios.defaults.transformRequest as AxiosRequestTransformer[])],
})

useloggingInterceptor(instance)

instance.interceptors.request.use(
  (config) => {
    const localeService = useLocaleService()
    const authService = useAuthService()

    config.headers
      .set("locale", localeService.locale, false)
      .set("authUserId", authService.authUserId, false)
      .set("merchantProfileId", authService.authMerchantProfileId, false)
    if (APPCONFIG.FAKE_X_USER_ID) {
      //config.headers.set("x-user-id", APPCONFIG.FAKE_X_USER_ID, false)
      config.headers.set("x-user-id", authService.authUserEmail, false) // FAKE_X_USER_ID được login đưa vào authUserEmail
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

instance.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    // Do something with response error
    console.debug("interceptor.auth.response", "error")
    if (error.response) {
      // Request was made. However, the status code of the server response falls outside the 2xx range
      if (!error.config?.allowAnonymous && error.response.status === 401) {
        console.warn("interceptor.auth.response", "error", 401, "redirect to login page")
        // Unauthorized error
        // Redirect to login page
        const router = useGlobalRouter()
        router.push("/login")
        return Promise.reject(error)
      }
      console.warn("interceptor.auth.response", "error", error.response.status)
    }
    return Promise.reject(error)
  }
)

export function handleApiError(
  error: Error | undefined,
  /**
   * @returns false: stop handle. Otherwise, display default toast message.
   */
  customHandleAction?: (error: Error) => boolean | undefined
) {
  let showDefaultToastError: boolean | undefined = true
  if (typeof customHandleAction == "function" && error) {
    showDefaultToastError = customHandleAction(error)
  }
  if (showDefaultToastError != false) {
    const { t } = useGlobalI18n()
    const toastService = useToastService()
    // Show a generic error message
    // alert("An error occurred. Please try again later.")
    toastService.error({
      summary: t("common.toast.error-summary"),
    })
  }
}

instance.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    // Do something with response error
    console.debug("interceptor.error.response", "error")
    if (error.response) {
      // Request was made. However, the status code of the server response falls outside the 2xx range
      if (!error.config?.handleErrorManually) {
        // Show a default error message
        handleApiError(error)
      }
    } else if (error.request) {
      // Request was made but no response received
    } else {
      // Error was triggered by something else
    }
    return Promise.reject(error)
  }
)

export { instance as http }
