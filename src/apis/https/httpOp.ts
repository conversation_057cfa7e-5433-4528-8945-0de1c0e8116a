//import { useAuthService, useLocaleService } from "@/services"
import APPC<PERSON><PERSON><PERSON> from "@/appConfig"
import { useGlobalI18n } from "@/plugins/i18n"
import { useGlobalRouter } from "@/plugins/router"
import { useLocaleService } from "@/services/localeService"
import { useToastService } from "@/services/toastService"
import axios from "axios"
import { useloggingInterceptor } from "../interceptors/loggingInterceptor"

const instance = axios.create({
  baseURL: APPCONFIG.APP_API_ENDPOINT,
  withCredentials: true
})

useloggingInterceptor(instance)

instance.interceptors.request.use(
  (config) => {
    const localeService = useLocaleService()
    //const authService = useAuthService()

    config.headers.set("locale", localeService.locale, false)
    //   .set("authUserId", authService.authUserId, false)
    //   .set("merchantProfileId", authService.authMerchantProfileId, false)
    //   .set("x-user-id", APPCONFIG.FAKE_X_USER_ID, false)

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

instance.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response) {
      const { t } = useGlobalI18n()
      const toastService = useToastService()

      if (!error.config?.allowAnonymous && error.response.status === 401) {
        // Unauthorized error
        // Redirect to login page
        const router = useGlobalRouter()
        router.push("/login")
      } else {
        // Show a generic error message
        // alert("An error occurred. Please try again later.")
        toastService.error({
          summary: t("common.toast.error-summary"),
        })
      }
    }
    return Promise.reject(error)
  }
)

export { instance as httpOp }
