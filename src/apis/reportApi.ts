import { DateRangModel, IPagedRequest } from "@/types/base";
import { saveAs } from "@/utils/fileUtil";
import { http } from "./https/httpApi";
import { unwrapDataObjectArray } from "@/types/utils/typeWrapperUtil";
import moment from "moment";

export async function getListReport(request: GetListReportRequest) {
  request.dateFrom = moment(request.dateFrom).startOf("date").toDate();
  request.dateTo = moment(request.dateTo).endOf("date").toDate();

  return await http.post<GetListReportResponse>(`/v1/transaction/report/summary`, request)
}

export async function getListLinkName(dateFrom: datetime, dateTo: datetime) {
  const request = {
    dateFrom: dateFrom,
    dateTo: dateTo
  }

  //return await http.post<LinkNameResponse[]>(`/v1/transaction/report/get_link_name`, request)

  return await http.post<LinkNameResponse[]>(`/v1/transaction/report/get_link_name`, request).then((res) => {
    return unwrapDataObjectArray(res)
  })
}

export async function exportSumary(request: GetListReportRequest) {
  request.dateFrom = moment(request.dateFrom).startOf("date").toDate()
  request.dateTo = moment(request.dateTo).endOf("date").toDate()

  return await http
    .post<Blob>(`/v1/transaction/report/export_summary`, request, { responseType: "blob" })
    .then((res) => {
      saveAs(res)
    })
}

export interface GetListReportRequest extends IPagedRequest {
  keywords: string
  linkType: number
  dateFrom: datetime
  dateTo: datetime
  timeInterval: string
  exportType: number
}

export interface GetListReportResponse {
  total: number
  data: {
    currency: string
    totalAuthorizeCount: number
    totalCaptureCount: number
    totalPurchaseCount: number
    totalRefundAmount: number
    totalRevenueAmount: number
    totalVoidRefundCount: number
    transactionMetrics: TransactionMetrics[]
  }
}

export interface TransactionMetrics {
  GroupLabel: string
  Metrics: {
    NoOfAuthorizeTrans: number
    NoOfCaptureTrans: number
    NoOfPurchaseTrans: number
    NoOfRefundVoidTrans: number
    TotalRefund: number
    TotalRevenue: number
  }
}

export interface DataReportFilter {
  keywords: string //link-name (if link type = 'PaymentLink' else "")
  dateRange: DateRangModel //required
  dataTableQuery: IPagedRequest //start, length
  linkType: number //All, MyLink, PaymentLink
  timeInterval: string //Daily, Weekly, Monthly
  exportType: number // default = 1
}

export enum EnumReportLinkType {
  All = 1,
  MyLink,
  PaymentLink,
}

export enum EnumReportTimeInterval {
  Daily = "Daily",
  Weekly = "Weekly",
  Monthly = "Monthly",
}

export interface LinkNameResponse {
  name: string
}
