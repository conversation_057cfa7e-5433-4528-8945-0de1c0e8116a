import { http } from "./https/httpApi"
import { GetListMerchantProfileInput, GetListMerchantProfileOutput, MerchantProfile, PinMerchantRequest } from "./types/merchantTypes"

export async function getMerchantProfileById(id: string) {
  return await http.get<MerchantProfile>(`/v1/merchant_profile/get_merchant_profile/${id}`)
}

export async function pinMerchantProfile(params: PinMerchantRequest) {
  return await http.post("/v1/merchant/pin", params)
}

export async function getListMerchantProfile(data: GetListMerchantProfileInput) {
  return await http.post<GetListMerchantProfileOutput>(`/v1/merchant_profile/get_list`, {})
}
