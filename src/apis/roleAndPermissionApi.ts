import { http } from "./https/httpApi"
import { GetAllPermissionInput, GetAllPermissionOutput } from "./types/permissionTypes"
import {
  AddRoleInput,
  AddRoleOutput,
  CheckRoleExistInput,
  CheckRoleExistOutput,
  DeleteRoleInput,
  DeleteRoleOutput,
  EditRoleInput,
  EditRoleOutput,
  GetDetailRoleInput,
  GetDetailRoleOutput,
  GetListRoleInput,
  GetListRoleOutput,
} from "./types/roleTypes"

/**
 * Load lấy dữ liệu cả Permission và PermissionGroup
 * @param data 
 * @returns 
 */
export async function getAllPermission(data: GetAllPermissionInput) {
  return await http.get<GetAllPermissionOutput>(`/v1/common/get_all_permission`)
}

export async function getListRole(data: GetListRoleInput) {
  return await http.post<GetListRoleOutput>(`/v1/role/get_list`, data)
}

export async function getDetailRole(data: GetDetailRoleInput) {
  return await http.get<GetDetailRoleOutput>(`/v1/role/get/${data.id}`)
}

export async function addRole(data: AddRoleInput) {
  return await http.post<AddRoleOutput>(`/v1/role/add`, data)
}

export async function editRole(data: EditRoleInput) {
  return await http.post<EditRoleOutput>(`/v1/role/edit/${data.id}`, data)
}

export async function deleteRole(data: DeleteRoleInput) {
  return await http.delete<DeleteRoleOutput>(`/v1/role/delete/${data.id}`)
}

export async function checkRoleExist(data: CheckRoleExistInput) {
  return await http.post<CheckRoleExistOutput>(`/v1/role/exist`, data)
}
