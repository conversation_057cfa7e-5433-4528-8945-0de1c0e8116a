import { useLocaleService } from "@/services/localeService"
import { http } from "./https/httpApi"
import axios, { AxiosRequestConfig } from "axios"
import { useAuthService } from "@/services/authService"
import APPCONFIG from "@/appConfig"
import { IPagedRequest, IPagedResult } from "@/types/base"
import { RoleBaseInfo, RoleSimpleInfo } from "./types/roleTypes"
import { EnumUserPermissionType } from "@/app.const"
import { unwrapDataObjectArray } from "@/types/utils/typeWrapperUtil"

export interface GetUserByLoginOutput {
  name: string
  email: string
  phone: string
  address: string
  iat: number
  exp: number
  sub: string
}

export interface GetUserOutput {
  user: UserSimpleInfo
  merchants?: MerchantSimpleInfo[]
  functions?: FunctionSimpleInfo[]
  permissions?: PermissionSimpleInfo[]
  roles?: RoleSimpleInfo[]
}

export interface UserSimpleInfo {
  id: string
  userSId: string
  fullName?: string
  email: string
  locale: string
  active: boolean
}

export interface MerchantBaseInfo {
  id: string
  name: string
}

export interface MerchantSimpleInfo {
  id: string
  merchant_id?: string
  merchantSIdInstallment?: string
  merchant_name: string
  //type?: string
  //active: boolean
  pin: boolean
  currency_code?: string
}

export interface FunctionSimpleInfo {
  id: string
  abstrack?: number
  controllerUrl?: string
  directiveUrl?: string
  moduleName?: string
  moduleUrl?: string
}


export interface PermissionBaseInfo {
  id: string
  name: string
}

export interface PermissionSimpleInfo extends PermissionBaseInfo {
  type?: EnumUserPermissionType
}

/**
 * Method sử dụng header cookies auth để lấy thông tin login user.
 * @returns
 */
export async function getUserByLogin(config?: AxiosRequestConfig) {
  return await http.get<GetUserByLoginOutput>(`/v1/user/getloginuser`, {
    //allowAnonymous: true,
    //handleErrorManually: true,
    ...config,
  })
}

export async function getUserByEmail(email: string, config?: AxiosRequestConfig) {
  return await http.get<GetUserOutput>(`/v1/user-profile/${email}`, config)
}

export async function getUserBySId(sid: string, config?: AxiosRequestConfig) {
  return await http.get<GetUserOutput>(`/v1/user/user_by_sid/${sid}`, config)
}

export async function getAllCurrency(config?: AxiosRequestConfig) {

  if (APPCONFIG.APP_DEV_FAKE_HAU_CURRENCY_GETALL_URL) {
    const ngrokApi = axios.create({
      baseURL: APPCONFIG.APP_DEV_FAKE_HAU_CURRENCY_GETALL_URL,
      //baseURL: "https://9764-118-70-205-202.ngrok-free.app/api",
      //baseURL: "https://4625-118-70-124-128.ngrok-free.app/api/v1/currency/getall",
    })

    ngrokApi.interceptors.request.use(
      (config) => {
        const localeService = useLocaleService()
        const authService = useAuthService()

        config.headers
          .set("locale", localeService.locale, false)
          .set("authUserId", authService.authUserId, false)
          .set("merchantProfileId", authService.authMerchantProfileId, false)

        if (APPCONFIG.FAKE_X_USER_ID) {
          //config.headers.set("x-user-id", APPCONFIG.FAKE_X_USER_ID, false)
          config.headers.set("x-user-id", authService.authUserId, false) // FAKE_X_USER_ID được login đưa vào authUserEmail
        }

        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    //return await ngrokApi.get<OnePayCurrencyItem[]>('https://4625-118-70-124-128.ngrok-free.app/api/v1/currency/getall')
    return await ngrokApi.get<OnePayCurrencyItem[]>("", config).then((res) => {
      return unwrapDataObjectArray(res)
    })
  }

  return await http.get<OnePayCurrencyItem[]>("/v1/currency/getall", config).then((res) => {
    return unwrapDataObjectArray(res)
  })
}

export async function updateCurrency(data: CurrencyUpdateRequest[], config?: AxiosRequestConfig) {
  return await http.post<CurrencyUpdateResponse>("/v1/currency/update_all", data, config)
}

export async function getListUser(data: GetListUserInput, config?: AxiosRequestConfig) {
  return await http.post<GetListUserOutput>(`/v1/user/get_list`, data, config)
}

export async function editUser(data: EditUserInput, config?: AxiosRequestConfig) {
  return await http.post<EditUserOutput>(`/v1/user/edit/${data.id}`, data, config)
}

export async function activeUser(data: ActiveUserInput, config?: AxiosRequestConfig) {
  return await http.post<ActiveUserOutput>(`/v1/user/active`, data, config)
}

export interface EditUserInput {
  id: uuid
  active?: boolean
  merchants?: MerchantBaseInfo[]
  roles?: RoleBaseInfo[]
  permissions?: PermissionBaseInfo[]
}

export interface EditUserOutput {
  id: uuid
}

export interface ActiveUserInput {
  id: uuid
  active: boolean
}

export interface ActiveUserOutput {
  id: uuid
  active: boolean
}

export interface OnePayCurrencyItem {
  currency_code: string
  currency_name_vi: string
  currency_name_en: string
  order: number
  exchange_Rate?: number
  update?: datetime
  type: string
}

export interface PaymentLinkCurrencyItem {
  id: string
  currencyCode: string
  currencyName: string
  enabled: boolean
  updateType: number
  exchangeRate?: number
  exchangeRateAuto?: number
  lastUpdate?: datetime
  updateBy?: string
  type: string
  imgSrc?: string
}

export interface CurrencyUpdateRequest {
  //merchantprofile_id: string
  id: string
  currency: string
  update_type: number
  exchange_rate: string
  enabled: boolean
}

export interface CurrencyUpdateResponse {
  type: string
  title?: string
  status: number
  detail?: string
  instance: string
}

export interface GetListUserInput extends IPagedRequest {
  keywords: string
  merchantProfile: string | string[] | null
  status: number | null
}

export interface GetListUserOutput extends IPagedResult<GetListUserItem> {}

export interface GetListUserItem {
  id: uuid
  userSId?: string
  fullName?: string
  email?: string
  locale?: string
  active?: boolean
  lastModifyDate?: Date
  merchants?: MerchantBaseInfo[]
  roles?: RoleBaseInfo[]
  permissions?: PermissionSimpleInfo[]
}
