import { RootObject, User } from "@/types/demo2"
import { http } from "./https/httpApi"

export const baseURL = "https://dummyjson.com"

export async function getUsers(input?: object) {
  // $.ajax({
  //   //url: `/api/WeatherForecastProxy/WeatherForecast`,
  //   url: `http://192.168.70.160:8888/WeatherForecast`,
  //   method: "GET",
  //   // headers: {
  //   //   MyCustomHeader1: "1",
  //   // },
  // }).then((data) => {
  //   console.log(data)
  // })

  //http.get(`/WeatherForecastProxy/WeatherForecast`)

  return await http.get<RootObject>(`${baseURL}/users`)
}

export async function getUserById(id: number) {
  return await http.get<User>(`${baseURL}/users/${id}`)
}

// export async function createStudent(input: CreateStudentInput) {
//   return await http.post<APIResponse<Student>>(`${baseURL}/unicorns`, input)
// }

// export async function updateStudent(input: UpdateStudentInput) {
//   return await http.put<APIResponse<boolean>>(`${baseURL}/unicorns`, input)
// }

// export async function deleteStudentById(id: number) {
//   return await http.delete<APIResponse<boolean>>(`${baseURL}/unicorns/${id}`)
// }