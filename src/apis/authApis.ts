import { httpOp } from "./https/httpOp"
import APPCONFIG from "@/appConfig"

// export const baseURL = "192.168.66.63:4567"

export type LoginInput = {
  username: string
  password: string
  returnUrl?: string
}

// export async function login(input?: LoginInput) {
//   return await http.get<Student[]>(`${baseURL}/unicorns`)
// }

export async function logout() {
  return await httpOp.delete(APPCONFIG.APP_LOGOUT_URL, { data: { system: "payment-link" } })
}

