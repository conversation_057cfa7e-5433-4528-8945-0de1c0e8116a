import { IPagedRequest, IPagedResult } from "@/types/base"
import { http } from "./https/httpApi"
import { EnumExportType, EnumTransactionType } from "@/app.const"
import { saveAs } from "@/utils/fileUtil"
import { AxiosRequestConfig, AxiosResponse } from "axios"
import { unwrapCaptureObject, unwrapRefundObject, unwrapVoidObject } from "@/types/utils/typeWrapperUtil"

export async function getListAllTrans(data: GetListTransInput, config?: AxiosRequestConfig) {
  return await http.post<ListTransaction>(`/v1/transactions`, data, config)
}

export async function detailTrans(transId: string, config?: AxiosRequestConfig) {
  return await http.get<GetDetailTransaction>(`/v1/transaction/${transId}`, config)
}

export async function getListRefund(data: GetListRefundInput, config?: AxiosRequestConfig) {
  return await http.post<GetListRefundOutput>(`/v1/transaction/get_list_refund`, data, config)
}

export async function refundTransaction(
  transType: string,
  refundRequest: RefundRequest,
  bnplReuquest: BnplRequestRefund,
  config?: AxiosRequestConfig
): Promise<AxiosResponse<RefundResponse, any>> {
  switch (transType) {
    case "international":
      return await http
        .post<RefundResponse>("/v1/transaction/international/purchase/refund", refundRequest, config)
        .then((res) => {
          return unwrapRefundObject(res)
        })
    case "domestic":
      return await http.post<RefundResponse>("/v1/transaction/domestic/refund", refundRequest, config).then((res) => {
        return unwrapRefundObject(res)
      })
    case "mpay":
      return await http.post<RefundResponse>("/v1/transaction/mpay/refund", refundRequest, config).then((res) => {
        return unwrapRefundObject(res)
      })
    case "bnpl":
      return await http.post<RefundResponse>("/v1/transaction/bnpl/refund", bnplReuquest, config).then((res) => {
        return unwrapRefundObject(res)
      })
    default:
      throw new Error(`The transType '${transType}' is not supported!`)
      break
  }
}

export async function voidTransaction(
  transType: string,
  voidRequest: VoidRequest,
  bnplReuquest: BnplRequestVoid,
  transactionTypeMic?: number,
  config?: AxiosRequestConfig
) {
  switch (transType) {
    case "international":
      switch (transactionTypeMic) {
        case EnumTransactionType.Authorize:
          voidRequest.path = "/void-authorize";

          return await http
            .post<VoidResponse>("/v1/transaction/international/authorize/void", voidRequest, config)
            .then((res) => {
              return unwrapVoidObject(res)
            })
        case EnumTransactionType.Purchase:
          voidRequest.path = "/void-purchase"

          return await http
            .post<VoidResponse>("/v1/transaction/international/purchase/void", voidRequest, config)
            .then((res) => {
              return unwrapVoidObject(res)
            })
        case EnumTransactionType.Capture:
          voidRequest.path = "/void-capture"

          return await http
            .post<VoidResponse>("/v1/transaction/international/capturerefund/void", voidRequest, config)
            .then((res) => {
              return unwrapVoidObject(res)
            })
        case EnumTransactionType.RefundCapture:
          voidRequest.path = "/void-capture-refund"

          return await http
            .post<VoidResponse>("/v1/transaction/international/capturerefund/void", voidRequest, config)
            .then((res) => {
              return unwrapVoidObject(res)
            })
        default:
          return await http
            .post<VoidResponse>("/v1/transaction/international/purchase/void", voidRequest, config)
            .then((res) => {
              return unwrapVoidObject(res)
            })
      }
    case "bnpl":
      return await http.post<VoidResponse>("/v1/transaction/bnpl/void", bnplReuquest, config).then((res) => {
        return unwrapVoidObject(res)
      })
    default:
      break
  }
}

export async function exportTransactions(data: ExportTransactionsInput, config?: AxiosRequestConfig) {
  return await http.post<Blob>(`/v1/transaction/export`, data, { responseType: "blob", ...config }).then((res) => {
    saveAs(res)
  })
}

export async function captureTransaction(
  transType: string,
  captureRequest: CaptureRequest,
  config?: AxiosRequestConfig
) {
  switch (transType) {
    case "international":
      return await http
        .post<CaptureResponse>("/v1/transaction/international/authorize/capture", captureRequest, config)
        .then((res) => {
          return unwrapCaptureObject(res)
        })
    default:
      break
  }
}

export async function approveRejectTransaction(data: ApproveRejectTransactionRequest[], config?: AxiosRequestConfig) {
  return await http.post<ApproveRejectTransactionResponse>("/v1/transaction/approve_reject", data, config)
}

export async function exportTransactionsRefund(data: ExportRefundInput, config?: AxiosRequestConfig) {
  return await http
    .post<Blob>(`/v1/transaction/export_refund`, data, { responseType: "blob", ...config })
    .then((res) => {
      saveAs(res)
    })
}

export interface GetListTransInput extends IPagedRequest {
  from_date?: datetime
  dateTo?: datetime
  to_date?: datetime
  merchant_id?: string
}

export interface GetListTransOutput extends IPagedResult<GetListTranItem> {
  totalAmount: number
}

export interface GetListTranItem {
  id: string
  merchantprofileID: string
  orderType: number
  orderRef: string
  customerName?: string
  email?: string
  phoneNumber?: string
  address?: string
  amount: number
  currency: string
  paymentMethod?: string
  cardno?: string
  transID: string
  transDate: datetime
  transType?: number
  transData?: string
  lastModifyDate?: datetime
  lastModifyBy?: string
  status: number
  cardType?: string
  cardUID?: string
  cardHolderName?: string
  cardExp?: string
  itaBank?: string
  itaFeeAmount?: number
  itaTime?: number
  responseCode?: string
  message?: string
  isIpnUpdate?: boolean
  bankName?: string
  itaMonthlyAmount?: string
  linkName?: string
  linkUri?: string
  merchantTransRef?: string
  authorizationCode?: string
  avs?: string
  cscResultCode?: string
  customerNote?: string
  networkTransID?: string
  refundCaptureTotal?: number
  refundTotal?: number
  riskManagement?: string
  transactionRefId?: string
  transactionRefNumber?: string
  commercialCardIndicator?: string
  merchantTransactionRef?: string
  transactionId?: string
}

export interface GetListRefundItem {
  id: string
  transID: string
  transDate: datetime
  transType?: string
  lastModifyDate?: datetime
  lastModifyBy?: string
  status: int
  linkName?: string
  linkUri?: string
  merchantTransRef?: string
  refundTotal?: number
  currency?: string
  transactionId?: string
  service?: string
  merchantId?: string
  orderType?: number
}

export interface TransactionColumnDisplay {
  isChecked: boolean
  ordinal: number
  isDisabled: boolean
  value: number
  field: string
  isDisplay: boolean
}

export interface TransactionHistory {
  id: string
  merchantProfileId: string
  transactionId: string
  amount: number
  currency: string
  transID: string
  transDate: datetime
  transType: number
  transData: string
  updateDate: datetime
  updateBy: string
  updateNotes?: string
  status: number
}

export interface Transaction {
  id: string
  transactionId: string
  note: string
  paymentDate: datetime
  cardNumber: string
  cardType: string
  merchantId : string
  currency: string
  orderInfo: string
  transRef: string
  status: string
  responseCode: string
  message: string
}

export interface GetDetailTransaction {
  paymentlink_model: DetailTransaction_PaymentlinkModel
  transaction: GetListTranItem
  transaction_details: TransactionDetailMaApp
  transaction_histories: TransactionHistory[]
}

export interface DetailTransaction_PaymentlinkModel {
  customerNoteLckey: string
  infoFieldOptions: number
  merchantName: string
  name: string
  paymentLinkId: uuid
  routerUri: string

  totalAmount: number
  currency: string
  exchangeRate: number
  paymentAmount: number
  paymentCurrency: string
}

export interface ListTransaction {
  list: Transaction[]
  total_records: number
}


export type TranStatus = {
  label: string
  class: string
}

export interface GetListRefundInput extends IPagedRequest {
  keywords?: string
  dateFrom?: datetime
  dateTo?: datetime
  refundType?: int[]
  refundStatus?: int[]
}

export interface GetListRefundOutput extends IPagedResult<GetListRefundItem> {
  totalAmount: number
}

export interface TransactionDetailMaApp {
  id: string
  merchantId: string
  service: string
  transactionType: string
  originalId: string
  merchantTxnRef: string
  canRefund: boolean
  canApprove: boolean
  canReject: boolean
  canVoid: boolean
  canCapture: boolean
  header: TranHeaderMaApp
  body: TranBodyMaApp[]
  captureData: {
    disable: boolean
    amount: number
    note: string
  }
  refundData: {
    disable: boolean
    amount: number
  }
  approveData: {
    disable: boolean
    note: string
  }
  discount: number
  provider: string
  settlementAmount: number
}

export interface TranHeaderMaApp {
  orderInfo: string
  cardNumber: string
  icon: string
  amount: number
  currency: string
  transactionDate: string
  transactionAdvanceStatus: string
  transactionStatusColor: {
    background: string
    text: string
  }
  responseCode: {
    code: string
    response: string
    reason: string
  }
}

export interface TranBodyMaApp {
  order: number
  content: TranBodyMaAppContent[]
}

export interface TranBodyMaAppContent {
  order?: number
  label?: string
  value?: string
  iconPrefix?: string
  required?: boolean
  enableCopy?: boolean
}

export interface TransactionAction {
  title: string
  isVoid: boolean
  isApprove: boolean
  isReject: boolean
  isCapture: boolean
  isRefund: boolean
  textAction: string
}

export interface RefundRequest {
  id?: string
  op?: string
  path?: string
  value?: {
    merchant_id?: string
    amount?: number
    transaction_reference?: string
    currency?: string
    note?: string
    order_info?: string | null
    cvv?: string | null
    user_name?: string | null
  }
  skipCallSynchronize?: boolean
}

export interface RefundResponse {
  transaction_id: string
  merchant_id: string
  original_transaction_id: string
  merchant_transaction_ref: string
  operator_id: string
  transaction_time: string
  amount: {
    total: string
    originalTotal: string
    currency: string
    refund_total: string
  }
  status: string
  transaction_type: string
  data: string
  parent_id: string
  n_type: string
  brandId: string
  dataPromotion: string
  sTransRefId: string
  paymentAmount: string
  note: string
  error?: string
  /**
   * Là prop của MIC bổ sung vào object trả về của OP.
   *
   * Là Id của Refund transaction để hỗ trợ redirect sang màn detail của nó.
   */
  redirect_id: uuid
}

export interface VoidRequest {
  id?: string
  op?: string
  path?: string
  value?: {
    merchant_id?: string
    amount?: number
    transaction_reference?: string
    currency?: string
    note?: string
  }
  skipCallSynchronize?: boolean
}

export interface VoidResponse {
  transaction_id: string
  original_transaction_id: string
  response_code: string
  reference_number: string
  merchant_transaction_ref: string
  transaction_type: string
  amount: {
    total: string
    currency: string
    refund_total: string
  }
  status: string
  operator_id: string
  merchant_id: string
  transaction_time: string
  advance_status: string
  financial_transaction_id: string
  note: string
  parent_id: string
  subHistories: string
  error?: string
  /**
   * Là prop của MIC bổ sung vào object trả về của OP.
   *
   * Là Id của Refund transaction để hỗ trợ redirect sang màn detail của nó.
   */
  redirect_id: uuid
}

export interface CaptureRequest {
  id?: string
  op?: string
  path?: string
  value?: {
    merchant_id?: string
    amount?: number
    transaction_reference?: string
    currency?: string
    note?: string
    order_info?: string | null
    cvv?: string | null
    user_name?: string
  }
  skipCallSynchronize?: boolean
}

export interface CaptureResponse {
  map: {
    status: 200
    message_en?: string
    message_vi?: string
    /**
     * Là prop của MIC bổ sung vào object trả về của OP.
     *
     * Là Id của Refund transaction để hỗ trợ redirect sang màn detail của nó.
     */
    redirect_id: uuid
  }
  error?: string
}

export interface ExportTransactionsInput {
  exportType: EnumExportType
  keywords?: string
  dateFrom?: datetime
  dateTo?: datetime
  transactionType?: number[]
  paymentMethod?: string[]
  transactionStatus?: int[]
}

export interface BnplRequestRefund {
  transactionId?: string
  merchantId?: string
  amount?: number
  note?: string
  canceledby?: string
  currency?: string
  provider?: string
  refundable?: boolean
  settlementAmount?: number
  discount?: number
  //transaction_reference?: string
  merchantTransRef?: string
}

export interface BnplRequestVoid {
  merchant_id?: string
  transaction_id?: string
  merchant_transaction_ref?: string
  reason?: string
  note?: string
}

export interface ApproveRejectTransactionRequest {
  id?: string
  op?: string
  path?: string
  value?: {
    merchant_id?: string
    transaction_reference?: string
    currency?: string
  }
  skipCallSynchronize?: boolean
  service?: string
  note?: string
}

export interface ApproveRejectTransactionResponse {
  code?: number
  message?: string
  errors?: any[]
}

export interface ExportRefundInput {
  exportType: EnumExportType
  keywords?: string
  dateFrom?: datetime
  dateTo?: datetime
  refundType?: number[]
  refundStatus?: number[]
}
