import { APIResponse, GetStudentInput, CreateStudentInput, Student, UpdateStudentInput } from "@/types/demo"
import { http } from "./https/httpApi"

export const baseURL = "https://crudcrud.com/api/1b75521d03014e8dbe46b14ae23e3f02"

export async function getStudents(input?: GetStudentInput) {
  return await http.get<Student[]>(`${baseURL}/unicorns`)
}

export async function getStudentById(id: number) {
  return await http.get<Student[]>(`${baseURL}/unicorns/${id}`)
}

export async function createStudent(input: CreateStudentInput) {
  return await http.post<APIResponse<Student>>(`${baseURL}/unicorns`, input)
}

export async function updateStudent(input: UpdateStudentInput) {
  return await http.put<APIResponse<boolean>>(`${baseURL}/unicorns`, input)
}

export async function deleteStudentById(id: number) {
  return await http.delete<APIResponse<boolean>>(`${baseURL}/unicorns/${id}`)
}