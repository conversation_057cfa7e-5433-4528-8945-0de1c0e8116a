import { http } from "./https/httpApi"
import { EnumReportTimeInterval, TransactionMetrics } from "./reportApi"

export async function dashBoard(request: DashBoardRequest) {
  return http.post<DashBoardResponse>("/v1/transaction/dashboard", request)
}

export interface DashBoardRequest {
  dateFrom: datetime
  dateTo: datetime
  timeInterval: EnumReportTimeInterval
}

export interface DashBoardResponse {
  transactionMetrics: TransactionMetrics[]
  totalPurchaseCount: number
  totalAuthorizeCount: number
  totalCaptureCount: number
  totalVoidRefundCount: number
  totalRevenueAmount: number
  totalRefundAmount: number
  currency: string
}
