import type { AxiosInstance } from "axios"

export const useloggingInterceptor = (instance: AxiosInstance) => {
  return instance.interceptors.response.use(
    function logResponse(res) {
      console.log(
        "%c HttpRequest Success",
        "color: #4CAF50; font-weight: bold",
        `${res.config.method?.toUpperCase()} ${res.config.url}`,
        res
      )
      return res
    },

    function logPromiseError(err) {
      console.log(
        "%c HttpRequest Error",
        "color: #EC6060; font-weight: bold",
        `${err.config.method?.toUpperCase()} ${err.config.url}`,
        err
      )
      return Promise.reject(err)
    }
  )
}
