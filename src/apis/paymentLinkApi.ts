import { EnumExportType, EnumPaymentLinkStatus } from "@/app.const"
import {
  OnePayInstallmentResponse,
  type CheckIsExit,
  type CurrencyConfig,
  type CustomerNoteLocale,
  type IsExit,
  type PaymentLinkRequest,
  type PaymentLocale,
  type PaymentMethods,
} from "@/types/apis"
import type { IPagedRequest, IPagedResult } from "@/types/base"
import moment from "moment"
import { http } from "./https/httpApi"
import { unwrapDataObjectArray } from "@/types/utils/typeWrapperUtil"
import APPCONFIG from "@/appConfig"

export async function getAllPaymentMethod() {
  //return await http.get<PaymentMethods[]>(`/v1/common/get_all_paymentmethod`)

  return await http.get<PaymentMethods[]>(`/v1/common/get_all_paymentmethod`).then((res) => {
    return unwrapDataObjectArray(res)
  })
}

export async function getAllLocale() {
  //return await http.get<PaymentLocale[]>(`/v1/common/get_all_locale`)

  return await http.get<PaymentLocale[]>(`/v1/common/get_all_locale`).then((res) => {
    return unwrapDataObjectArray(res)
  })
}

export async function getListPaymentLink(input: GetListPaymentLinkInput) {
  // Chỉ lấy đầu ngày - cuối ngày
  input.dateFrom = !input.dateFrom ? undefined : moment(input.dateFrom).startOf("date").toDate()
  input.dateTo = !input.dateTo ? undefined : moment(input.dateTo).endOf("date").toDate()

  return await http.post<GetListPaymentLinkOutput>(`/v1/paymentlink/get_list`, input)
}

export async function getListPaymentLinkTrans(input: GetListPaymentLinkTransInput) {
  // Chỉ lấy đầu ngày - cuối ngày
  input.dateFrom = !input.dateFrom ? undefined : moment(input.dateFrom).startOf("date").toDate()
  input.dateTo = !input.dateTo ? undefined : moment(input.dateTo).endOf("date").toDate()

  return await http.post<GetListPaymentLinkTransOutput>(`/v1/paymentlink/get_list_paymentlink_trans`, input)
}

export async function pinPaymentLink(input: PinPaymentLinkInput) {
  return await http.post(`/v1/paymentlink/pin`, input)
}

export async function exportPaymentLink(input: ExportPaymentLinkInput) {
  //await delay() // For test only
  return await http.post<Blob>(`/v1/paymentlink/export_paymentlink`, input, { responseType: "blob" })

  // return await http.post<Blob>(`/v1/paymentlink/export_paymentlink`, input, { responseType: "blob" }).then((res) => {
  //   saveAs(res)
  //   //return new File([res.data], "test.data") // vẫn ko thể trả ra được
  // })

  // Cách dưới dây chưa thể thực hiện. Không thấy return được value ra mà bị dừng luôn action.
  //return await http.post<Blob>(`/v1/paymentlink/export_paymentlink`, input, { responseType: "blob" })
}

export async function exportPaymentLinkTrans(input: ExportPaymentLinkTransInput) {
  //await delay() // For test only
  return await http.post<Blob>(`/v1/paymentlink/export_paymentlink_trans`, input, { responseType: "blob" })

  // return await http
  //   .post<Blob>(`/v1/paymentlink/export_paymentlink_trans`, input, { responseType: "blob" })
  //   .then((res) => {
  //     saveAs(res)
  //   })
}

export async function setEnableStatus(input: SetEnableStatusInput) {
  return await http.post<void>("/v1/paymentlink/status", input)
}

export async function addNew(input: PaymentLinkRequest) {
  return await http.post<AddPaymentlinkOutput>("/v1/paymentlink/add", input)
}

export async function getById(input: uuid) {
  return await http.get<GetByIdPaymentLinkOutput>(`/v1/paymentlink/get/${input}`)
}

export async function upload(input: any) {
  const formData = new FormData()
  formData.append("file", input[0])
  return await http.post<FileRespone>("/v1/common/file/upload", formData)
}

export async function download(fileId: string) {
  //await http.get(`/v1/file/${input}`)
  return await http.get<Blob>(`/v1/file/${fileId}`, { responseType: "blob" })

  // return await http.get<Blob>(`/v1/file/${fileId}`, { responseType: "blob" }).then((res) => {
  //   saveAs(res)
  // })
}

export async function getCurrencyConfig(merchantProfileId: string) {
  //return await http.get<CurrencyConfig[]>(`v1/merchant_profile/get_currency_config/${merchantProfileId}`)

  return await http
    .get<CurrencyConfig[]>(`v1/merchant_profile/get_currency_config/${merchantProfileId}`)
    .then((res) => {
      return unwrapDataObjectArray(res)
    })
}

export async function edit(input: PaymentLinkRequest, id: string) {
  return await http.post<EditPaymentlinkOutput>(`/v1/paymentlink/edit/${id}`, input)
}

export async function getInstallmentsSetting(merchantId: string) {
  // get amount from config
  let amount = APPCONFIG.APP_GET_INSTALLMENT_OF_AMOUNT
  let getFromDefault = false
  // get default if cannot load from config
  if (!amount || isNaN(amount)) {
    amount = 3000000
    getFromDefault = true
  }
  console.log("getInstallmentsSetting.amount", amount, getFromDefault ? "from default" : "from config")

  return await http.get<OnePayInstallmentResponse>(`v1/common/get_installments/${amount}`)
}

export async function checkExits(input: CheckIsExit) {
  return await http.post<IsExit>(`/v1/common/check_router_uri`, input)
}

export interface GetListPaymentLinkItem {
  id: string
  merchantProfileId: string
  routerUri: string
  name: string
  description?: string
  dueDate?: Date
  paymentMethods: number
  installmentSettings: string
  hasPresetAmount: boolean
  presetAmountJson?: string
  totalAmount?: number
  currency?: string
  exchangeRate?: number
  paymentAmount?: number
  paymentCurrency?: string
  hasLimitPayment: boolean
  maxPaymentQty?: number
  totalPaymentQty?: number
  infoFieldOptions: number
  fileId?: string
  fileName?: string
  isPinned: boolean
  enabled?: boolean
  status?: EnumPaymentLinkStatus
  customerNoteLckey?: CustomerNoteLocale[]
}

export const isGetListPaymentLinkItem = (value: GetListPaymentLinkItem): value is GetListPaymentLinkItem =>
  !!value?.id && !!value?.merchantProfileId && !!value?.name

export interface GetListPaymentLinkInput extends IPagedRequest {
  keywords?: string
  linkStatus?: int[]
  dateFrom?: datetime
  dateTo?: datetime
  linkType?: number
}

export interface AddPaymentlinkOutput {
  id: uuid
}

export interface EditPaymentlinkOutput {
  id: uuid
}

export interface GetListPaymentLinkOutput extends IPagedResult<GetListPaymentLinkItem> {}

export interface GetListPaymentLinkTransItem {
  id: uuid
  merchantprofileID: uuid
  paymentlinkId: uuid
  orderType: number
  orderRef: string
  customerName?: string
  email?: string
  phoneNumber?: string
  address?: string
  amount: number
  currency: string
  paymentMethod?: number
  cardno?: string
  transID: string
  transDate: datetime
  transType?: number
  transData?: string
  lastModifyDate?: datetime
  lastModifyBy?: string
  status: number
  cardType?: string
  cardUID?: string
  cardHolderName?: string
  cardExp?: string
  itaBank?: string
  itaFeeAmount?: number
  itaTime?: number
  responseCode?: string
  message?: string
  isIpnUpdate?: boolean
  bankName?: string
  itaMonthlyAmount?: string
}

export interface GetListPaymentLinkTransInput extends IPagedRequest {
  paymentLinkId: uuid
  status?: int[]
  dateFrom?: datetime
  dateTo?: datetime
}

export interface GetListPaymentLinkTransOutput extends IPagedResult<GetListPaymentLinkTransItem> {}

export interface PinPaymentLinkInput {
  id: uuid
  pin: boolean
}

export interface ExportPaymentLinkInput {
  exportType: EnumExportType
  keywords?: string
  linkStatus?: int[]
  dateFrom?: datetime
  dateTo?: datetime
}

export interface ExportPaymentLinkTransInput {
  exportType: EnumExportType
  paymentLinkId: uuid
  status?: int[]
  dateFrom?: datetime
  dateTo?: datetime
}

export interface GetByIdPaymentLinkOutput {
  id: string
  merchantProfileId: string
  routerUri: string
  name: string
  description?: string
  dueDate?: Date
  paymentMethods: number
  installmentSettings?: string
  hasPresetAmount: boolean
  presetAmountJson?: string
  totalAmount?: number
  currency?: string
  exchangeRate?: number
  paymentAmount?: number
  paymentCurrency?: string
  hasLimitPayment: boolean
  maxPaymentQty?: number
  totalPaymentQty?: number
  inprogressPayments?: number
  infoFieldOptions: number
  customerNoteLckey?: CustomerNoteLckeyType[]
  createDate?: datetime
  createByUserId?: uuid
  createByUser?: UserInfo
  lastModifyDate?: datetime
  lastModifyByUserId?: uuid
  lastModifyByUser?: UserInfo
  enabled?: boolean
  status?: EnumPaymentLinkStatus
  fileId?: string
  fileName?: string
  isPinned: boolean
  linkType: number
}

export interface UserInfo {
  // id?: uuid
  name?: string
  email?: string
  active?: boolean
}

export interface CustomerNoteLckeyType {
  locale?: string
  value?: string
}

export interface FileRespone {
  id: string
  fileName: string
}

export interface SetEnableStatusInput {
  id: uuid
  enabled: boolean
}
