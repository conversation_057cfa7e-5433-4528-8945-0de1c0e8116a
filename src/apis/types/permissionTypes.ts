export interface GetAllPermissionInput {}

export interface GetAllPermissionOutput {
  permissions: PermissionItem[]
  permissionGroups: PermissionGroupItem[]
}

export interface PermissionItem {
  id: uuid,
  name: string,
  description?: string,
  ordinal?: number,
  permissionGroupId?: uuid
}

export interface PermissionGroupItem {
  id: uuid,
  name: string,
  description?: string,
  ordinal?: number,
}