import { IPagedRequest, IPagedResult } from "@/types/base"

export interface TermsConditions {
  locale: string
  value?: string
}

export interface MerchantProfile {
  id: string
  merchantSId: string
  name?: string
  logoFileId?: string
  email?: string
  phone?: string
  address?: string
  websiteUrl?: string
  defaultCurrency?: string
  termsConditionsLckey?: string
  termsConditions: TermsConditions[]
  enableMylink: boolean
  mylinkRouterUri?: string
  mylinkInfoFieldOptions?: int
  mylinkCustomerNoteLckey?: string
  mylinkTotalPayments?: bigint

  cfgPaymentMethods?: int
  cfgEmailNotifyTypes?: int
  cfgEmailNotifyList?: string
  cfgEmailRemindUnpaidInvoice?: boolean
  cfgEmailRemindInvAfterHours?: string //	data có dạng array int
  cfgUseAuthorizeCapture?: boolean
  approveDate?: datetime //	có timezone
  approveByUserId?: string
  //approveByUser?:	 	User object	O	Nếu editMode = true, trả về object User nếu có
  approveNotes?: string
  createDate?: datetime //	có timezone
  createByUserId?: uuid
  //createByUser?:	 	User object	O	//Nếu editMode = true, trả về object User nếu có
  lastModifyDate?: datetime //có timezone
  lastModifyByUserId?: uuid
  //lastModifyByUser?:	 	User object	//Nếu editMode = true, trả về object User nếu có
  status?: int
}

export interface PinMerchantRequest {
  merchantId: string
  pin: boolean
}

export interface GetListMerchantProfileInput extends IPagedRequest {
  keywords?: string
}

export interface GetListMerchantProfileOutput extends IPagedResult<MerchantProfileItem> {}

export interface MerchantProfileItem {
  id: uuid
  name: string
  logoFileId?: string
  merchantSId?: string
  merchantSIdInstallment?: string
}