import { IPagedRequest, IPagedResult } from "@/types/base"
import { PermissionBaseInfo } from "../userApis"

export interface RoleBaseInfo {
  id: string
  name: string
}

export interface RoleSimpleInfo extends RoleBaseInfo {
  id: uuid
  name: string
  description?: string
}

export interface GetListRoleInput extends IPagedRequest {
  name?: string
}

export interface GetListRoleOutput extends IPagedResult<GetListRoleItem> {}

export interface GetListRoleItem {
  id: uuid
  name: string
  description?: string
  permissions?: PermissionBaseInfo[]
}

export interface GetDetailRoleInput {
  id: uuid
}

export interface GetDetailRoleOutput {
  id: uuid
  name: string
  description?: string
  permissions?: PermissionBaseInfo[]
}

export interface AddRoleInput {
  id: uuid
  name: string
  description?: string
  permissions?: PermissionBaseInfo[]
}

export interface AddRoleOutput {
  id: uuid
}

export interface EditRoleInput {
  id: uuid
  name: string
  description?: string
  permissions?: PermissionBaseInfo[]
}

export interface EditRoleOutput {
  id: uuid
}

export interface DeleteRoleInput {
  id: uuid
}

export interface DeleteRoleOutput {
  id: uuid
}

export interface CheckRoleExistInput {
  id: Nullable<uuid>,
  name: uuid
}

export interface CheckRoleExistOutput {
  exist: boolean
}
