export enum AppRouteNames {
  HOME = "home",
  DASHBOARD = "dashboard",
  PAYMENTLINK_LIST = "paymentlink",
  PAYMENTLINK_DETAIL = "paymentlink-detail",
  PAYMENTLINK_CREATE = "paymentlink-create",
  PAYMENTLINK_EDIT = "paymentlink-edit",
  PAYMENTLINK_DETAIL__TRANSDETAIL = "paymentlink-detail--trans-detail",
  TRANSACTIONMANAGEMENT_LIST = "transactionManagement",
  TRANSACTIONMANAGEMENT_DETAIL = "transactionManagement-detail",
  REPORT = "report",
  LOGIN = "login",
  LOGOUT = "logout",
  PROFILE = "profile",
  SETTINGS = "settings",
  TEST_LANG = "test_lang",
  ERROR_401Unauthorized = "401",
  ERROR_403Forbidden = "403",
  ERROR_404NotFound = "404",
  ERROR_403NoMerchant = "403-NoMerchant",
  ERROR_403NoAppPermission = "403-NoAppPermission",
  TEST_COMPONENT = "test_component",
  ADMIN_USERMANAGEMENT = "admin-userManagement",
  ADMIN_USERMANAGEMENT_EDIT = "admin-userManagement-edit",
  ADMIN_ROLEMANAGEMENT = "admin-roleManagement",
  ADMIN_ROLEMANAGEMENT_ADD = "admin-roleManagement-add",
  ADMIN_ROLEMANAGEMENT_EDIT = "admin-roleManagement-edit",
  TRANSACTION_RESULT = "payments",
}
