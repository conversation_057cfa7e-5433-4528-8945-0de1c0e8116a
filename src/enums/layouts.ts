export enum AppLayoutNames {
  Default = "DefaultLayout",
  ErrorNoMenu = "ErrorNoMenuLayout",
  Admin = "AdminLayout",
  Custom = "CustomLayout",
  Empty = "EmptyLayout",
}

export const AppLayoutMapper = {
  [AppLayoutNames.Default]: async () => await import(`@/layouts/default/DefaultLayout.vue`),
  [AppLayoutNames.Admin]: async () => await import(`@/layouts/admin/AdminLayout.vue`),
  [AppLayoutNames.Custom]: async () => await import(`@/layouts/custom/CustomLayout.vue`),
  [AppLayoutNames.Empty]: async () => await import(`@/layouts/empty/EmptyLayout.vue`),
  [AppLayoutNames.ErrorNoMenu]: async () => await import(`@/layouts/errorNoMenu/ErrorNoMenuLayout.vue`),
}

export const DefaultLayoutName = AppLayoutNames.Default