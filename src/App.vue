<script setup lang="ts">
import { useHeadSafe } from "@vueuse/head"
import APPCONFIG from "./appConfig"
import { useAuthService } from "./services/authService"
import { useLayoutService } from "./services/layoutService"
import { useLocaleService } from "./services/localeService"

import Layout from "@/layouts/Layout.vue"
import { useNavigatorService } from "./services/navigatorService"
import { useRouter } from "vue-router"
import { useRouterService } from "./services/routerService"
import { useToastService } from '@/services/toastService';

const router = useRouter()
const layoutService = useLayoutService()
const navigatorService = useNavigatorService()
const routerService = useRouterService()
const localeService = useLocaleService()
const authService = useAuthService()
const toastService = useToastService()

navigatorService.wrapRouter(router)
routerService.initService(router)
localeService.initLocale()

useHeadSafe({
  templateParams() {
    return {
      siteName: APPCONFIG.APP_TITLE,
      mode: APPCONFIG.MODE_PROD ? "" : ` (${APPCONFIG.MODE})`,
      separator: "|",
      subPage: null, // empty
    }
  },
  //titleTemplate: (title?: string) => `${title} - ${APPCONFIG.APP_TITLE}`,
  titleTemplate: "%s %separator %siteName %mode",
})
</script>

<template>
  <Layout></Layout>

  <AppConfirmDialogManager />
  <AppToastManager />
</template>

<style lang="scss"></style>
