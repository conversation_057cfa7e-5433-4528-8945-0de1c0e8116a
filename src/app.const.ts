export enum InfoFieldOptions {
  enableName = 1,
  requireName = 2,
  enableEmail = 4,
  requireEmail = 8,
  enablePhone = 16,
  requirePhone = 32,
  enableAddress = 64,
  requireAddress = 128,
  enableNotes = 256,
  requireNotes = 512,
}

/** Trạng thái của Payment Link */
export enum EnumPaymentLinkStatus {
  /** PaymentLink sẵn sàng giao dịch */
  Active = 1,
  /** PaymentLink  đã đủ số lượng giới hạn thanh toán */
  LimitReached = 2,
  /** PaymentLink đã hết hạn (DueDate) */
  Expired = 3,
  // Trạng thái của one time link
  Successful = 4,
}

/** Dùng cho trang tìm kiếm payment link theo status */
export enum EnumSearchPaymentLinkStatus {
  // Chú ý sắp xếp thứ tự để order ở màn Link Management list
  SearchActive = 1,
  SearchExpired = 3,
  SearchInactive = -1,
  SearchLimitReached = 2,
  SearchSuccessful = 4,
}

/** Trạng thái của Invoice */
export enum EnumInvoiceStatus {
  /** Invoice đang chỉnh sửa */
  Incomplete = -1,
  /** Invoice đã update hoàn tất */
  Created = 1,
  /** Invoice đang được thanh toán ở PayGate */
  Inprogress = 2,
  /** Invoice bị cancel */
  Cancel = -2,
  /** Invoice chưa thanh toán thành công */
  Unpaid = 3,
  /** Invoice đc thanh toán hoàn toàn */
  FullyPaid = 4,
}

export enum EnumInvoiceType {
  /** Trường Amount là bắt buộc */
  AmountOnly = 1,
  /** Mỗi dòng thông tin bắt buộc điền đủ thông tin Item Name/Unit Price/Quantity */
  ItemList = 2,
}

/** Trạng thái giao dịch */
export enum EnumTransactionStatus {
  AwaitTransactionResult = 1,
  Successful = 2,
  Failed = 3,
  Processing = 4,
  Incomplete = 5,
  AwaitMerchantApproval = 6,
  MerchantRejected = 7,
  AwaitOnepayApprovval = 8,
  OnepayRejected = 9,
  MerchantApproved = 10,
  OnepayApproved = 11,
}

/** Loại giao dịch */
export enum EnumTransactionType {
  /** Cấp phép */
  Authorize = 1,
  /** Quyết toán */
  Capture = 2,
  /** Thanh toán */
  Purchase = 3,
  /** Hoàn tiền  */
  Refund = 4,
  /** Hoàn trả quyết toán */
  RefundCapture = 5,
  /** Hoàn trả khiếu nại */
  RefundDispute = 6,
  /** Hủy cấp phép */
  VoidAuthorize = 7,
  /** Hủy quyết toán */
  VoidCapture = 8,
  /** Hủy thanh toán */
  VoidPurchase = 9,
  /** Hủy hoàn trả */
  VoidRefund = 10,
  /** Hủy hoàn trả quyết toán */
  VoidRefundCapture = 11,
  RequestRefund = 12,
	PayLater = 13,
	Void = 14,
}

/** Loại giao dịch */
export enum EnumOrderType {
  MyLink = 1,
  PaymentLink = 2,
  Invoice = 3,
}

/** Cấu hình ẩn/hiện, bắt buộc nhập các thông tin KH cần nhập liệu: Name,Phone,Email.Address */
export enum EnumInfoFieldOptions {
  EnableName = 1,
  RequireName = 2,
  EnableEmail = 4,
  RequireEmail = 8,
  EnablePhone = 16,
  RequirePhone = 32,
  EnableAddress = 64,
  EnableNotes = 128,
  RequireNotes = 256,
}

/** Các loại Generate OrderRef/RouterUri/Code/... */
export enum EnumGenerateType {
  MyLinkOrder = 1,
  PaymentOrder = 2,
  PaymentUri = 3,
  InvoiceOrder = 4,
  InvoiceUri = 5,
}

export enum EnumTimeInterval {
  Daily = 1,
  Weekly = 2,
  Monthly = 3,
}

/** Các điều kiện gửi email thông báo */
export enum EnumEmailNotifyType {
  /** a successful trans occurs */
  SuccessfulTrans = 1,
  /** Invoice gets 3 consecutive failed attemps */
  InvoiceFail3Consecutive = 2,
  /** OnePay approves/reject a refund request */
  OpAprRejRefundRequest = 4,
  /** OnePay approves/reject merchant profile changes */
  OpAprRejProfileChanged = 8,
}

export enum EnumExportType {
  CSV = 1,
  // Excel = 2,
  // PDF = 3,
  // Word = 4,
}

export const DEFAULT_VALUE_NUMBER_PAYMENT_LINK = 0;
export const CURERENCY_USD = "USD";

export enum EnumSearchTransactionType {
  Authorize = 1,
  Capture = 2,
  Purchase = 3,
  Refund = 4,
  RefundCapture = 5,
  RefundDispute = 6,
  VoidAuthorize = 7,
  VoidCapture = 8,
  VoidPurchase = 9,
  VoidRefund = 10,
  VoidRefundCapture = 11,
  RequestRefund = 12,
  PayLater = 13,
}

export enum EnumSearchRefundType {
  Refund = 4,
  RefundCapture = 5,
  RefundDispute = 6,
  VoidRefund = 10,
  VoidRefundCapture = 11,
  RequestRefund = 12
}

export enum EnumTransactionPaymentMethod {
  InternationalCard = "INTERNATIONAL_CARD",
  DomesticCard = "DOMESTIC_CARD",
  MobileApp = "APP",
  DigitalWallet = "DIGITAL_WALLET", // cái này chưa có định nghĩa chuẩn từ OP
  BuyNowPayLater = "BNPL",
  Installment = "INSTALLMENT",
  VietQR = "VIETQR",
}

export enum EnumPaymentLinkType {
  MultiUse = 1,
  OneTime
}

/**
 * Loại bản ghi Permission của user
 */
export enum EnumUserPermissionType {
  /**
   * UserPermission: Permission của user được lấy từ bảng UserPermission
   */
  UserPermission = 1,
  /**
   * RolePermission: Permission của user được lấy từ bảng UserRole → RolePermission
   */
  RolePermission = 2,
}

export enum EnumTransManagermentPaymentMethod {
  InternationalCard = "INTERNATIONAL_CARD",
  DomesticCard = "DOMESTIC_CARD",
  App = "APP",
  ShopeePay = "SHOPEEPAY",
  UnionPay = "UNIONPAY",
  SmartPay = "SMARTPAY",
  BNPL = "BNPL",
  Installment = "INSTALLMENT",
  VietQR = "VIETQR"
}
