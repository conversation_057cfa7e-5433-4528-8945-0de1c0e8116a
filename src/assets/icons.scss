@import "./scss/variables";

.pi {
    display: flex;

    &::before {
        content: '';
        width: 20px;
        height: 20px;
        color: inherit;
        background-color: currentColor;
    }
}

.pi-search::before {
    mask: url('./icons/search.svg') no-repeat 50% 50%;
    mask-size: cover;
}

.pi-reload::before {
    mask: url('./icons/reload.svg') no-repeat 50% 50%;
    mask-size: cover;
}

.pi-edit::before {
    mask: url('./icons/edit.svg') no-repeat 50% 50%;
    mask-size: cover;
}

.pi-apps::before {
    mask: url('./icons/gridDot.svg') no-repeat 50% 50%;
    mask-size: cover;
}

.pi-download::before {
    mask: url('./icons/download.svg') no-repeat 50% 50%;
    mask-size: cover;
}

.pi-download-qrcode::before {
    mask: url('./icons/download-qrcode.svg') no-repeat 50% 50%;
    mask-size: cover;
}