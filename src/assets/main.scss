// @use "./scss/_boostrap_custom.scss" as boostrap;
// @use "./scss/_prime_custom.scss" as prime;
// @import "./scss/_primeboostrap_custom.scss";
// @forward "./scss/boostrap_custom";
@import "./scss/variables";
@import "./fonts/inter/inter.css";
@import "./icons.scss";

:root {
  color: var(--text-body, #404040);
  /* Inter/B3/12_Regular */
  font-family: "Inter", sans-serif;
  font-feature-settings: normal;
  --font-family: "Inter", sans-serif;
  --font-feature-settings: normal;
  font-size: 16px; // $font-size-base
  font-style: normal;
  font-weight: normal;
}

html,
body,
:root,
#app,
#app-wrapper {
  height: 100%;
}
// #app, #app-wrapper  {
//   display: flex;
//   flex: 1 1 auto;
// }

//svg > symbol path {
//stroke: inherit; // dùng thì lỗi svg download
//fill: inherit; // dùng thì lỗi loạn lên
//}

/* #region .text-* */

.text-line-break {
  @include allow_line_break();
}

/* #endregion .text-* */

/* #region .text-truncate-lines-* */

.text-truncate-lines,
.text-truncate-lines-1,
.text-truncate-lines-2,
.text-truncate-lines-3,
.text-truncate-lines-4 {
  overflow: hidden;
  text-overflow: ellipsis;
  /*white-space: normal;*/
  white-space: pre;
  display: -webkit-box;
  -webkit-line-clamp: 2; /*default display 2 lines only*/
  -webkit-box-orient: vertical;
  visibility: visible; /*safari bug*/
}

.text-truncate-lines-1 {
  -webkit-line-clamp: 1; /*display 1 lines*/
}

.text-truncate-lines-3 {
  -webkit-line-clamp: 3; /*display 3 lines*/
}

.text-truncate-lines-4 {
  -webkit-line-clamp: 4; /*display 4 lines*/
}

/* #endregion .text-truncate-lines-* */

.page-title {
  color: var(--text-headings, #1c1c1c);
  font-size: 1.5rem;
  font-style: normal;
  font-weight: 600;
  line-height: 2.25rem;
}

.user-selectable,
.user-clickable,
.clickable {
  @include cursor-pointer();
}

.divider {
  display: flex;
  position: relative;

  color: var(--border-tertiary, #e6e6e6);

  // .p-divider-horizontal
  &.divider-horizontal,
  &--horizontal {
    width: 100%;
    align-items: center;
    margin: 1rem 0;
    padding: 0 1rem;

    // .p-divider-horizontal:before
    &:before {
      position: absolute;
      display: block;
      top: 50%;
      left: 0;
      width: 100%;
      content: "";
      border-top: 1px solid currentColor;
    }
  }

  // .p-divider-vertical
  &.divider-vertical,
  &--vertical {
    min-height: 100%;
    justify-content: center;
    margin: 0 1rem;
    padding: 0.5rem 0;

    &:before {
      position: absolute;
      display: block;
      top: 0;
      left: 50%;
      height: 100%;
      content: "";
      border-left: 1px solid currentColor;
    }
  }
}

.flex--center-middle {
  display: flex;
  justify-content: center;
  align-items: center;
}

.text-error,
[text-red] {
  font-weight: 400;
  line-height: 16px;
  color: #d82039;
  margin-top: 4px;
}

.control-error {
  border: 1px solid;
  border-color: #d82039;
}

.no-permission {
  pointer-events: auto; // auto thay vì none là để cho phép hover vào hiện được tooltip
  color: var(--text-disabled2, #bababa);
}
div.no-permission {
  cursor: default !important; //!important để chặn thay đổi ở các thẻ div li
}

.panel-ErrorComponent {
  max-height: 75rem;
  background: var(--surface-ghost, #fff);
  border-radius: var(--layout-spacing-spacing-xs, 0.5rem);

  padding: 3rem 16.1875rem;
  // @include media-breakpoint-up(sm) {
  //   padding: 3rem 3rem;
  // }
  
  // > Large devices (desktops, 992px and up)
  @media (max-width: 1199px) { 
    padding: 3rem 9rem;
  }

  // > Small devices (landscape phones, 992px and up)
  @media (max-width: 991px) { 
    padding: 3rem 3rem;
  }
  

  display: flex;
  flex-direction: column;
  gap: 1rem;
  justify-content: center;
  align-items: center;

  text-align: center;

  .panel-ErrorComponent--title {
    color: var(--text-headings, #1c1c1c);
    /* Inter/H1/24_Semibold */
    font-size: var(--Typeface-size-2xl, 1.5rem);
    font-style: normal;
    font-weight: 600;
    line-height: 2.25rem; /* 150% */

    margin-bottom: 0.25rem;
  }
  .panel-ErrorComponent--subtitle {
    color: var(--text-tertiary, #6c6c6c);
    /* Inter/B2/14_Regular */
    font-size: var(--Typeface-size-md, 0.875rem);
    font-style: normal;
    font-weight: 400;
    line-height: 1.25rem; /* 142.857% */
  }

  &.panel-ErrorComponent-horizontal {
    flex-direction: row-reverse;
    gap: 3rem;

    text-align: left;

    @media (max-width: 768px) { 
      flex-direction: column;
      text-align: center;
    }

    @media (max-width: 576px) { 
      margin: 0 -12px;
      height: 100%;
      border-radius: 0;
    } 
  }
}