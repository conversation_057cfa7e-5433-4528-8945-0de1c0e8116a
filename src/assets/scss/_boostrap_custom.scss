@import "./variables";

.dropdown-menu-right {
  @extend .dropdown-menu-end;
}

.form-control {
  @include border_inside()
}

.card {
  border: none;
  .card-header {
    border-bottom: 0;
  }
  .card-body {
    padding: 1rem 1.5rem;
  }
  &.card-sm .card-body {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
  &.card-lg .card-body {
    padding: 1.25rem 1.5rem;
  }
  .card-title {
    margin-bottom: 1rem;
  }
}

.btn-close {
  background: var(--surface-ghost-hover, #f5f5f5);
  width: inherit;
  height: inherit;
  padding: 0.75rem;
}

hr {
  border-color: var(--border-tertiary, #E6E6E6);
  opacity: 1;
}


h6, .h6, h5, .h5, h4, .h4, h3, .h3, h2, .h2, h1, .h1 {
  line-height: 1.4;
}

h2, .h2 {
  font-size: var(--Typeface-size-xl, 1.25rem);
  font-weight: 600;
  line-height: 1.75rem; /* 140% */
}

h3, .h3 {
  font-size: var(--Typeface-size-lg, 1);
}


.mb-4px {
  margin-bottom: var(--layout-spacing-spacing-md, .25rem) !important;
}
.mb-12px {
  margin-bottom: var(--layout-spacing-spacing-md, .75rem) !important;
}
.mb-8px {
  margin-bottom: var(--layout-spacing-spacing-xs, .5rem) !important;
}

.mt-16px {
  margin-top: var(--layout-spacing-spacing-xs, 1rem) !important;
}

.mt-8px {
  margin-top: var(--layout-spacing-spacing-xs, .5rem) !important;
}
