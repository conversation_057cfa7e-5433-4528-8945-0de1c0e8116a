.p-inputtext.p-inputtext-tms {
  width: 100%;
  color: var(--bs-body-color);
  border-radius: $input-border-radius;
  font-size: $font-size-base;
  padding-top: $input-padding-y;
  padding-bottom: $input-padding-y;
  padding-left: $input-padding-x;
  padding-right: $input-padding-x;
  //line-height: $input-line-height; // 1.4285714286 = 20px
  line-height: 1.25rem; // 1.25rem = 20px

  &::placeholder {
    color: #bababa;
  }
  
  //&:focus {
  //  border-color: var(--p-focus-ring-color);
  //}

  //@include border_inside(var(--border-primary, #dcdcdc));
  border-color: #dcdcdc;
  &.p-invalid {
    border-color: #f87171;
  }

  &.p-inputtext-sm {
    font-size: var(--Typeface-size-md, 0.875rem);
    font-style: normal;
    font-weight: 400;
    line-height: 1.25rem; /* 142.857% */

    padding: 0.4375rem 1rem; // 8px nhưng bớt 1 để làm border outline
  }

  &.p-inputtext-lg {
    font-size: 1.25rem;
    font-weight: 400;
    line-height: 1.75rem; /* 140% */

    padding: 0.6875rem 1rem;
  }
}

//#region inputgroup

.p-inputgroup .p-inputtext,
.p-fluid .p-inputgroup .p-inputtext,
.p-inputgroup .p-inputwrapper,
.p-fluid .p-inputgroup .p-input {
  flex: 1 1 auto;
  width: 1%;
}

.p-inputgroup > .p-component,
.p-inputgroup > .p-inputwrapper > .p-inputtext,
.p-inputgroup > .p-float-label > .p-component {
  border-radius: 0;
  margin: 0;
}

.p-inputgroup > .p-component:not(:first-child),
.p-inputgroup > .p-inputwrapper > .p-inputtext:not(:first-child),
.p-inputgroup > .p-float-label > .p-component :not(:first-child) {
  border-left: none;
}

.p-inputgroup-addon:first-child,
.p-inputgroup button:first-child,
.p-inputgroup input:first-child,
.p-inputgroup > .p-inputwrapper:first-child,
.p-inputgroup > .p-inputwrapper:first-child > .p-inputtext {
  border-top-left-radius: $input-border-radius;
  border-bottom-left-radius: $input-border-radius;
}

.p-inputgroup-addon:last-child,
.p-inputgroup button:last-child,
.p-inputgroup input:last-child,
.p-inputgroup > .p-inputwrapper:last-child,
.p-inputgroup > .p-inputwrapper:last-child > .p-inputtext {
  border-top-right-radius: $input-border-radius;
  border-bottom-right-radius: $input-border-radius;
}

.p-inputgroup-addon {
  border-right: 1px solid #cbd5e1; // #cbd5e1 là mặc định của primevue
  min-width: unset;
  padding-top: $input-padding-y;
  padding-bottom: $input-padding-y;
  padding-left: $input-padding-x;
  padding-right: $input-padding-x;
  line-height: $input-line-height;
}

//#endregion inputgroup


.p-icon-field.p-icon-field-tms {
  > .p-input-icon {
    margin-top: -0.75rem; // Is 1/2 of 24px of icon height
  }

  &.p-icon-field-left > .p-inputtext {
    padding-left: 2.5rem;
  }

  &.p-icon-field-right > .p-inputtext {
    padding-right: 2.5rem;
  }

  // &:has(.p-inputtext) {
  //   padding-left: 1px; // Do ko border outline nên đẻ đản bảo border ko bị cắt cụt thì cần lùi lại chút
  //   padding-right: 1px;
  // }

  // &.p-icon-field-search {
  //   .p-inputtext {
  //     padding-top: 0.5rem; // 0.25rem*16=4px
  //     padding-bottom: 0.5rem; // 0.25rem*16=4px
  //   }
  // }
}
