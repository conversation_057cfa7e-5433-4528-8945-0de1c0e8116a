@import "./variables";

.p-component {
  font-size: $font-size-base;
}

.p-link {
  font-size: $font-size-base;
}

.p-dropdown.p-dropdown-tms {
  .p-dropdown-label {
    font-size: $font-size-base;
    color: var(--bs-body-color);
    padding: 2px 0 2px 8px;
    align-self: center;
    padding: 0.5rem 0.75rem;
  }
  .p-dropdown-trigger {
    color: var(--bs-body-color);
    width: auto;
    margin-right: 8px;
    margin-left: 4px;
  }
}

.p-datatable.p-datatable-tms {
  line-height: 1rem;
  font-size: 0.75rem;

  .p-datatable-thead > tr > th {
    padding: 0.375rem 1rem;
    background-color: #f5f5f5;
    font-weight: 500;
    // text-transform: capitalize;
  }

  .p-datatable-wrapper {
    border: 1px solid #e6e6e6;
    border-width: 1px 1px 0 1px;
  }

  &.p-datatable-gridlines {
    .p-datatable-wrapper {
      border-width: 0 0 0 0;
    }
  }

  .p-datatable-table {
    //border: 1px solid #e6e6e6;
    border-collapse: separate; // fix lại lỗi của boostrap chuyển thành collapse

    .p-datatable-tbody > tr:not(.p-highlight):hover {
      background-color: #f5f5f5;
    }
  }

  .p-paginator {
    padding-left: 0;
    padding-right: 0;

    &,
    .p-dropdown .p-inputtext,
    .p-paginator-page {
      color: var(--color-neutral-700_68, #404040);
      /* Inter/B3/12_Regular */
      font-size: var(--Typeface-size-sm, 0.75rem);
      font-style: normal;
      font-weight: 400;
      line-height: 1rem; /* 133.333% */
    }

    > :first-child {
      padding-left: 0;
    }

    .p-dropdown {
      margin-right: auto;
    }
  }

  .p-datatable-tbody > tr {
    .p-col-hide-items > *,
    .p-col-actions > * {
      visibility: hidden;
    }

    &:hover {
      .p-col-hide-items > *,
      .p-col-actions > * {
        visibility: visible;
      }
    }
  }

  .p-col-has-icons {
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .p-col-actions {
    text-align: right;
    padding-top: 0;
    padding-bottom: 0;

    .p-button-icon-only {
      padding: 0;
      width: 2.25rem; // 2.25rem*16=36px
      height: 2.25rem; // 2.25rem*16=36px
      margin-left: 0.75rem; // 0.75rem*16=12px
      //display: inline-block;
    }

    .p-button-icon-only:first-child {
      margin-left: 0;
    }
  }

  .p-datatable-tbody > tr.p-highlight {
    background: var(--surface-info, #EDF3FF);
    color: #334155; // color of default primevue datatable
  }
  .p-datatable-tbody > tr:has(+ .p-highlight) > td,
  .p-datatable-tbody > tr.p-highlight > td {
    border-bottom-color: #e2e8f0; // color of default primevue datatable
  }

  &.table-nowrap tbody > tr > td {
    // Sử dụng .table-nowrap cho phép tất cả td bên trong chỉ hiển thị content trên 1 dòng. tránh việc cứ mỗi td lại thêm nowrap
    // Nếu ko muốn dùng thì phải bổ sung thêm text-nowrap cho từng td
    white-space: nowrap;
  }
}

.p-paginator.p-paginator-tms {
  .p-paginator-pages {
    .p-paginator-page {
      border-radius: 0.25rem; // 0.25rem*16=4px
      color: var(--color-neutral-700_68, #404040);
      min-width: 1.5rem; // 1.5rem*16=24px
      height: 1.5rem;

      &.p-highlight {
        background: var(--theme-info, #2e6be5);
        color: var(--color-neutral-50_255, #fff);
      }
      &:not(.p-highlight):hover {
        background: var(--color-neutral-100_245, #f5f5f5);
        color: var(--color-neutral-700_68, #404040);
      }
    }
  }

  .p-paginator-first,
  .p-paginator-prev,
  .p-paginator-next,
  .p-paginator-last {
    border-radius: 0.25rem; // 0.25rem*16=4px
    border: 1px solid var(--color-neutral-300_230, #e6e6e6);
    color: var(--color-neutral-700_68, #404040);
    min-width: 1.5rem; // 1.5rem*16=24px
    height: 1.5rem;
  }

  .p-paginator-current {
    color: var(--color-neutral-700_68, #404040);
  }

  .p-dropdown {
    height: 24px;
    @include border_inside();
    .p-dropdown-label {
      padding: 2px 0 2px 8px;
    }
  }
}

.p-datatable th.text-right {
  .p-column-header-content {
    justify-content: end;
  }
}

@import "./input/input";

// .p-dropdown .p-dropdown-label.p-inputtext  {
//   @include no-border_inside();
// }

.p-button.p-button-tms {
  border-radius: var(--layout-radius-radius-xs, 0.25rem); // 0.25rem*16=4px
  background: var(--surface-action, #2e6be5); // Defautl background
  color: var(--text-on-action, #fff);

  //@include border_inside();
  border: none;

  font-size: $font-size-base;
  line-height: 1.25rem; // 1.25rem*16=20px
  padding: var(--layout-spacing-spacing-sm, 0.625rem) var(--layout-spacing-spacing-xl, 1.25rem); // 0.625rem*16=10px  & 1.25rem*16=20px

  &.p-button-lg {
  }

  &.p-button-sm {
    font-size: var(--Typeface-size-sm, 0.75rem);
    line-height: 1rem; /* 133.333% */
    padding: var(--layout-spacing-spacing-sm, 0.625rem) var(--layout-spacing-spacing-lg, 1rem);
  }

  &.p-button-icon-only.p-button-rounded {
    border-radius: 50%;
  }

  &.p-button-text {
    background: transparent;
    color: #404040;

    &:not(:disabled):hover {
      background: var(--theme-neutral-lighter, #e6e6e6);
      color: inherit;
    }
    &:not(:disabled):active {
      background: var(--theme-neutral-lighter, #e6e6e6);
      color: inherit;
    }
    &:disabled {
      background: var(--surface-action-disabled2, #e6e6e6);
      color: inherit;
    }
  }

  &.p-button-link {
    background: transparent;
    border: transparent;
    color: #2e6be5;
  }

  &:not(:disabled):hover {
    background: var(--surface-action-hover, #5889ea);
    color: #fff;
  }
  &:not(:disabled):active {
    background: var(--surface-action-focused, #214ca3);
    color: #fff;
  }
  &:disabled {
    background: var(--surface-action-disabled2, #e6e6e6);
    color: var(--text-disabled2, #bababa);
  }

  &.p-button-primary,
  &.p-button-create,
  &.p-button-save,
  &.p-button-copy-link {
    background: var(--surface-action, #2e6be5);
    color: var(--text-on-action, #fff);

    &:not(:disabled):hover {
      background: var(--surface-action-hover, #5889ea);
      color: #fff;
    }
    &:not(:disabled):active {
      background: var(--surface-action-focused, #214ca3);
      color: #fff;
    }
    &:disabled {
      background: var(--surface-action-disabled2, #e6e6e6);
      color: var(--text-disabled2, #bababa);
    }
  }

  &.p-button-secondary,
  &.p-button-download,
  &.p-button-apps,
  &.p-button-clear,
  &.p-button-close,
  &.p-button-cancel {
    background: var(--color-neutral-700_68, #f5f5f5);
    color: var(--color-neutral-700_68, #404040);

    &:not(:disabled):hover {
      background: var(--theme-neutral-lighter, #e6e6e6);
      color: #404040;
    }
    &:not(:disabled):active {
      background: var(--theme-neutral-light, #dcdcdc);
      color: #404040;
    }
    &:disabled {
      background: var(--surface-action-disabled2, #e6e6e6);
      color: var(--text-disabled2, #bababa);
    }
  }

  &.p-button-danger {
    background: var(--color-neutral-700_68, #d82039);
    color: var(--text-on-action, #fff);

    &:not(:disabled):hover {
      background: var(--theme-neutral-lighter, #e6e6e6);
      color: #fff;
    }
    &:not(:disabled):active {
      background: var(--theme-neutral-light, #dcdcdc);
      color: #fff;
    }
    &:disabled {
      background: var(--surface-action-disabled2, #e6e6e6);
      color: var(--text-disabled2, #bababa);
    }
  }
}

//#region p-form-check*

.p-form-check-group {
  display: flex;
  gap: 1rem;
  padding: 0.75rem;

  &.p-form-check-group-column {
    flex-direction: column;
  }
}

.p-form-check {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

//#endregion p-form-check*

.p-toast.p-toast-tms {
  width: auto;
  &.p-toast-group-quickAlert {
    width: auto;
  }

  .p-toast-message {
    border-radius: 0.5rem; // 0.5rem*16=8px
    line-height: $input-line-height;
    backdrop-filter: none; // cái này làm mờ chữ trên message

    &.p-toast-message-success {
      background: var(--theme-success, #00bf5f);
      border-color: var(--theme-success, #00bf5f);
      color: var(--text-on-action, #fff);
    }

    &.p-toast-message-warn {
      background: var(--icon-warning, #F38713);
      border-color: var(--icon-warning, #F38713);
      color: var(--text-on-action, #fff);
    }

    &.p-toast-message-error {
      background: var(--theme-danger-light, #e04d61);
      border-color: var(--theme-danger-light, #e04d61);
      color: var(--text-on-action, #fff);
    }

    .p-toast-message-content {
      padding: var(--layout-spacing-spacing-xs, 8px) var(--layout-spacing-spacing-xxl, 24px);

      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;

      .p-toast-detail:is(:empty) {
        margin: 0;
      }
      .p-toast-summary {
        font-weight: 400;
      }
    }
  }

  .p-toast-message.p-toast-message-copytoclipboard {
    background: var(--theme-black, #000);
    border-color: var(--theme-black, #000);
    color: var(--text-on-action, #fff);

    //&.p-toast-message-leave-active {
    //  transition-delay: 50s!important; // test delay animation
    //}
    &:not(.p-toast-message-leave-to) {
      opacity: 0.85;
    }

    .p-toast-message-content {
      padding: var(--layout-spacing-spacing-lg, 16px) var(--layout-spacing-spacing-lg, 16px)
        var(--layout-spacing-spacing-lg, 16px) var(--layout-spacing-spacing-xl, 20px);

      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .p-toast-message-icon {
        color: inherit;
        margin-bottom: 8px;
      }

      .p-toast-message-text {
        margin-left: 0;
      }

      .p-toast-icon-close {
        display: none;
      }
    }
  }
}

.p-dialog {
  .p-dialog-header {
    padding: 1.25rem 1.5rem; // 1.25rem*16=20px - 1.5rem*16=24px

    .p-dialog-title {
      color: var(--text-headings, #1c1c1c);
      font-weight: 600;
      font-size: 1.5rem; // 1.5rem*16=24px
      line-height: 2.25rem; /* 150% */ // 2.25rem*16=36px
    }

    .p-dialog-header-icon.p-dialog-header-close {
      width: 40px;
      height: 40px;
      border-radius: var(--layout-radius-radius-sm, 8px);
      background: var(--surface-ghost-hover, #f5f5f5);
    }
  }

  .p-dialog-content {
    padding: 0 1.5rem 1.25rem 1.5rem; // 1.25rem*16=20px - 1.5rem*16=24px
  }
}

.p-overlaypanel {
  &::before {
    content: none;
  }

  &::after {
    content: none;
  }

  .p-overlaypanel-content {
    padding: 1.5rem;
  }
}

.p-tooltip {
  //min-width: 10rem; // dùng nên v-tooltip:"{ class: 'classname' }" để set width

  .p-tooltip-text {
    border-radius: 8px;
    padding: 12px 20px;
  }

  &.p-tooltip-top {
    .p-tooltip-arrow {
      border-top-color: #fff;
    }
    .p-tooltip-text {
      background-color: #fff;
      color: #404040;
    }
  }

  &.p-tooltip-right {
    .p-tooltip-arrow {
      border-right-color: #fff;
    }
    .p-tooltip-text {
      background-color: #fff;
      color: #404040;
    }
  }

  &.p-tooltip-bottom {
    .p-tooltip-arrow {
      border-bottom-color: #fff;
    }
    .p-tooltip-text {
      background-color: #fff;
      color: #404040;
    }
  }

  &.p-tooltip-left {
    .p-tooltip-arrow {
      border-left-color: #fff;
    }
    .p-tooltip-text {
      background-color: #fff;
      color: #404040;
    }
  }
}

.p-panel {
  .p-panel-header {
    padding: 1rem 1.5rem;

    .p-panel-header-icon:enabled:hover {
      background: transparent;
    }
  }
  .p-panel-content {
    padding: 0 1.5rem 1rem 1.5rem;
  }
}

.p-sidebar {
  .p-sidebar-header {
    position: relative;
    padding: 1rem 1.5rem;

    .p-sidebar-close,
    .p-sidebar-icon {
      width: 3rem;
      height: 3rem;
      padding: 0.75rem;
    }
  }
}
