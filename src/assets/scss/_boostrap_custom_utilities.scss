@import "/node_modules/bootstrap/scss/functions";
@import "/node_modules/bootstrap/scss/variables";
@import "/node_modules/bootstrap/scss/maps";
@import "/node_modules/bootstrap/scss/mixins";
@import "/node_modules/bootstrap/scss/utilities";

// Sử dụng Bootstrap Utilities Api để bổ sung lại các "Missing v4 utilities"

$utilities: ()!default;
$utilities: map-merge(
  $utilities,
  (
    "left": (
      class: left,
      property: left,
      values: $position-values
    ),
    "right": (
      class: right,
      property: right,
      values: $position-values
    ),
    "border-left": (
      property: border-left,
      class: border-left,
      values: (
        null: var(--#{$prefix}border-width) var(--#{$prefix}border-style) var(--#{$prefix}border-color),
        0: 0,
      )
    ),
    "border-right": (
      property: border-right,
      class: border-right,
      values: (
        null: var(--#{$prefix}border-width) var(--#{$prefix}border-style) var(--#{$prefix}border-color),
        0: 0,
      )
    ),
    "float": (
      responsive: true,
      property: float,
      values: (
        start: left,
        end: right,
        none: none,
        left: left,
        right: right,
      ),
    ),
    "margin-left": (
      class: ml,
      responsive: true,
      property: margin-left,
      values: map-merge($spacers, (auto: auto))
    ),
    "margin-right": (
      class: mr,
      responsive: true,
      property: margin-right,
      values: map-merge($spacers, (auto: auto))
    ),
    "negative-margin-left": (
      class: ml,
      responsive: true,
      property: margin-left,
      values: $negative-spacers
    ),
    "negative-margin-right": (
      class: mr,
      responsive: true,
      property: margin-right,
      values: $negative-spacers
    ),
    "padding-left": (
      class: pl,
      responsive: true,
      property: padding-left,
      values: $spacers
    ),
    "padding-right": (
      class: pr,
      responsive: true,
      property: padding-right,
      values: $spacers
    ),
    "rounded-left": (
      class: rounded-left,
      property: border-bottom-left-radius border-top-left-radius,
      values: (
        null: var(--#{$prefix}border-radius),
        0: 0,
        1: var(--#{$prefix}border-radius-sm),
        2: var(--#{$prefix}border-radius),
        3: var(--#{$prefix}border-radius-lg),
        4: var(--#{$prefix}border-radius-xl),
        5: var(--#{$prefix}border-radius-xxl),
        circle: 50%,
        pill: var(--#{$prefix}border-radius-pill)
      )
    ),
    "rounded-right": (
      class: rounded-right,
      property: border-top-right-radius border-bottom-right-radius,
      values: (
        null: var(--#{$prefix}border-radius),
        0: 0,
        1: var(--#{$prefix}border-radius-sm),
        2: var(--#{$prefix}border-radius),
        3: var(--#{$prefix}border-radius-lg),
        4: var(--#{$prefix}border-radius-xl),
        5: var(--#{$prefix}border-radius-xxl),
        circle: 50%,
        pill: var(--#{$prefix}border-radius-pill)
      )
    ),
    "justify-content": (
      responsive: true,
      property: justify-content,
      values: (
        start: flex-start,
        end: flex-end,
        center: center,
        between: space-between,
        around: space-around,
        evenly: space-evenly,
        left: flex-start,
        right: flex-end,
      )
    ),
    "align-items": (
      responsive: true,
      property: align-items,
      values: (
        start: flex-start,
        end: flex-end,
        center: center,
        baseline: baseline,
        stretch: stretch,
        left: flex-start,
        right: flex-end,
      )
    ),
    "align-content": (
      responsive: true,
      property: align-content,
      values: (
        start: flex-start,
        end: flex-end,
        center: center,
        between: space-between,
        around: space-around,
        stretch: stretch,
        left: flex-start,
        right: flex-end,
      )
    ),
    "align-self": (
      responsive: true,
      property: align-self,
      values: (
        auto: auto,
        start: flex-start,
        end: flex-end,
        center: center,
        baseline: baseline,
        stretch: stretch,
        left: flex-start,
        right: flex-end,
      )
    ),
    "text-align": (
      responsive: true,
      property: text-align,
      class: text,
      values: (
        start: left,
        end: right,
        center: center,
        left: left,
        right: right,
      )
    ),
  )
);