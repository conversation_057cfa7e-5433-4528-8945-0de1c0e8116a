$font-family: "Inter", sans-serif;
$font-family-base: "Inter", sans-serif;

$font-size-root: 1rem;
$font-size-base: 0.875rem; // 16*0.875 = 14px
$line-height-base: 1.5; // 1.5*16=24px

$color-text-headings: #1C1C1C;
$color-text-body: #404040;
$color-text-success: #00BF5F;
$color-text-danger: #D82039;

$color-bg-success: #8AE2B5;


$color-primary: #2E6BE5;
$color-secondary: #7fdbff;
$color-tertiary: #39cccc;

// Override Variables

$border-radius: 0.5rem;// đang áp cụng cho card
$border-color: var(--border-primary, #DCDCDC);

// #region Card (https://getbootstrap.com/docs/5.0/components/card/#variables)

//$card-spacer-y: $spacer;
$card-spacer-y: 1.5rem;
//$card-spacer-x: $spacer;
$card-spacer-x: 1.5rem;
$card-cap-padding-y: 1rem;
$card-title-spacer-y: .75rem;
$card-cap-bg: inherit;
// #endregion

// #region Button (https://getbootstrap.com/docs/5.0/components/buttons/#variables)

$btn-border-radius: 0.25rem; // button và input thì chỉ 16*0.25=4px

// #endregion

// #region Form-control (https://getbootstrap.com/docs/5.0/forms/form-control/#variables)

$input-border-radius: 0.25rem; // button và input thì chỉ 16*0.25=4px
$input-line-height: 1.42857142857; // 14*1.42857142857=20px (bị áp dụng font-size của input)
//$input-line-height: 1.25rem; // 14*1.42857142857=20px (bị áp dụng font-size của input)
//$input-padding-y: 0.625rem; // 16*0.625=10px (nếu dùng outline để có border inside)
//$input-padding-y: calc(0.625rem - 1px); // 16*0.625=10px - 1px = 9px
$input-padding-y: 0.5625rem; // 0.5625rem*16=9px
$input-padding-x: 1rem; // 16*1=16px

// #endregion

$offcanvas-padding-y: 1rem;
$offcanvas-padding-x: 1.5rem;
$nav-link-font-size: 0.875rem;

@mixin border_inside($color: var(--border-primary, #dcdcdc)) {
  border: 1px solid $color; // Hủy border để sử dụng outline
  //outline: 1px solid $color; // Outline ko có radius -> ko thể dùng nếu ko có border
}
@mixin no-border_inside() {
  border: none;
  outline: none;
}

@mixin allow_line_break() {
  white-space: pre;
  white-space: pre-wrap;

  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
}
@mixin text_truncate($line: 1) {
  @if $line == 1 {
    /* bootstrap css */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    overflow: hidden;
    text-overflow: ellipsis;
    /*white-space: normal;*/
    white-space: pre;
    display: -webkit-box;
    -webkit-line-clamp: $line; /*default display 2 lines only*/
    -webkit-box-orient: vertical;
    visibility: visible; /*safari bug*/
  }
}

@mixin cursor-pointer() {
  // Chuyển cursor thành pointer (clickable) để dành cho icon có thể bấm vào
  cursor: pointer;
}

