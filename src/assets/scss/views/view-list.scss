/**
  * .view-list
  * ├── .view-list--header
  * │   ├── .view-list--header-title
  * │   └── .view-list--header-actions
  * │
  * ├── .view-list--filter-group
  * │   ├── .view-list--filter-group-item
  * │   └── .view-list--filter-group-item
  * │
  * ├── .view-list--divider
  * │   
  * ├── .view-list--body
  * │   ├── .view-list--body-summary
  * │   └── .view-list--body-datatable
  * │
  * └── .view-list--footer
  */  

/**
  * .view-list.with-tabview
  * ├── .view-list--header.pb-0
  * │   └── .view-list--title
  * │
  * ├── .view-list--tabview(p-0)
  * │   ├── .view-list--tabview-nav
  * │   │
  * │   ├── .view-list--divider
  * │   │
  * │   └── .view-list--tabview-panels
  * │       ├── .view-list--tabview-panel
  * │       │   ├── ...
  * │       │   ├── ...
  * │       │   └── ...
  * │       │
  * │       └── .view-list--tabview-panel
  * │           ├── .view-list--header
  * │           │   │
  * │           │   └── .view-list--filter-group
  * │           │       ├── .view-list--filter-item
  * │           │       └── .view-list--filter-item
  * │           │
  * │           │
  * │           └── .view-list--body
  * │               ├── .view-list--body-summary
  * │               └── .view-list--body-datatable
  * │   
  * │
  * └── .view-list--footer
  */  
.view-list {

  %divider-border {
    border-bottom: 1px solid #e6e6e6;
  }

  display: flex;
  flex-direction: column;
  height: 100%;

  .view-list--divider {
    margin: 1rem 0;
    padding: 0 1rem;

    display: flex;
    width: 100%;
    position: relative;
    align-items: center;

    &::before {
      position: absolute;
      display: block;
      top: 50%;
      left: 0;
      width: 100%;
      content: "";

      @extend %divider-border;
    }
  }

  .view-list--header {
    padding: 20px 24px 12px;

    @extend %divider-border;
  }

  .view-list--group-filters {
    padding: 0 24px;
    margin-bottom: 0.75rem;
  }

  .panel-header {
    padding: 20px 24px 12px;
    border-bottom: 1px solid #e6e6e6;
  }

  .view-list--body {
    padding: 1rem 1.5rem;

    flex: 1 1 auto;
    overflow: auto;
  }
}