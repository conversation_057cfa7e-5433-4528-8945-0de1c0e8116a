/** generated by vite-plugin-runtime */
interface ImportMetaEnv {
  readonly APP_KEY: string;
  readonly APP_HOST_URI: string;
  readonly APP_PL_HOST_URI: string;
  readonly APP_ML_HOST_URI: string;
  readonly APP_LOGIN_URL: string;
  readonly APP_LOGOUT_URL: string;
  readonly APP_GET_INSTALLMENT_OF_AMOUNT: number;
  readonly APP_API_ENDPOINT: string;
  readonly APP_MAX_SELECTED_DAYS_IN_DATERANGE: number;
  readonly APP_DEV_FAKE_JWT: string;
}
interface ImportMeta {
  readonly env: ImportMetaEnv;
}
