import { AppLayoutMapper, App<PERSON><PERSON>outN<PERSON><PERSON>, DefaultLayoutName } from "@/enums/layouts"
import { Component } from "vue"
import type { Router } from "vue-router"

async function importLayout(layoutName: string) {
  return AppLayoutMapper[layoutName]()
  // switch (layoutName) {
  //   case AppLayoutNames.Default:
  //     return await import(`@/layouts/default/DefaultLayout.vue`)
  //   case AppLayoutNames.Custom:
  //     return await import(`@/layouts/custom/CustomLayout.vue`)
  //   case AppLayoutNames.Empty:
  //     return await import(`@/layouts/empty/EmptyLayout.vue`)
  //   default:
  //     throw new Error(`The layout ${layoutName} is not supported.`)
  // }
}

/**
 * This middleware is used to dynamically update the Layouts system.
 * As soon as the route changes, it tries to pull the layout that we want to display from the laptop. Then it loads the layout component, and assigns the loaded component to the meta in the layout Component variable. And layoutComponent is used in the basic layout AppLayout.vue, there is a dynamic update of the layout component
 * If the layout we want to display is not found, loads the default layout App Layout Default.vue
 * */
export function useLayoutMiddleware(
  router: Router,
  options?: {
    debug?: boolean
  }
) {
  options = {
    ...options,
  }

  router.beforeEach(async (route) => {
    try {
      options.debug && console.log("Middleware check layout", route.path)

      const layout = route.meta.layout ?? DefaultLayoutName
      const layoutComponent = await importLayout(layout)
      options.debug && console.log(`Mounted layout ${layout}`)
      route.meta.layoutComponent = layoutComponent.default
    } catch (ex) {
      console.error("Error occurred in processing of layoutMiddleware", ex)
      console.log(`Try mounting default layout...`)
      const layoutComponent = await importLayout(DefaultLayoutName)
      console.log(`Mounted default layout ${DefaultLayoutName}`)
      route.meta.layoutComponent = layoutComponent.default
    }
  })
}

declare module "vue-router" {
  interface RouteMeta {
    /**
     * The layout name để sử dụng cho page view.
     *
     * @default 'default'
     * @example
     * ```ts
     * export const routes: [
     * {
     *  name: AppRouteNames.ABOUT,
     *  path: '/',
     *  component: () => import('./views/About.vue'),
     *  meta: {
     *   layout: 'MyLayout'
     *  }
     * },
     * ```
     */
    layout?: AppLayoutNames

    layoutComponent?: Component
  }
}
