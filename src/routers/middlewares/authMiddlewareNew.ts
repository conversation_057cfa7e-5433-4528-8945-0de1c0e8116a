import { AppRouteNames } from "@/enums/routers"
import { useAuthService } from "@/services/authService"
import type { Router } from "vue-router"

export function useAuthMiddleware(
  router: Router,
  options?: {
    debug?: boolean
  }
) {
  options = {
    ...options
  }

  router.beforeEach(async (to, from) => {
    // TODO: Khối xử lý login+authMiddleware+router(LOGIN) đang bị cồng kềnh, cần xử lý lại.
    try {
      // Mặc định yêu cầu phải có authentication.
      if (to?.meta?.allowAnonymous == true) {
        options.debug && console.log("Middleware allows anonymous", to.path)

        return true
      } else {
        options.debug && console.log("Middleware check auth", to.path)

        //const isAuthenticated: boolean = true // TODO: chưa làm
        //const isAuthorized: boolean = true // TODO: chưa làm

        // Get user store
        //const userStore = useUserStore();

        // Fetch user data from the server or local storage
        //await userStore.fetchUser();

        // NOTE: hiện tại đang xử lý verifyLoginDataTask ở route LOGIN.
        // Sau đó sẽ lại đi ngược lại. check xử lý ở đây.
        // Steps Ex: Redirect to HOME -> check auth(in beforeEach) -> (cannot at first time) -> redirect to LOGIN(return=HOME) -> verifyLoginDataTask(in beforeEach) -> (if ok) back to return=HOME
        // -> Do không call check OP auth ở đây, nên:
        // + Không tự check authen/author of user.
        // + Không tự check merchants of user.
        // + Chỉ check nếu user F5 do reload thật lại page.

        //const { verifyLoginDataTask } = useAuthService()
        //const { success, mode, errCode } = await verifyLoginDataTask.perform()

        const { isAuthenticated, isAuthorized, isSysAdmin, isNoMerchant, isNoAppPermission } = checkAuth()

        // You can use try/catch to get an id token and set it to your request header
        // ex: try { ... next() } catch { ... next({ name: 'login'}) }

        if (!isAuthenticated) {
          if (isNoAppPermission) {
            // NOTE: Khi user không được Authen thì tồn tại 1 case đặc biệt là OP trả về 403 isNoAppPermission=true đồng thời ko có user info.
            if (to?.meta?.allowNoAppPermission == true) {
              // Tiếp tục xử lý
              options.debug && console.log("Middleware check 401-403 OK")
              return true
            }

            options.debug && console.warn("Middleware check auth 403")
            // return login page /register?returnUrl=/home
            return {
              name: AppRouteNames.ERROR_403NoAppPermission,
              message: "NotHaveAppPermission",
              query: {
                returnUrl: to.fullPath,
              },
            }
          }

          options.debug && console.warn("Middleware check auth 401")
          // return login page /register?returnUrl=/home
          return {
            name: AppRouteNames.LOGIN,
            message: "UnAuthenticated",
            query: {
              returnUrl: to.fullPath,
            },
          }
        }
        if (!isAuthorized) {
          options.debug && console.warn("Middleware check auth 403")
          return {
            name: AppRouteNames.ERROR_403Forbidden,
            message: "UnAuthorized",
            replace: true,
            query: {
              returnUrl: to.fullPath,
            },
          }
        }
        if (!isSysAdmin && to?.meta?.sysAdminOnly == true) {
          options.debug && console.warn("Middleware check auth 403 (admin)")
          return {
            name: AppRouteNames.ERROR_403Forbidden,
            message: "NotSysAdmin",
            replace: true,
            query: {
              returnUrl: to.fullPath,
            },
          }
        }
        if (isNoMerchant && to?.meta?.allowNoMerchant != true) {
          options.debug && console.warn("Middleware check auth 403 (NoMerchant)")
          return {
            name: AppRouteNames.ERROR_403NoMerchant,
            message: "NotHaveAnyMerchant",
            replace: true,
            query: {
              returnUrl: to.fullPath,
            },
          }
        }

        // Tiếp tục xử lý
        options.debug && console.log("Middleware check OK")
        return true
      }
    } catch (ex) {
      console.error("Error occurred in processing of authMiddleware", ex)

      // Nếu không phải là development mode, chuyển hướng về trang chủ hoặc trang khác
      return new Error("Error occurred in processing of authMiddleware")
    }
  })
}

function checkAuth() {
  let isAuthenticated: boolean = false
  let isAuthorized: boolean = false
  let isSysAdmin: boolean = false
  let isNoMerchant: boolean = false
  let isNoAppPermission: boolean = false

  const authService = useAuthService()

  isAuthenticated = authService.isAuth
  isAuthorized = true // TODO: chưa làm
  isSysAdmin = authService.isSysAdmin
  isNoMerchant = authService.isNoMerchant
  isNoAppPermission = authService.isNoAppPermission

  return {
    isAuthenticated,
    isAuthorized,
    isSysAdmin,
    isNoMerchant,
    isNoAppPermission,
  }
}

declare module "vue-router" {
  interface RouteMeta {
    /**
     * Khi set route.meta.allowAnonymous=true thì AuthMiddleware sẽ bỏ qua ko check authentication và authorization.
     *
     * @example
     * ```ts
     * export const routes: [
     * {
     *  name: AppRouteNames.ABOUT,
     *  path: '/',
     *  component: () => import('./views/About.vue'),
     *  meta: {
     *   allowAnonymous: true
     *  }
     * },
     * ```
     */
    allowAnonymous?: boolean

    /**
     * Khi set route.meta.sysAdminOnly=true thì user truy cập page này bắt buộc phải có quyền System-Admin.
     */
    sysAdminOnly?: boolean

    /**
     * Khi set route.meta.allowNoMerchant=true thì user truy cập page này không cần bắt buộc phải có thông tin merchant -> system pages
     */
    allowNoMerchant?: boolean

    /**
     * Khi set route.meta.allowNoAppPermission=true thì user truy cập page này không cần bắt buộc phải có thông tin OP MA-WEB PL app permission
     */
    allowNoAppPermission?: boolean
  }
}
