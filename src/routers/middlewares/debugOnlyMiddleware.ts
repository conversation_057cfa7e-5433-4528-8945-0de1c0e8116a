import { AppRouteNames } from "@/enums/routers"
import type { Router } from "vue-router"

export function useDebugOnlyMiddleware(
  router: Router,
  options?: {
    debug?: boolean
  }
) {
  options = {
    ...options,
  }

  router.beforeEach((router, from) => {
    try {
      options.debug && console.log("Middleware check debugOnly", router.path)

      //const isdebugOnly = router.meta.debugOnly ?? false
      const isDebugOnly = router.matched.some((record) => record.meta.debugOnly)
      if (isDebugOnly && !isDebugMode()) {
        // Nếu không phải là development mode, chuyển hướng về trang chủ hoặc trang khác
        return {
          name: AppRouteNames.ERROR_404NotFound,
          message: "debugOnly",
          query: {
            ReturnUrl: encodeURIComponent(router.fullPath),
          },
        }
      }
    } catch (ex) {
      console.error("Error occurred in processing of debugOnlyMiddleware", ex)

      // Nếu không phải là development mode, chuyển hướng về trang chủ hoặc trang khác
      return new Error("Error occurred in processing of debugOnlyMiddleware")
    }
  })
}

export function isDebugMode() {
  return process.env.NODE_ENV == "development" || process.env.NODE_ENV == "production" // TODO: tạm thời vì op dùng config riêng nên dùng tạm luôn cho TMS mode
}

declare module "vue-router" {
  interface RouteMeta {
    /**
     * Router work only in dev mode.
     *
     * @default 'false'
     * @example
     * ```ts
     * export const routes: [
     * {
     *  name: AppRouteNames.ABOUT,
     *  path: '/',
     *  component: () => import('./views/About.vue'),
     *  meta: {
     *   debugOnly: true
     *  }
     * },
     * ```
     */
    debugOnly?: boolean
  }
}
