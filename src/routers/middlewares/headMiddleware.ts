import { MaybeComputedRef, useHead } from "@vueuse/head"
import type { Router } from "vue-router"

export function useHeadMiddleware(
  router: Router,
  options?: {
    debug?: boolean
  }
) {
  options = {
    ...options,
  }

  //const { t,locale } = useGlobalI18n()
  //const headTitle = ref<string>('')
  //const headTitleWithLang = computed<string>(() => { locale; return headTitle.value })

  router.beforeEach((route, from) => {
    try {
      options.debug && console.log("Middleware check head", route.path)

      if (route.meta.title) {
        useHead({
          //title: 'aa' //OK
          //title: t('aa') //NOT OK
          //title: () => t('aa') //OK
          title: route.meta.title,
        })
      }
    } catch (ex) {
      console.error("Error occurred in processing of headMiddleware", ex)

      // Nếu không phải là development mode, chuyển hướng về trang chủ hoặc trang khác
      return new Error("Error occurred in processing of headMiddleware")
    }
  })
}

declare module "vue-router" {
  interface RouteMeta {
    /**
     * Set head title for page route
     *
     * @default ''
     * @example
     * ```ts
     * export const routes: [
     * {
     *  name: AppRouteNames.ABOUT,
     *  path: '/',
     *  component: () => import('./views/About.vue'),
     *  meta: {
     *   title: () => lang('page-title')
     *  }
     * },
     * ```
     */
    title?: MaybeComputedRef<string> | Promise<MaybeComputedRef<string>> | undefined
  }
}
