import APPCONFIG from "@/appConfig"
import { AppLayoutNames } from "@/enums/layouts"
import { AppRouteNames } from "@/enums/routers"
import { LoginModeEnum, useAuthService } from "@/services/authService"
import { redirectTo } from "@/utils/appUtil"
import { createRouter, createWebHistory } from "vue-router"
import { useAuthMiddleware } from "./middlewares/authMiddlewareNew"
import { useDebugOnlyMiddleware } from "./middlewares/debugOnlyMiddleware"
import { useLayoutMiddleware } from "./middlewares/layoutMiddleware"
import { useGlobalI18n } from "@/plugins/i18n"
import { useHeadMiddleware } from "./middlewares/headMiddleware"

const { t } = useGlobalI18n()

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_URL),
  routes: [
    {
      name: AppRouteNames.HOME,
      path: "/",
      //component: () => import("@/views/Home.vue"),
      meta: {},
      redirect: (to) => {
        // default child path
        return { name: AppRouteNames.DASHBOARD }
      },
    },
    {
      name: AppRouteNames.DASHBOARD,
      path: "/dashboard",
      component: () => import("@/views/Dashboard.vue"),
      meta: {
        title: () => t("page-Dashboard.page-title"),
      },
    },
    {
      name: AppRouteNames.PAYMENTLINK_LIST,
      path: "/paymentlink",
      component: () => import("@/views/paymentlink/LinkManagement.vue"),
      meta: {
        title: () => t("page-LinkManagement.page-title"),
      },
      children: [
        {
          name: AppRouteNames.PAYMENTLINK_DETAIL,
          path: ":linkId",
          props: (route) => ({
            linkId: route.params.linkId, // Pull `id` from route params
            visible: true,
          }),
          component: () => import("@/views/paymentlink/LinkDetail.vue"),
          meta: {
            title: () => t("page-LinkManagement-detail.page-title"),
          },
        },
        {
          name: AppRouteNames.PAYMENTLINK_DETAIL__TRANSDETAIL,
          path: ":linkId/trans/:tranId",
          props: (route) => ({
            transactionId: route.params.tranId, // Pull `id` from route params
            visible: true,
          }),
          component: () => import("@/views/transaction/TransactionDetail.vue"),
          meta: {
            title: () => t("page-Transaction-Detail.page-title"),
          },
        },
        {
          name: AppRouteNames.PAYMENTLINK_CREATE,
          path: "create",
          props: (route) => ({
            visible: true,
          }),
          component: () => import("@/components/paymentLink/AddNew.vue"),
          meta: {
            title: () => t("components-paymentLink-add.head-title"),
          },
        },
        {
          name: AppRouteNames.PAYMENTLINK_EDIT,
          path: "edit/:linkId",
          props: (route) => ({
            linkId: route.params.linkId, // Pull `id` from route params
            visible: true,
          }),
          component: () => import("@/components/paymentLink/Edit.vue"),
          meta: {
            title: () => t("components-paymentLink-edit.head-title"),
          },
        },
      ],
    },
    // {
    //   name: AppRouteNames.PAYMENTLINK_DETAIL,
    //   path: "/paymentlink/:linkId",
    //   props: true,
    //   component: () => import("@/views/paymentlink/LinkDetail.vue"),
    //   meta: {},
    // },
    {
      name: AppRouteNames.TRANSACTIONMANAGEMENT_LIST,
      path: "/transaction",
      component: () => import("@/views/transaction/TransactionManagement.vue"),
      meta: {
        title: () => t("page-Transaction-Management.page-title"),
      },
      children: [
        {
          name: AppRouteNames.TRANSACTIONMANAGEMENT_DETAIL,
          //path: ":tranIds",
          path: ":tranId+", // support  matches /tranId, /tranId/tranId2, /tranId/tranId2/tranId3, etc
          props: (route) => {
            const paramsTranIds = Array.isArray(route.params.tranId) ? [...route.params.tranId] : [route.params.tranId]
            return {
              transactionId: paramsTranIds.lastOrDefault(), // Pull `id` from route params
              transactionIds: paramsTranIds, // Pull `id`[] from route params
              visible: true,
            }
          },
          component: () => import("@/views/transaction/TransactionDetail.vue"),
          meta: {
            title: () => t("page-Transaction-Detail.page-title"),
          },
        },
      ],
    },
    {
      name: AppRouteNames.REPORT,
      path: "/report",
      component: () => import("@/views/report/TransactionReportManagement.vue"),
      meta: {
        title: () => t("page-ReportManagement.page-title"),
      },
    },
    {
      name: AppRouteNames.PROFILE,
      path: "/profile/:username",
      props: true,
      component: () => import("@/views/Home.vue"),
    },
    {
      name: "profile-favorites",
      path: "/profile/:username/favorites",
      props: true,
      component: () => import("@/views/Home.vue"),
    },
    {
      name: AppRouteNames.SETTINGS,
      path: "/settings",
      component: () => import(/* webpackChunkName: "settings" */ "@/views/Home.vue"),
    },
    {
      name: AppRouteNames.TEST_LANG,
      path: "/test/lang",
      component: () => import("@/views/test/Lang.vue"),
      meta: {
        layout: AppLayoutNames.Default,
        debugOnly: true,
      },
    },
    {
      name: "TestDatePicker",
      path: "/test/date",
      component: () => import("@/views/test/TestDatePickerView.vue"),
      meta: {
        layout: AppLayoutNames.Empty,
        debugOnly: true,
      },
    },
    {
      name: AppRouteNames.LOGIN,
      path: "/login",
      component: () => import("@/views/Login.vue"),
      meta: {
        allowAnonymous: true,
        debugOnly: true,
        layout: AppLayoutNames.Empty,
      },
      async beforeEnter(to, from) {
        // TODO: Khối xử lý login+authMiddleware+router(LOGIN) đang bị cồng kềnh, cần xử lý lại.
        console.log("LOGIN page", "beforeEnter", from.fullPath)

        const { verifyLoginDataTask } = useAuthService()
        const { success, mode, errCode } = await verifyLoginDataTask.perform()

        if (success) {
          console.log("LOGIN page", "beforeEnter", "success")
          const returnUrl = to.query.returnUrl?.toString()

          return returnUrl ?? "/"
        } else if (mode == LoginModeEnum.OP) {
          console.log("LOGIN page", "beforeEnter", "opcheck", errCode)

          // Block để tránh lặp vô tận ở authMiddleware vốn đã có check. Đúng ra nên làm hết ở router
          //
          if (errCode === 401) {
            // Tự để hàm checkLoginOPTask tự xử lý redirect sang MA-WEB
            redirectTo(APPCONFIG.APP_LOGIN_URL)
            return false
          }

          if (errCode === 403) {
            router.push({ name: AppRouteNames.ERROR_403NoAppPermission }) // Điều hướng trực tiếp
            return false
          }

          return false
        }
      },
    },
    {
      path: "/logout",
      component: () => import("@/views/errors/Error404NotFound.vue"),
      async beforeEnter(to, from) {
        const { logoutTask } = useAuthService()

        await logoutTask.perform()
        redirectTo(APPCONFIG.APP_LOGIN_URL)

        return false
      },
      // redirect(to) {
      //   console.log("TO LOGOUT")
      //   const {logoutTask} = useAuthService()

      //   logoutTask.perform();
      //   redirectTo(APPCONFIG.APP_LOGIN_URL)
      //   return {
      //     path: "/",
      //   }
      // },
    },
    // ADMIN AREA
    {
      path: "/admin",
      meta: {
        layout: AppLayoutNames.Admin,
        sysAdminOnly: true,
        allowNoMerchant: true,
      },
      children: [
        {
          path: "",
          redirect: (to) => {
            // default child path
            return { name: AppRouteNames.ADMIN_USERMANAGEMENT }
          },
        },
        {
          name: AppRouteNames.ADMIN_USERMANAGEMENT,
          path: "user",
          component: () => import("@/views/admin/user/UserManagement.vue"),
          meta: {
            title: () => t("page-UserManagement.page-title"),
          },
        },
        {
          name: AppRouteNames.ADMIN_USERMANAGEMENT_EDIT,
          path: "user/:userSId",
          props: (route) => ({
            userSId: route.params.userSId, // Pull `id` from route params
            mode: "Edit", // Hard code `Edit`
          }),
          component: () => import("@/views/admin/user/UserDetail.vue"),
          meta: {
            cache: false, // Màn ADD/EDIT mặc định reload từ đầu
            title: () => t("page-UserDetail.head-title-edit"),
          },
        },
        {
          path: "role",
          component: () => import("@/components/RouterViewKeepAlive.vue"),
          props: {
            name: "AdminRole", // quickset name cho RouterViewKeepAlive component. (phương án 2. Bạn có thể làm như quản lý user)
          },
          children: [
            {
              name: AppRouteNames.ADMIN_ROLEMANAGEMENT,
              path: "",
              component: () => import("@/views/admin/role/RoleManagement.vue"),
              meta: {
                title: () => t("page-RoleManagement.page-title"),
              },
            },
            {
              name: AppRouteNames.ADMIN_ROLEMANAGEMENT_EDIT,
              path: "edit/:roleId",
              props: (route) => ({
                roleId: route.params.roleId, // Pull `id` from route params
                mode: "Edit", // Hard code `Edit`
              }),
              component: () => import("@/views/admin/role/RoleDetail.vue"),
              meta: {
                cache: false, // Màn ADD/EDIT mặc định reload từ đầu
                title: () => t("page-RoleDetail.head-title-edit"),
              },
            },
            {
              name: AppRouteNames.ADMIN_ROLEMANAGEMENT_ADD,
              path: "add",
              props: {
                mode: "Add",
              },
              component: () => import("@/views/admin/role/RoleDetail.vue"),
              meta: {
                cache: false, // Màn ADD/EDIT mặc định reload từ đầu
                title: () => t("page-RoleDetail.page-title-add"),
              },
            },
          ],
        },
      ],
    },
    // ERROR AREA
    {
      path: "/error",
      meta: {
        isErrorPage: true,
      },
      children: [
        {
          name: AppRouteNames.ERROR_404NotFound,
          path: "404",
          component: () => import("@/views/errors/Error404NotFound.vue"),
          meta: {
            title: () => t("errors.404.head-title"),
          },
        },
        {
          name: AppRouteNames.ERROR_403Forbidden,
          path: "403",
          component: () => import("@/views/errors/Error403Forbidden.vue"),
          meta: {
            title: () => t("errors.403.head-title"),
          },
        },
        {
          name: AppRouteNames.ERROR_403NoMerchant,
          path: "403/NoMerchant",
          component: () => import("@/views/errors/Error403NoMerchant.vue"),
          meta: {
            //allowAnonymous: true, // để fix tạm lỗi lặp vô tận khi không load được merchant
            allowNoMerchant: true,
            layout: AppLayoutNames.ErrorNoMenu,
            title: () => t("errors.403-NoMerchant.head-title"),
          },
        },
        {
          name: AppRouteNames.ERROR_403NoAppPermission,
          path: "403/NoAppPermission",
          component: () => import("@/views/errors/Error403NoAppPermission.vue"),
          meta: {
            //allowAnonymous: true, // để fix tạm lỗi lặp vô tận
            allowNoAppPermission: true,
            layout: AppLayoutNames.ErrorNoMenu,
            title: () => t("errors.403-NoAppPermission.head-title"),
          },
        },
      ],
    },
    // The last
    {
      path: "/:pathMatch(.*)*",
      component: () => import("@/views/errors/Error404NotFound.vue"),
      meta: {
        allowAnonymous: true,
        layout: AppLayoutNames.Empty,
        title: () => t("errors.404.head-title"),
      },
    },
  ],
})

const debug = true
// Ở trước thì chạy trước
useAuthMiddleware(router, { debug })
useDebugOnlyMiddleware(router, { debug })
useLayoutMiddleware(router, { debug })
useHeadMiddleware(router, { debug })

export default router

declare module "vue-router" {
  interface RouteMeta {
    /**
     * Sử dụng để đánh dấu page này là page lỗi.
     */
    isErrorPage?: boolean
  }
}
