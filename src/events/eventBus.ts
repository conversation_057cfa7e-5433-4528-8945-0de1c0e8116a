import { useEventBus, type EventBusKey } from "@vueuse/core"

export enum EventCommandEnum {
  UpdateList = "Update-List",
  UpdateDetail = "Update-Detail",
  Created = "Created",
  Edited = "Edited",
  Deleted = "Deleted",
}

type List_EventBusKey = {
  command: EventCommandEnum.UpdateList
  data?: {}
}
type CRUD_EventBusKey = {
  command: EventCommandEnum.UpdateList | EventCommandEnum.Created | EventCommandEnum.Edited | EventCommandEnum.Deleted
  data?: { id: uuid }
}

export const useManagementEventBusKey: EventBusKey<List_EventBusKey> = Symbol("symbol-key")
export const roleManagementEventBusKey: EventBusKey<List_EventBusKey> = Symbol("symbol-key")

export const paymentLinkEventBusKey: EventBusKey<CRUD_EventBusKey> = Symbol("symbol-key")
export const usePaymentLinkEventBus = () => useEventBus(paymentLinkEventBusKey)

export const userDetailEventBusKey: EventBusKey<CRUD_EventBusKey> = Symbol("symbol-key")
export const roleDetailEventBusKey: EventBusKey<CRUD_EventBusKey> = Symbol("symbol-key")
