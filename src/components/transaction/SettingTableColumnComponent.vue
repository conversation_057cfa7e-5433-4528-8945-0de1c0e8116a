<script setup lang="ts">
import { ref } from "vue";
import { TransactionColumnDisplay } from '@/apis/transactionApi';

import PaymentLinkClose from '@/assets/icons/payment-link-close.svg';
import DragDropIcon from '@/assets/icons/drag-drop-column-display.svg';
import { type UseDraggableReturn, VueDraggable } from 'vue-draggable-plus';
import { useDebounceFn } from "@vueuse/core";
import APPCONFIG from "@/appConfig";
import { useTransactionService } from "@/services/transactionService";
import { useAuthService } from "@/services/authService";

const { formatCololumnTable } = useTransactionService();
const { authUserId } = useAuthService();

defineProps({
  visible: Boolean,
});

const [modelColumnModel, modelColumnModelModifiers] = defineModel<TransactionColumnDisplay[]>('modelColumn', {
  default: []
})

const isSelectAll = ref<boolean>(false);
const modelSelected = ref<any[]>([1]);
const refDragElement = ref<UseDraggableReturn>();
const _modelValue = ref<any[]>([]);
const disabled = ref<boolean>(true);
const keywords = ref<string>('');

const emit = defineEmits(["closeDialog"]);

const addDisplayColumn = () => {
  modelColumnModel.value.forEach((item) => {
    if (modelSelected.value.includes(item.value)) {
      item.isDisplay = true;
    } else {
      item.isDisplay = false;
    }
  });

  modelColumnModel.value = modelColumnModel.value.sort((a, b) => (a.ordinal > b.ordinal) ? 1 : ((b.ordinal > a.ordinal) ? -1 : 0));
  localStorage.setItem(`dispay-column-${authUserId}`, JSON.stringify(modelColumnModel.value));
  emit("closeDialog");
}

const closeDisplayColumn = () => {
  emit("closeDialog");
}

const onUpdate = () => {
  _modelValue.value = _modelValue.value.map((value, index) => {
    value.ordinal = index + 1;
    return value
  });
}

const onShow = () => {
  _modelValue.value = modelColumnModel.value;
  modelSelected.value = _modelValue.value.filter((item) => item.isDisplay).map((item) => { return item.value });
}

const onClickSelectAll = () => {
  modelSelected.value = isSelectAll.value
    ? _modelValue.value.map((item) => item.value)
    : [1]
}

const onChangeKeyword = useDebounceFn(() => {
  _modelValue.value = modelColumnModel.value.filter(el => formatCololumnTable(el.field).toLowerCase().includes(keywords.value.toLocaleLowerCase()));
}, APPCONFIG.DEBOUNCED_DELAY_INPUT);

const itemSelected = () => {
  return `${modelSelected.value.length}/${_modelValue.value.length}`
}
</script>

<template>
  <Dialog :visible="visible" modal class="display-column" :draggable="false" @show="onShow">
    <template #header>
      <span class="title">{{ $t("component-transaction-setting-column.header.title") }}</span>
      <button type="button" class="btn-close" @click="closeDisplayColumn">
        <SvgIcon :src="PaymentLinkClose" />
      </button>
    </template>

    <template #footer>
      <button class="btn-cancel" @click="closeDisplayColumn">{{ $t("component-transaction-setting-column.footer.cancel") }}</button>
      <button class="btn-confirm" @click="addDisplayColumn">{{ $t("component-transaction-setting-column.footer.apply") }}</button>
    </template>

    <InputTextFilterClearable
      v-model="keywords"
      placeholder="Search Column name"
      class="input-search-column"
      @keyup="onChangeKeyword()"
    />

    <div class="select-all-item">
      <div class="group-check-box-all">
        <Checkbox v-model="isSelectAll" :binary="true" class="select-all-checkbox" @change="onClickSelectAll()" />
        <span class="select-all">{{ $t("component-transaction-setting-column.content.select-all") }}</span>
      </div>

      <div class="element-is-selected">{{ itemSelected() }} {{ $t("component-transaction-setting-column.content.selected") }}</div>
    </div>

    <VueDraggable
      ref="refDragElement"
      v-model="_modelValue"
      :animation="150"
      :disabled="disabled"
      ghost-class="ghost"
      class="drag-item"
      @update="onUpdate()"
    >
      <div v-for="(item, index) in _modelValue" :key="index" class="select-item">
        <div class="group-check-box-item">
          <Checkbox v-model="modelSelected" :value="item.value" :disabled="item.isDisabled" class="select-item-checkbox" />
          <span class="select-item-label">{{ formatCololumnTable(item.field) }}</span>
        </div>

        <div class="drag-drop-icon" @mouseenter="disabled = false" @mouseleave="disabled = true">
          <SvgIcon :src="DragDropIcon" />
        </div>
      </div>
    </VueDraggable>
  </Dialog>
</template>

<style lang="scss">
.display-column {
  border-radius: 12px;
  padding: 16px 24px;
  min-width: 496px;
  max-height: 924px;

  .p-dialog-header {
    padding: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .p-dialog-header-icons {
      display: none;
    }

    .title {
      font-size: 24px;
      font-weight: 600;
      line-height: 36px;
      color: #1C1C1C;
    }

    .btn-close {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      padding: 8px;
    }
  }

  .p-dialog-content {
    padding: 0;
    width: 448px;
    overflow: hidden;

    .select-all-item {
      margin-top: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 44px;
      border: 1px solid #DCDCDC;
      border-radius: 4px;
      padding: 10px 16px;
    }

    .group-check-box-all {
      .select-all-checkbox {
        margin-right: 11px;

        .p-checkbox-box {
          width: 18px;
          height: 18px;
          border: 2px solid #6C6C6C;
          border-radius: 2px;
        }
      }

      .select-all {
        font-weight: 600;
        color: #404040;
      }
    }

    .element-is-selected {
      color: #6C6C6C;
    }

    .select-item {
      margin-top: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 44px;
      border: 1px solid #DCDCDC;
      border-radius: 4px;
      padding: 10px 16px;

      &:last-child {
        margin-bottom: 16px;
      }
    }

    .group-check-box-item {
      .select-item-checkbox {
        margin-right: 11px;

        .p-checkbox-box {
          width: 18px;
          height: 18px;
        }
      }

      .select-item-label {
        color: #404040;
      }
    }
  }

  .p-dialog-footer {
    padding: 0;

    .btn-cancel {
      min-width: 100px;
      height: 40px;
      border-radius: 4px;
      padding: 10px 24px;
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      color: #404040;
      margin-right: 20px;
      border: none;
    }

    .btn-confirm {
      min-width: 120px;
      height: 40px;
      border-radius: 4px;
      padding: 10px 24px;
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      color: #FFFFFF;
      border: none;
      background-color: #2E6BE5;
    }
  }

  .ghost {
    opacity: 0.5;
    background: #fff;
    box-shadow: 0px 4px 5px 0px #00000033;
  }

  .drag-item {
    height: 70vh;
    overflow: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      border: 3px solid #DCDCDC;
    }
  }
}
</style>
