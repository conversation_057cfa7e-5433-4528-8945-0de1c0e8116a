<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useToggle } from "@vueuse/core";
import { enumToSelectListItem } from "@/utils/enumUtil";
import { EnumTransactionPaymentMethod, EnumTransactionStatus } from "@/app.const";

import AppMultipleSelectOverlayPanel from "../ui/AppMultipleSelectOverlayPanel.vue";
import OverlayPanel from "primevue/overlaypanel";
import ArrowDown from '@/assets/icons/arrow-down.svg';
import { SelectListItem } from "@/types/base";
import { useI18n } from "vue-i18n";

const i18n = useI18n();

const [visibleModel, visibleModelModifiers] = defineModel<boolean>("visible", {
  default: false,
})

const [modelPaymentMethodModel, modelPaymentMethodModelModifiers] = defineModel<any[]>("modelPaymentMethod", {
  default: [],
})
const innerModelPaymentMethodModel = ref<any[]>([])

const [modelTranStatusModel, modelTranStatusModelModifiers] = defineModel<any[]>("modelTranStatus", {
  default: [],
})
const innerModelTranStatusModel = ref<any[]>([])

const [isSelectedAllPaymentMethodModel] = defineModel<boolean>("isSelectedAllPaymentMethod", {
  default: true,
})
const innerIsSelectedAllPaymentMethod = ref(true)

const [isSelectedAllTransTypeModel] = defineModel<boolean>("isSelectedAllTransType", {
  default: true,
})
const innerIsSelectedAllTransType = ref(true)

const listItemPaymentMethod = ref<SelectListItem[]>([]);
listItemPaymentMethod.value = enumToSelectListItem(EnumTransactionPaymentMethod).map((item) => {
  return {
    value: item.value,
    label: i18n.t(`component-transaction-more-filter.list-payment-method.${item.label}`),
  }
});
const labelPaymentMethod = computed(() =>
  innerModelPaymentMethodModel.value.length == 0
    ? i18n.t("component-transaction-more-filter.list-payment-method.All")
    : listItemPaymentMethod.value
      .filter((item) => innerModelPaymentMethodModel.value.includes(item.value))
      .map((item) => item.label)
      .join(", ")
);

const listItemTranStatus = ref<SelectListItem[]>([]);
listItemTranStatus.value = enumToSelectListItem(EnumTransactionStatus)
  .filter((item) => {
    return (
      item.value !== EnumTransactionStatus.AwaitTransactionResult &&
      item.value !== EnumTransactionStatus.AwaitMerchantApproval &&
      item.value !== EnumTransactionStatus.MerchantRejected &&
      item.value !== EnumTransactionStatus.AwaitOnepayApprovval &&
      item.value !== EnumTransactionStatus.OnepayRejected &&
      item.value !== EnumTransactionStatus.MerchantApproved &&
      item.value !== EnumTransactionStatus.OnepayApproved
    )
  })
  .map((item) => {
    return {
      value: item.value,
      label: i18n.t(`component-transaction-more-filter.list-trans-status.${item.label}`),
    }
  })

const labelTranStatus = computed(() =>
  innerModelTranStatusModel.value.length == 0
    ? i18n.t("component-transaction-more-filter.list-trans-status.All")
    : listItemTranStatus.value
      .filter((item) => innerModelTranStatusModel.value.includes(item.value))
      .map((item) => item.label)
      .join(", ")
);

const refFilterPaymentMethod = ref<InstanceType<typeof AppMultipleSelectOverlayPanel> | null>(null)
const [isVisiblePaymentMethod] = useToggle();
const toggleFilterPaymentMethod = (event: any) => {
  refFilterPaymentMethod.value?.toggle(event)
}

const refFilterTranStatus = ref<InstanceType<typeof AppMultipleSelectOverlayPanel> | null>(null)
const [isVisibleTranStatus] = useToggle();
const toggleFilterTranStatus = (event: any) => {
  refFilterTranStatus.value?.toggle(event)
}

const emit = defineEmits<{
  /**
   * Emits whenever the picker opens/closes overlay panel
   */
  toggle: [open: boolean]
  /**
   * Callback to invoke when the overlay is shown.
   *
   * @memberof OverlayPanel
   */
  show: []
  /**
   * Callback to invoke when the overlay is hidden.
   *
   * @memberof OverlayPanel
   */
  hide: []
  /**
   * Emits when the user click on cancel button
   */
  cancel: []
  /**
   * Emits when the user click on apply button
   * @param value - json object containing the dates: {startDate, endDate}
   */
  apply: [value1: any[], value2: any[]]

  /**
   * Emits when user enter value in input
   * @param value - string text that to filter
  **/
  filter: [value?: string]
}>()

const refTransMoreFilter = ref<InstanceType<typeof OverlayPanel> | null>(null);

function onToggle(open: boolean) {
  //console.log('AppDateRangPicker.onToggle', open)
  visibleModel.value = open
  emit("toggle", open)
  if (open) {
    emit("show")
  } else {
    emit("hide")
  }
}

//#region Public methods

/**
 * Toggles the visibility of the overlay.
 * @param {Event} event - Browser event.
 * @param {*} [target] - Optional target if event.currentTarget should not be used.
 *
 * @memberof OverlayPanel
 */
const toggle = (event: Event, target?: any): void => {
  refTransMoreFilter.value?.toggle(event, target)
}

/**
 * Shows the overlay.
 * @param {Event} event - Browser event.
 * @param {*} [target] - Optional target if event.currentTarget should not be used.
 *
 * @memberof OverlayPanel
 */
const show = (event: Event, target?: any): void => {
  refTransMoreFilter.value?.show(event, target)
}

/**
 * Hides the overlay.
 *
 * @memberof OverlayPanel
 */
const hide = (): void => {
  refTransMoreFilter.value?.hide()
}

/*
 * Check all payment method and trans type when clear filter action
*/
const checkAllFilter = (): void => {
  innerIsSelectedAllPaymentMethod.value = true;
  innerIsSelectedAllTransType.value = true;
}

defineExpose({
  toggle,
  show,
  hide,
  checkAllFilter
})

//#endregion Public methods

const onShow = () => {
  // store states
  innerModelPaymentMethodModel.value = modelPaymentMethodModel.value
  innerModelTranStatusModel.value = modelTranStatusModel.value
  innerIsSelectedAllPaymentMethod.value = isSelectedAllPaymentMethodModel.value
  innerIsSelectedAllTransType.value = isSelectedAllTransTypeModel.value
  onToggle(true)
}

const onClickCancel = () => {
  // reset states
  innerModelPaymentMethodModel.value = modelPaymentMethodModel.value
  innerModelTranStatusModel.value = modelTranStatusModel.value
  innerIsSelectedAllPaymentMethod.value = isSelectedAllPaymentMethodModel.value
  innerIsSelectedAllTransType.value = isSelectedAllTransTypeModel.value
  emit('cancel');
  hide()
}

const onClickApply = () => {
  // save states
  modelPaymentMethodModel.value = innerModelPaymentMethodModel.value
  modelTranStatusModel.value = innerModelTranStatusModel.value
  isSelectedAllPaymentMethodModel.value = innerIsSelectedAllPaymentMethod.value
  isSelectedAllTransTypeModel.value = innerIsSelectedAllTransType.value
  emit('apply', modelPaymentMethodModel.value, modelTranStatusModel.value);
  hide()
}
</script>

<template>
  <OverlayPanel
    ref="refTransMoreFilter"
    class="panel-more-filter"
    @show="onShow"
    @hide="onToggle(false)"
  >
    <div class="payment-method">
      <div class="title">
        {{ $t("component-transaction-more-filter.payment-method") }}
      </div>

      <div class="filter-model">
        <Button class="dropdown-filter-model" @click="toggleFilterPaymentMethod">
          <span class="text-truncate" style="width: 264px;text-align: left;">{{ labelPaymentMethod }}</span>

          <SvgIcon :src="ArrowDown" />

          <DropdownMultipleSelectOverlayPanel
            ref="refFilterPaymentMethod"
            v-model:modelValue="innerModelPaymentMethodModel"
            v-model:visible="isVisiblePaymentMethod"
            v-model:is-seclected-all="innerIsSelectedAllPaymentMethod"
            class="panel-morefilter--ddlFilterPaymentMethod"
            :options="listItemPaymentMethod"
            option-label="label"
            option-value="value"
            :select-all-allowed="true"
            :select-all-label="$t('component-transaction-more-filter.list-payment-method.All')"
            :select-all-value="[]"
            :auto-apply="true"
          ></DropdownMultipleSelectOverlayPanel>
        </Button>
      </div>
    </div>

    <div class="trans-status">
      <div class="title">
        {{ $t("component-transaction-more-filter.status") }}
      </div>

      <div class="filter-model">
        <Button class="dropdown-filter-model" @click="toggleFilterTranStatus">
          <span class="text-truncate" style="width: 264px;text-align: left;">{{ labelTranStatus }}</span>

          <SvgIcon :src="ArrowDown" />

          <DropdownMultipleSelectOverlayPanel
            ref="refFilterTranStatus"
            v-model:modelValue="innerModelTranStatusModel"
            v-model:visible="isVisibleTranStatus"
            v-model:is-seclected-all="innerIsSelectedAllTransType"
            class="panel-morefilter--ddlFilterTranStatus"
            :options="listItemTranStatus"
            option-label="label"
            option-value="value"
            :select-all-allowed="true"
            :select-all-label="$t('component-transaction-more-filter.list-trans-status.All')"
            :select-all-value="[]"
            :auto-apply="true"
          ></DropdownMultipleSelectOverlayPanel>
        </Button>
      </div>
    </div>

    <div class="panel-footer">
      <Button :label="$t('common.buttons.cancel')" class="btn-cancel" @click="onClickCancel" />
      <Button :label="$t('common.buttons.apply')" class="btn-apply" @click="onClickApply" />
    </div>
  </OverlayPanel>
</template>

<style lang="scss">
.panel-more-filter {
  .p-overlaypanel-content {
    width: 360px;
    padding: 16px 24px 24px 24px;

    .title {
      font-weight: 600;
      color: #404040;
      margin-bottom: 4px;
    }

    .filter-model {
      width: 100%;

      .dropdown-filter-model {
        width: 100%;
        padding: 10px 16px;
        background-color: #fff;
        border: 1px solid #DCDCDC;
        color: #404040;
        margin-bottom: 16px;
      }
    }

    .panel-footer {
      margin-top: 8px;
      display: flex;
      justify-content: end;
      align-items: center;

      .btn-cancel {
        border: none;
        margin-right: 16px;
        background-color: #F5F5F5;
        color: #404040;
      }

      .btn-apply {
        width: 120px;
      }
    }
  }
}

.panel-morefilter--ddlFilterPaymentMethod,
.panel-morefilter--ddlFilterTranStatus {
  min-width: 312px;
  .select-item-wrapper {
    max-height: none;
  }
}
</style>
