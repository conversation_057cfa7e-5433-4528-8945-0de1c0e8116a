<script setup lang="ts">
import LogoOnePay from "@/assets/icons/onepay-logo-download-qrcode.svg?url";

import { formatCurrency } from "@/utils/formatUtil";
import { ref } from "vue";
import * as htmlToImage from "html-to-image";
import { useToastService } from "@/services/toastService";

const toastService = useToastService();
const qrTemplate = ref();
const isShowTemplateDownload = ref(false);
const downloadTask = ref(false);

const props = withDefaults(
  defineProps<{
    logo?: string
    linkName?: string
    qrcode?: string
    paymentAmount?: number
    paymentCurrency?: string
    dueDate?: string
  }>(),
  {
    logo: "",
    linkName: "",
    qrcode: "",
    paymentAmount: 0,
    paymentCurrency: "",
    dueDate: ""
  }
)

const downloadQr = () => {
  downloadTask.value = true;
  isShowTemplateDownload.value = true;

  setTimeout(() => {
    htmlToImage
      .toPng(qrTemplate.value)
      .then((dataUrl) => {
        if (!dataUrl) return
        //console.debug("htmlToImage", "dataUrl", dataUrl);

        const downloadImage = document.createElement("a");
        downloadImage.href = dataUrl;
        downloadImage.download = props.linkName ? `ImageQR-${props.linkName}.png` : "ImageQR.png";
        downloadImage.click();
        downloadImage.remove();
      })
      .catch((error) => {
        toastService.error({});
        // eslint-disable-next-line no-console
        console.error("htmlToImage", error);
      })
      .finally(() => {
        isShowTemplateDownload.value = false;
        downloadTask.value = false;
      });
  }, 100)
}

const show = () => {
  isShowTemplateDownload.value = true;
}

const hide = () => {
  isShowTemplateDownload.value = false;
}
defineExpose({
  show,
  hide,
  downloadQr
})
</script>

<template>
  <Teleport to="body">
    <div v-show="isShowTemplateDownload" class="qr-code-template">
      <div ref="qrTemplate" class="qr-code-template-container">
        <div class="qr-code-template-header">
          <div class="qr-code--merchant-logo">
            <img v-if="props.logo" :src="props.logo" alt="" class="merchant-logo" />
          </div>
          <div class="qr-code--link-name">
            {{ props.linkName }}
          </div>
        </div>

        <div class="qr-code-template-body">
          <div class="qr-code--img-wrapper">
            <img :src="props.qrcode" alt="" class="qr-code--img" />
          </div>

          <div class="link-amount-info-wrapper">
            <div v-if="props.paymentAmount" class="qr-code--amount">
              {{ formatCurrency(props.paymentAmount, props.paymentCurrency) }}
            </div>

            <div v-if="props.dueDate" class="qr-code--duedate">
              {{ $t("components-download-qr-dialog.payment-term") }} {{ props.dueDate }}
            </div>
          </div>
        </div>

        <div class="qr-code-template-footer">
          <div class="qr-code--onepay-logo">
            <!-- POWERED BY <SvgIcon :src="LogoOnePay" /> -->
            <!-- POWERED BY <img src="../assets/icons/onepay-logo-download-qrcode.svg" class="img-onepay-logo" /> -->
            <span>{{ $t("components-download-qr-dialog.powered-by") }}</span><img :src="LogoOnePay" class="img-onepay-logo" />
          </div>
        </div>
      </div>
    </div>

    <div class="app-loading-download-qr">
      <AppLoading v-if="downloadTask" />
    </div>
  </Teleport>
</template>

<style lang="scss">
.qr-code-template {
  position: fixed;
  top: 0;
  left: -9999px;
  z-index: 9999;

  .qr-code-template-container {
    width: 23.4375rem; // 375px
    // height có 3 mode giá trị: 450px 490px 590px
    // max-height: 36.875rem;
    background-color: #fff;
    padding: 1.5rem;

    .qr-code-template-header {
      margin-bottom: 1rem;

      .qr-code--merchant-logo {
        display: flex;
        justify-content: center;
        align-items: center;
        //height: 7.5rem;
        // height 120px là cần phải được tính từ mép trên luôn chứ ko phải vào content
        // -> đối sang dùng margin vì đã có padding 24px bao ngoài
        margin-bottom: 1.5rem;

        .merchant-logo {
          object-fit: contain;
          //max-width: 9rem;
          //height: 4rem;
          // Trường hợp này logo sẽ to max 162x72
          max-width: 10.125rem;
          max-height: 4.5rem;
          height: 100%;
          width: 100%;
        }
      }

      .qr-code--link-name {
        color: var(--text-headings, #1C1C1C);
        text-align: center;
        /* Inter/B1/16_Regular */
        font-style: normal;
        font-weight: 400;
        line-height: 1.5rem; /* 150% */
        
        //word-wrap: break-word;
        //word-break: break-word;
        @include global.allow_line_break();

        margin-bottom: 1rem;
      }
    }

    .qr-code-template-body {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-bottom: 1.5rem;

      .qr-code--img-wrapper {
        width: 15rem;
        height: 15rem;

        padding: 1rem;
        border: 1px solid #2e6be5;
        border-radius: 0.5rem;

        .qr-code--img {
          width: 100%;
        }
      }

      .qr-code--amount {
        margin-top: 1rem;
        color: var(--text-headings, #1C1C1C);
        text-align: center;
        
        font-family: var(--Typeface-family-Inter, Inter);
        font-size: var(--Typeface-size-2xl, 1.5rem);
        font-style: normal;
        font-weight: 600;
        line-height: 2.25rem; /* 150% */
      }

      .qr-code--duedate {
        margin-top: 0.25rem;
        color: var(--100_646464, #646464);
        text-align: center;

        font-size: var(--Typeface-size-md, 0.875rem);
        font-style: normal;
        font-weight: 400;
        line-height: 1.25rem; /* 142.857% */
      }
    }

    .qr-code-template-footer {
      .qr-code--onepay-logo {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.375rem;

        font-weight: 600;
        line-height: 0.75rem;
        font-size: 0.625rem;
        color: #6b6b6b;

        .img-onepay-logo {
          height: 1.125rem;
        }
      }
    }
  }
}

.app-loading-download-qr {
  .component-loading {
    position: fixed;
    top: 0;
    z-index: 9999;
    background-color: black;
    opacity: 0.75;
  }
}
</style>
