<script lang="ts" setup>
import ToolTipIcon from "@/assets/icons/add-payment-info-tooltip.svg"
import ArrowDown from "@/assets/icons/arrow-down.svg"
import CLoseIcon from "@/assets/icons/payment-link-add-edit-close.svg"
import PaymentLinkClose from "@/assets/icons/payment-link-close.svg"

import { CURERENCY_USD } from "@/app.const"
import APPCONFIG from "@/appConfig"
import { useAuthService } from "@/services/authService"
import { caculatePaymentAmount } from "@/services/paymentLinkService.utils"
import type { CurrencyConfig, CurrencyExchangeRate, PresetAmountJson } from "@/types/apis"
import { formatCurrency, formatExchangeRate } from "@/utils/formatUtil"
import { computed, reactive, ref, watch } from "vue"
import { useI18n } from "vue-i18n"

const props = withDefaults(
  defineProps<{
    visible: boolean
    isAdd: boolean
    propCurrencyConfig?: CurrencyConfig[]
    propCurrency?: string
    propPresetAmountJson?: PresetAmount<PERSON><PERSON>
    propDiscounted?: number
    propTaxed?: number
    propOtherFeed?: number
    propTotalAmount?: number | null
    propPaymentAmount?: number | null
    propPaymentCurrency?: string | null
  }>(),
  {
    visible: false,
    isAdd: true,
    propCurrencyConfig: undefined,
    propCurrency: undefined,
    propPresetAmountJson: undefined,
    propDiscounted: undefined,
    propTaxed: undefined,
    propOtherFeed: undefined,
    propTotalAmount: undefined,
    propPaymentAmount: undefined,
    propPaymentCurrency: undefined,
  }
)

const getDefaultAmountJson = (isEdit: boolean = false): PresetAmountJson => {
  if (isEdit && props.propPresetAmountJson) {
    return {
      amount: props.propPresetAmountJson.amount,
      enableDiscount: props.propPresetAmountJson.enableDiscount,
      discount: props.propPresetAmountJson.discount,
      discountIsPercent: props.propPresetAmountJson.discountIsPercent,
      enableTax: props.propPresetAmountJson.enableTax,
      tax: props.propPresetAmountJson.tax,
      enableOtherFee: props.propPresetAmountJson.enableOtherFee,
      otherFeeLabel: props.propPresetAmountJson.otherFeeLabel,
      otherFee: props.propPresetAmountJson.otherFee,
      otherFeeIsPercent: props.propPresetAmountJson.otherFeeIsPercent,
    }
  }

  return {
    amount: 0,
    enableDiscount: false,
    discount: null,
    discountIsPercent: true,
    enableTax: false,
    tax: null,
    enableOtherFee: false,
    otherFeeLabel: "",
    otherFee: null,
    otherFeeIsPercent: true,
  }
}

const { selectMerchantProfileTask, authMerchantProfileId, authMerchantProfile } = useAuthService()

const presetAmountJson = reactive<PresetAmountJson>(getDefaultAmountJson())

const PercentObjectItem = { name: "(%)", value: "percent" }

const selectedDiscount = ref(PercentObjectItem)
const discountTypes = computed(() =>
  currencySelected.value
    ? [
        { name: PercentObjectItem.name, value: PercentObjectItem.value },
        { name: currencySelected.value.currency, value: currencySelected.value.currency },
      ]
    : [{ name: PercentObjectItem.name, value: PercentObjectItem.value }]
)

const selectedOtherFee = ref(PercentObjectItem)
const otherFeeTypes = computed(() =>
  currencySelected.value
    ? [
        { name: PercentObjectItem.name, value: PercentObjectItem.value },
        { name: currencySelected.value.currency, value: currencySelected.value.currency },
      ]
    : [{ name: PercentObjectItem.name, value: PercentObjectItem.value }]
)

const paymentAmount_enable = ref<boolean>(false)
const paymentAmount_amount = ref<number>()
const paymentAmount_currency = ref<string>()
const paymentAmount_totalAmount = ref<number>()
const paymentAmount_exchangeRate = ref<number>()
const paymentAmount_paymentAmount = ref<number>()
const paymentAmount_paymentCurrency = ref<string>()

const paymentAmount_enableDiscount = ref<boolean>(false)
const paymentAmount_discount = ref<number>()
const paymentAmount_discountIsPercent = ref<boolean>()
const paymentAmount_discountValue = ref<number>()

const paymentAmount_enableTax = ref<boolean>(false)
const paymentAmount_tax = ref<number>()
const paymentAmount_taxValue = ref<number>()

const paymentAmount_enableOtherFee = ref<boolean>(false)
const paymentAmount_otherFee = ref<number>()
const paymentAmount_otherFeeLabel = ref<string>()
const paymentAmount_otherFeeIsPercent = ref<boolean>()
const paymentAmount_otherFeeValue = ref<number>()

const { t } = useI18n()

const emit = defineEmits(["closePopupAddAmount", "addPaymentAmount"])

const currencyOptions = ref<CurrencyExchangeRate[]>([])
const currencySelected = ref<CurrencyExchangeRate>()

const merchantProfile = await selectMerchantProfileTask.perform({
  merchantId: authMerchantProfileId,
})

const onShow = () => {
  const currency = props.propCurrency ? props.propCurrency : merchantProfile.defaultCurrency
  const paymentCurrency = props.propPaymentCurrency ? props.propPaymentCurrency : merchantProfile.defaultCurrency

  //#region build currencyOptions + currencySelected
  if (merchantProfile?.defaultCurrency === CURERENCY_USD) {
    const newObj = {
      currency: CURERENCY_USD,
      exchangeRate: 1,
      imgICon: new URL(`/src/assets/icons/flags/${CURERENCY_USD}.svg`, import.meta.url).href,
    } as CurrencyExchangeRate

    currencySelected.value = newObj

    currencyOptions.value.push(newObj)
  } else if (props.propCurrencyConfig && props.propCurrencyConfig.length > 0) {
    const newArr = props.propCurrencyConfig.map((element) => {
      return {
        currency: element.currency,
        exchangeRate: parseFloat(element.exchangeRate.toString()),
        imgICon: new URL(`/src/assets/icons/flags/${element.currency}.svg`, import.meta.url).href,
      } as CurrencyExchangeRate
    })

    currencySelected.value = newArr.find((item) => item.currency === currency)
    if (!currencySelected.value) {
      currencySelected.value = newArr[0]
    }

    currencyOptions.value = newArr
  } else {
    const newObj = {
      currency: authMerchantProfile?.defaultCurrency,
      exchangeRate: 1,
      imgICon: new URL(`/src/assets/icons/flags/${authMerchantProfile?.defaultCurrency}.svg`, import.meta.url).href,
    } as CurrencyExchangeRate

    currencySelected.value = newObj

    currencyOptions.value.push(newObj)
  }
  //#endregion

  //#region build selected discountIsPercent+otherFeeIsPercent
  if (!presetAmountJson.discountIsPercent) {
    selectedDiscount.value = { name: currencySelected.value.currency, value: currencySelected.value.currency }
  }
  if (!presetAmountJson.otherFeeIsPercent) {
    selectedOtherFee.value = { name: currencySelected.value.currency, value: currencySelected.value.currency }
  }
  //#endregion

  if (!props.isAdd) {
    // set edit data
    Object.assign(presetAmountJson, getDefaultAmountJson(true))
    // Thực hiện tính toán lại ở watch
  }

  //#region Xử lý giá trị các data ko nằm trong presetAmountJson và ko được tính toán + lưu trữ lại
  paymentAmount_currency.value = currency
  paymentAmount_paymentCurrency.value = paymentCurrency
  //paymentAmount_currency.value = currencySelected.value.currency;
  //paymentAmount_exchangeRate.value = currencySelected.value.exchangeRate;
  //#endregion
}

watch(currencySelected, async () => {
  if (!currencySelected.value) return

  if (selectedDiscount.value.value !== PercentObjectItem.value)
    selectedDiscount.value = { name: currencySelected.value.currency, value: currencySelected.value.currency }
  if (selectedOtherFee.value.value !== PercentObjectItem.value)
    selectedOtherFee.value = { name: currencySelected.value.currency, value: currencySelected.value.currency }
})

watch(selectedDiscount, () => {
  if (selectedDiscount.value.value === PercentObjectItem.value) presetAmountJson.discountIsPercent = true
  else presetAmountJson.discountIsPercent = false
})

watch(selectedOtherFee, () => {
  if (selectedOtherFee.value.value === PercentObjectItem.value) presetAmountJson.otherFeeIsPercent = true
  else presetAmountJson.otherFeeIsPercent = false
})

const toggleDiscount = () => {
  presetAmountJson.enableDiscount = !presetAmountJson.enableDiscount
  if (!presetAmountJson.enableDiscount) presetAmountJson.discount = 0
}

const toggleTax = () => {
  presetAmountJson.enableTax = !presetAmountJson.enableTax
  if (!presetAmountJson.enableTax) presetAmountJson.tax = 0
}

const toggleOther = () => {
  presetAmountJson.enableOtherFee = !presetAmountJson.enableOtherFee
  if (!presetAmountJson.enableOtherFee) presetAmountJson.otherFee = 0
}

const clickAddAmount = () => {
  // 2025-09-01: otherFeeLabel nếu để trống thì hiển thị mặc định. ko lưu thẳng vào db thế này.
  //if (!presetAmountJson.otherFeeLabel)
  //  presetAmountJson.otherFeeLabel = t("components-paymentLink-AddPaymentAmount.other-fee")

  if (presetAmountJson.discount === 0) {
    presetAmountJson.enableDiscount = false
    presetAmountJson.discount = null
  }

  if (presetAmountJson.tax === 0) {
    presetAmountJson.enableTax = false
    presetAmountJson.tax = null
  }

  if (presetAmountJson.otherFee === 0) {
    presetAmountJson.enableOtherFee = false
    presetAmountJson.otherFee = null
    presetAmountJson.otherFeeLabel = ""
  }

  emit("addPaymentAmount", {
    presetAmountJson: presetAmountJson,

    currency: paymentAmount_currency.value,
    exchangeRate: paymentAmount_exchangeRate.value,
    paymentAmount: paymentAmount_paymentAmount.value,
    totalAmount: paymentAmount_totalAmount.value,
    discounted: paymentAmount_discountValue.value,
    taxed: paymentAmount_taxValue.value,
    otherFeed: paymentAmount_otherFeeValue.value,
    hasPresetAmount: true,
  })
}

const clickCloseAmount = () => {
  emit("closePopupAddAmount")
}

watch(
  [
    paymentAmount_amount,
    paymentAmount_enableDiscount,
    paymentAmount_discount,
    paymentAmount_discountIsPercent,
    paymentAmount_enableTax,
    paymentAmount_tax,
    paymentAmount_enableOtherFee,
    paymentAmount_otherFee,
    paymentAmount_otherFeeIsPercent,
    paymentAmount_otherFeeLabel,
  ],
  () => {
    presetAmountJson.amount = paymentAmount_amount.value ?? 0

    presetAmountJson.enableDiscount = paymentAmount_enableDiscount.value ?? false
    presetAmountJson.discount = paymentAmount_discount.value ?? null
    presetAmountJson.discountIsPercent = paymentAmount_discountIsPercent.value ?? false

    presetAmountJson.enableTax = paymentAmount_enableTax.value ?? false
    presetAmountJson.tax = paymentAmount_tax.value ?? null

    presetAmountJson.enableOtherFee = paymentAmount_enableOtherFee.value ?? false
    presetAmountJson.otherFee = paymentAmount_otherFee.value ?? null
    presetAmountJson.otherFeeIsPercent = paymentAmount_otherFeeIsPercent.value ?? false
    presetAmountJson.otherFeeLabel = paymentAmount_otherFeeLabel.value ?? ""
  }
)

watch(
  [presetAmountJson, currencySelected],
  ([presetAmountJson, currencySelected]) => {
    if (!presetAmountJson.amount) presetAmountJson.amount = 0
    if (!presetAmountJson.discount) presetAmountJson.discount = 0
    if (!presetAmountJson.tax) presetAmountJson.tax = 0
    if (!presetAmountJson.otherFee) presetAmountJson.otherFee = 0

    const currency = currencySelected?.currency
    const exchangeRate = currencySelected?.exchangeRate ?? 0
    const paymentCurrency = paymentAmount_paymentCurrency.value

    // WARN: Công thức sau được lặp lại tương ứng ở
    // - MerchantWeb: LinkDetail + AddPaymentAmount + LinkPaymentAmount
    // - ClientWeb: paymentLinkViewStore + paymentLinkViewResultStore
    const caculateRes = caculatePaymentAmount({
      presetAmountJson: presetAmountJson,
      otherInfo: {
        currency: currency,
        exchangeRate: exchangeRate,
        paymentCurrency: paymentCurrency,
      },
    })

    // Chú ý: riêng màn Add/Edit
    // Giá trị amount/totalAmount/paymentAmount được lấy từ tính toán
    paymentAmount_amount.value = caculateRes.amount
    paymentAmount_currency.value = currency
    paymentAmount_totalAmount.value = caculateRes.totalAmount
    paymentAmount_exchangeRate.value = exchangeRate
    paymentAmount_paymentAmount.value = caculateRes.paymentAmount
    paymentAmount_paymentCurrency.value = paymentCurrency

    paymentAmount_enableDiscount.value = caculateRes.enableDiscount
    paymentAmount_discount.value = caculateRes.discount
    paymentAmount_discountIsPercent.value = caculateRes.discountIsPercent
    paymentAmount_discountValue.value = caculateRes.discountResult

    paymentAmount_enableTax.value = caculateRes.enableTax
    paymentAmount_tax.value = caculateRes.tax
    paymentAmount_taxValue.value = caculateRes.taxResult

    paymentAmount_enableOtherFee.value = caculateRes.enableOtherFee
    paymentAmount_otherFeeLabel.value = caculateRes.otherFeeLabel
    paymentAmount_otherFee.value = caculateRes.otherFee
    paymentAmount_otherFeeIsPercent.value = caculateRes.otherFeeIsPercent
    paymentAmount_otherFeeValue.value = caculateRes.otherFeeResult
  },
  { deep: true }
)

const formatTextExchangeRate = (): string => {
  //return `1 ${paymentAmount_currency.value} = ${formatCurrency(paymentAmount_exchangeRate.value, paymentAmount_paymentCurrency.value)}`
  return formatExchangeRate(
    1,
    paymentAmount_currency.value,
    paymentAmount_exchangeRate.value,
    paymentAmount_paymentCurrency.value
  )
}

const disabledBtnConfirm = computed(() => {
  if (paymentAmount_paymentCurrency.value === "VND") {
    return (
      !paymentAmount_paymentAmount.value ||
      paymentAmount_paymentAmount.value < 0 ||
      paymentAmount_paymentAmount.value > APPCONFIG.MAX_PAYMENTAMOUNT_VND
    )
  } else {
    return (
      !paymentAmount_paymentAmount.value ||
      paymentAmount_paymentAmount.value < 0 ||
      paymentAmount_paymentAmount.value > APPCONFIG.MAX_PAYMENTAMOUNT_USD
    )
  }
})
</script>

<template>
  <Dialog :visible="visible" modal class="add-payment-amount" :draggable="false" @show="onShow">
    <template #header>
      <span class="title">{{ $t("components-paymentLink-AddPaymentAmount.title") }}</span>
      <button type="button" class="btn-close" @click="clickCloseAmount">
        <SvgIcon :src="PaymentLinkClose" />
      </button>
    </template>

    <div class="currency">
      <span class="p-dialog-content-label">{{ $t("components-paymentLink-AddPaymentAmount.currency") }}</span>

      <div v-if="currencyOptions && currencyOptions.length > 1" class="currency-icon">
        <Dropdown
          v-model="currencySelected"
          :options="currencyOptions"
          placeholder="Select currency"
          class="dropdown-currency"
          panel-class="dropdown-currency-panel"
        >
          <template #value="slotProps">
            <div v-if="slotProps.value" class="currency-selected">
              <img :src="slotProps.value.imgICon" style="width: 20px !important" />
              <div>{{ slotProps.value.currency }}</div>
            </div>

            <span v-else>
              {{ slotProps.placeholder }}
            </span>
          </template>

          <template #option="slotProps">
            <div class="currency-options">
              <img :src="slotProps.option.imgICon" style="width: 20px !important" />
              <div class="lable-currency">{{ slotProps.option.currency }}</div>
            </div>
          </template>
        </Dropdown>
      </div>

      <div v-else-if="currencyOptions && currencyOptions.length <= 1" class="lable-currency-icon">
        <img :src="currencySelected?.imgICon" style="width: 20px !important" />
        <span>{{ currencySelected?.currency.trim() }}</span>
      </div>
    </div>

    <div class="amount">
      <span class="p-dialog-content-label">{{ $t("components-paymentLink-AddPaymentAmount.amount") }}</span>
      <IconField class="amount-input-wrapper" icon-position="right">
        <!-- <InputNumberNew
          v-if="currency !== 'VND'"
          v-model="presetAmountJson.amount" locale="en-US"
          :is-integer="false"
          :currency="currencySelected?.currency"
        /> -->
        <!-- <InputNumberNew
          v-else
          v-model="presetAmountJson.amount" locale="en-US"
          :is-integer="true"
          :currency="currencySelected?.currency"
        /> -->
        <InputAmount
          v-model:model-value="presetAmountJson.amount"
          name="paymentAmount-amount"
          class="text-right pr-5"
          mode="currency"
          :currency="currencySelected?.currency"
        />
        <InputIcon style="color: inherit">{{ currencySelected?.currency }}</InputIcon>
        <!-- <InputText v-model="currency" readonly /> -->
      </IconField>
    </div>

    <div class="discount">
      <span class="p-dialog-content-label">{{ $t("components-paymentLink-AddPaymentAmount.discount") }}</span>
      <a v-if="!presetAmountJson.enableDiscount" href="javascript:void(0)" @click="toggleDiscount">
        {{ $t("components-paymentLink-AddPaymentAmount.btn-add") }}
      </a>
      <div v-if="presetAmountJson.enableDiscount" class="group-discount">
        <InputGroup>
          <!-- <InputNumberNew
            v-if="presetAmountJson.discountIsPercent"
            v-model="presetAmountJson.discount"
            locale="en-US"
            :is-integer="false"
            :is-percent="true"
            :min-fraction-digits="0"
            :max-fraction-digits="0"
          /> -->

          <!-- <InputNumberNew
            v-else-if="selectedDiscount.value !== 'VND'"
            v-model="presetAmountJson.discount"
            locale="en-US"
            :is-integer="false"
            :is-percent="false"
          /> -->

          <!-- <InputNumberNew
            v-else v-model="presetAmountJson.discount"
            locale="en-US"
            :is-integer="true"
            :is-percent="false"
          /> -->
          <InputAmount
            v-model:model-value="presetAmountJson.discount"
            name="paymentAmount-discount"
            class="text-right"
            :mode="presetAmountJson.discountIsPercent ? 'float' : 'currency'"
            :currency="currencySelected?.currency"
          />

          <Dropdown v-model="selectedDiscount" :options="discountTypes" option-label="name">
            <template #dropdownicon>
              <SvgIcon :src="ArrowDown" />
            </template>
          </Dropdown>
        </InputGroup>
        <span class="ml-2 text-center clickable" style="min-width: 20px" @click="toggleDiscount">
          <SvgIcon :src="CLoseIcon" />
        </span>
      </div>
    </div>

    <div v-if="paymentAmount_enableDiscount && paymentAmount_discountValue != undefined" class="label-discount">
      {{ "-" + formatCurrency(paymentAmount_discountValue, paymentAmount_currency) }}
    </div>

    <div class="tax">
      <span class="p-dialog-content-label">{{ $t("components-paymentLink-AddPaymentAmount.tax") }}</span>
      <a v-if="!presetAmountJson.enableTax" href="javascript:void(0)" @click="toggleTax">
        {{ $t("components-paymentLink-AddPaymentAmount.btn-add") }}
      </a>
      <div v-if="presetAmountJson.enableTax" class="group-tax">
        <IconField class="tax-input-wrapper" icon-position="right">
          <!-- <InputNumberNew
            v-model="presetAmountJson.tax"
            locale="en-US"
            :is-integer="false"
            :is-percent="true"
            :min-fraction-digits="0"
            :max-fraction-digits="0"
          /> -->
          <InputAmount
            v-model:model-value="presetAmountJson.tax"
            name="paymentAmount-tax"
            class="text-right"
            style="padding-right: 2rem"
            mode="float"
          />
          <InputIcon style="color: inherit">{{ t("common.percentSymbol") }}</InputIcon>
        </IconField>
        <!-- <IconField class="amount-input-wrapper" icon-position="right"></IconField> -->
        <span class="ml-2 text-center clickable" style="min-width: 20px" @click="toggleTax">
          <SvgIcon :src="CLoseIcon" />
        </span>
      </div>
    </div>

    <div v-if="paymentAmount_enableTax && paymentAmount_taxValue != undefined" class="label-tax">
      {{ formatCurrency(paymentAmount_taxValue, paymentAmount_currency) }}
    </div>

    <div class="other-fee">
      <span v-if="!presetAmountJson.enableOtherFee" class="p-dialog-content-label">
        {{ $t("components-paymentLink-AddPaymentAmount.other-fee") }}
      </span>
      <InputText
        v-if="presetAmountJson.enableOtherFee"
        v-model="presetAmountJson.otherFeeLabel"
        class="text-other-fee"
        :maxlength="200"
        @focusout="() => (presetAmountJson.otherFeeLabel = presetAmountJson.otherFeeLabel.trim())"
      />
      <a v-if="!presetAmountJson.enableOtherFee" href="javascript:void(0)" @click="toggleOther">
        {{ $t("components-paymentLink-AddPaymentAmount.btn-add") }}
      </a>
      <div v-if="presetAmountJson.enableOtherFee" class="group-other">
        <InputGroup>
          <!-- <InputNumberNew
            v-if="presetAmountJson.otherFeeIsPercent"
            v-model="presetAmountJson.otherFee"
            locale="en-US"
            :is-integer="false"
            :is-percent="true"
            :min-fraction-digits="0"
            :max-fraction-digits="0"
          /> -->

          <!-- <InputNumberNew
            v-else-if="selectedOtherFee.value !== 'VND'"
            v-model="presetAmountJson.otherFee"
            locale="en-US"
            :is-integer="false"
            :is-percent="false"
          /> -->

          <!-- <InputNumberNew
            v-else v-model="presetAmountJson.otherFee"
            locale="en-US"
            :is-integer="true"
            :is-percent="false"
          /> -->

          <InputAmount
            v-model:model-value="presetAmountJson.otherFee"
            name="paymentAmount-otherFee"
            class="text-right"
            :mode="presetAmountJson.otherFeeIsPercent ? 'float' : 'currency'"
            :currency="currencySelected?.currency"
          />

          <Dropdown v-model="selectedOtherFee" :options="otherFeeTypes" option-label="name">
            <template #dropdownicon>
              <span class="text-center" style="min-width: 20px"><SvgIcon :src="ArrowDown" /></span>
            </template>
          </Dropdown>
        </InputGroup>
        <span class="ml-2 text-center clickable" style="min-width: 20px" @click="toggleOther">
          <SvgIcon :src="CLoseIcon" />
        </span>
      </div>
    </div>

    <div v-if="paymentAmount_enableOtherFee && paymentAmount_otherFeeValue != undefined" class="label-other-fee">
      {{ formatCurrency(paymentAmount_otherFeeValue, paymentAmount_currency) }}
    </div>

    <div v-if="paymentAmount_currency !== paymentAmount_paymentCurrency" class="total-amount">
      <hr class="divider-addPayment" />

      <div class="total-amount-other-vnd">
        <span class="p-dialog-content-label">{{ $t("components-paymentLink-AddPaymentAmount.total") }}</span>
        <span class="total-convert">
          {{ formatCurrency(paymentAmount_totalAmount, paymentAmount_currency) }}
        </span>
      </div>
      <div v-tooltip="$t('components-paymentLink-AddPaymentAmount.tooltip')" class="convert-money">
        <span class="converted">{{ formatTextExchangeRate() }}</span>
        <SvgIcon :src="ToolTipIcon" />
      </div>
    </div>

    <hr class="divider-addPayment" />

    <div class="total-payment-amount">
      <span class="p-dialog-content-label">{{ $t("components-paymentLink-AddPaymentAmount.payment") }}</span>
      <span class="total-amount">
        {{ formatCurrency(paymentAmount_paymentAmount, paymentAmount_paymentCurrency) }}
      </span>
    </div>

    <div
      v-if="
        paymentAmount_paymentAmount &&
        paymentAmount_paymentAmount > APPCONFIG.MAX_PAYMENTAMOUNT_VND &&
        paymentAmount_paymentCurrency == 'VND'
      "
      class="warnning-maximun-amount"
    >
      {{
        $t("components-paymentLink-AddPaymentAmount.warnning-maximun-amount-value", {
          maxAmount: formatCurrency(APPCONFIG.MAX_PAYMENTAMOUNT_VND, "VND"),
        })
      }}
    </div>

    <div
      v-else-if="paymentAmount_paymentAmount && paymentAmount_paymentAmount > APPCONFIG.MAX_PAYMENTAMOUNT_USD"
      class="warnning-maximun-amount"
    >
      {{
        $t("components-paymentLink-AddPaymentAmount.warnning-maximun-amount-value", {
          maxAmount: formatCurrency(APPCONFIG.MAX_PAYMENTAMOUNT_USD, paymentAmount_paymentCurrency),
        })
      }}
    </div>

    <template #footer>
      <Button
        :label="$t('components-paymentLink-AddPaymentAmount.btn-cancel')"
        class="btn-cancel"
        @click="clickCloseAmount"
      />
      <Button
        :label="$t('components-paymentLink-AddPaymentAmount.btn-confirm')"
        :class="{
          'btn-confirm-disabled': disabledBtnConfirm,
        }"
        class="btn-confirm"
        @click="clickAddAmount"
      />
    </template>
  </Dialog>
</template>

<style lang="scss">
.add-payment-amount {
  border-radius: 12px;
  padding: 20px 24px;
  min-width: 520px;

  .p-dialog-header {
    margin-bottom: 22px;
    padding: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .p-dialog-header-icons {
      display: none;
    }

    .title {
      font-size: 24px;
      font-weight: 600;
      line-height: 36px;
      color: #1c1c1c;
    }

    .btn-close {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      padding: 8px;
    }
  }

  .p-dialog-content {
    padding: 0;
    margin-top: 8px;
    margin-bottom: 8px;

    &-label {
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
    }

    a {
      text-decoration: none;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      color: #2e6be5;
    }

    hr.divider-addPayment {
      background: #dcdcdc;
    }

    .total-amount {
      .total-amount-other-vnd {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .total-convert {
          font-size: 16px;
          font-weight: 600;
          line-height: 24px;
        }
      }

      .convert-money {
        font-size: 12px;
        font-weight: 400;
        line-height: 16px;
        color: #6c6c6c;
        display: flex;
        justify-content: flex-end;

        .converted {
          margin-right: 8px;
        }
      }
    }

    .label-discount,
    .label-tax,
    .label-other-fee {
      color: #6c6c6c;
      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
      text-align: right;
      margin-top: 4px;
      margin-right: 28px; // 20px of x button + 8px margin
    }

    .currency {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .currency-icon {
        display: flex;
        align-items: center;

        span {
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
        }
      }

      .lable-currency-icon {
        min-width: 59px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        span {
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
        }
      }
    }

    .amount {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16px;

      .amount-input-wrapper {
        width: 242px;

        .p-input-icon {
          line-height: 1.25rem;
          margin-top: -0.625rem; // =1/2 of line-height
        }
      }

      .p-inputgroup {
        width: 242px;

        .p-inputnumber {
          width: 211px;

          .p-inputtext {
            text-align: right;
            padding-right: 0;
            border-right: none;

            &:focus {
              outline: none;
            }
          }
        }

        .p-inputtext-tms {
          width: 51px;
          padding-left: 4px;

          &:focus {
            outline: none;
          }
        }
      }
    }

    .discount {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16px;

      .group-discount {
        width: 242px;
        display: flex;
        align-items: center;

        .p-inputgroup {
          height: 40px;

          .p-inputnumber {
            min-width: 129px;

            .p-inputtext {
              text-align: right;
            }
          }

          .p-dropdown {
            border: 1px solid var(--border-primary, #dcdcdc);
            border-left: none;
            display: flex;
            align-items: center;

            span.p-inputtext {
              outline: none;
              padding: 10px 0 10px 16px;
            }

            .p-dropdown-trigger {
              width: 20px;
              height: 20px;
              margin-right: 16px;
            }
          }
        }
      }
    }

    .tax {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16px;

      .group-tax {
        width: 242px;
        display: flex;
        align-items: center;

        .p-inputgroup {
          text-align: right;

          .p-inputnumber {
            width: 175px;

            .p-inputtext {
              text-align: right;
              padding-right: 0;
              border-right: none;

              &:focus {
                outline: none;
              }
            }
          }

          .p-inputtext-tms {
            width: 17px;
            padding-left: 4px;

            &:focus {
              outline: none;
            }
          }
        }
      }
    }

    .other-fee {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16px;

      .text-other-fee {
        width: 210px;
        margin-right: 20px;
      }

      .group-other {
        width: 242px;
        display: flex;
        align-items: center;

        .p-inputgroup {
          height: 40px;

          .p-inputnumber {
            min-width: 129px;

            .p-inputtext {
              text-align: right;
            }
          }

          .p-dropdown {
            border: 1px solid var(--border-primary, #dcdcdc);
            border-left: none;
            display: flex;
            align-items: center;

            span.p-inputtext {
              outline: none;
              padding: 10px 0 10px 16px;
            }

            .p-dropdown-trigger {
              width: 20px;
              height: 20px;
              margin-right: 16px;
            }
          }
        }
      }
    }

    .total-payment-amount {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16px;

      .total-amount {
        font-size: 20px;
        font-weight: 600;
        line-height: 28px;
        color: #1c1c1c;
      }
    }
  }

  .p-dialog-footer {
    margin-top: 20px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .btn-cancel {
      min-width: 100px;
      height: 40px;
      border-radius: 4px;
      padding: 10px 24px;
      color: #404040;
      margin-right: 20px;
      border: none;
      background-color: #f5f5f5;
    }

    .btn-confirm {
      min-width: 120px;
      height: 40px;
      border-radius: 4px;
      padding: 10px 24px;
      color: #ffffff;
      border: none;
      background-color: #2e6be5;
    }

    .p-button-label {
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
    }
  }
}

.warnning-maximun-amount {
  text-align: right;
  color: #d82039;
}

.btn-confirm-disabled {
  pointer-events: none;
  background-color: #e6e6e6 !important;
  color: #bababa !important;
}

.dropdown-currency {
  min-width: 130px;
  padding: 10px 16px;
  border-radius: 4px;
  height: 44px;

  .p-dropdown-label {
    padding: 0;

    .currency-selected {
      display: flex;
      justify-content: space-between;
      width: 58px;
    }
  }

  .p-dropdown-trigger {
    margin: 0;
    width: 24px;
    height: 24px;

    svg {
      margin-right: 0 !important;
    }
  }
}

.dropdown-currency-panel {
  .currency-options {
    display: flex;
    justify-content: space-around;

    .lable-currency {
      margin-left: 8px;
    }
  }
}
</style>
