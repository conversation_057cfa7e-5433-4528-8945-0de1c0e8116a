<script setup lang="ts">
import Delete from "@/assets/icons/payment-link-delete-amount-limit.svg"
import Edit from "@/assets/icons/payment-link-edit-amount-limit.svg"
import InfoIcon from "@assets/icons/info.svg"

import APPCONFIG from "@/appConfig"
import { useAuthService } from "@/services/authService"
import { usePaymentLinkService } from "@/services/paymentLinkService"
import { caculatePaymentAmount } from "@/services/paymentLinkService.utils"
import type { CurrencyConfig, PaymentLink } from "@/types/apis"
import { formatCurrency, formatExchangeRate } from "@/utils/formatUtil"
import { ref } from "vue"

const { getCurrencyConfigTask } = usePaymentLinkService()
const { authMerchantProfileId } = useAuthService()
const currencyConfig: CurrencyConfig[] = await getCurrencyConfigTask.perform(authMerchantProfileId).then((item) => {
  const arrValue: CurrencyConfig[] = []

  item.forEach((element) => {
    if (element.enabled) arrValue.push(element)
  })

  return arrValue
})

const discounted = ref<number>()
const taxed = ref<number>()
const otherFeed = ref<number>()

const model = defineModel<PaymentLink>({ required: true })
const [isEditModel, isEditModelModifiers] = defineModel<boolean>("isEdit", {
  default: false,
})

if (isEditModel.value) {
  if (model.value.presetAmountJson) {
    if (!model.value.presetAmountJson.amount) model.value.presetAmountJson.amount = 0
    if (!model.value.presetAmountJson.discount) model.value.presetAmountJson.discount = 0
    if (!model.value.presetAmountJson.tax) model.value.presetAmountJson.tax = 0
    if (!model.value.presetAmountJson.otherFee) model.value.presetAmountJson.otherFee = 0

    // WARN: Công thức sau được lặp lại tương ứng ở
    // - MerchantWeb: LinkDetail + AddPaymentAmount + LinkPaymentAmount
    // - ClientWeb: paymentLinkViewStore + paymentLinkViewResultStore
    const caculateRes = caculatePaymentAmount({
      presetAmountJson: model.value.presetAmountJson,
      otherInfo: {
        currency: model.value.currency,
        exchangeRate: model.value.exchangeRate,
        paymentCurrency: model.value.paymentCurrency,
      },
    })

    // Chú ý: riêng màn Add/Edit Panel display
    // Giá trị amount/totalAmount/paymentAmount tôi để nguyên code của Nhật do lười ko đồng nhất code với 2 màn còn lại.
    discounted.value = caculateRes.discount
    taxed.value = caculateRes.tax
    otherFeed.value = caculateRes.otherFee
  }
}

const paymentAmount = ref(false)
const isAddPayment = ref(false)

const openPaymentAmount = (isAdd: boolean) => {
  paymentAmount.value = true
  isAddPayment.value = isAdd

  if (isAddPayment.value) {
    model.value.presetAmountJson = undefined
    discounted.value = 0
    taxed.value = 0
    otherFeed.value = 0
    model.value.paymentAmount = 0
    model.value.totalAmount = 0
  }
}

const addPaymentAmount = (emitObject: any) => {
  model.value.presetAmountJson = emitObject.presetAmountJson
  model.value.currency = emitObject.currency
  model.value.exchangeRate = emitObject.exchangeRate
  model.value.paymentAmount = emitObject.paymentAmount
  model.value.totalAmount = emitObject.totalAmount
  discounted.value = emitObject.discounted
  taxed.value = emitObject.taxed
  otherFeed.value = emitObject.otherFeed
  model.value.hasPresetAmount = emitObject.hasPresetAmount

  paymentAmount.value = false
}

const closePaymentAmount = () => {
  paymentAmount.value = false
}

const resetAmount = () => {
  model.value.hasPresetAmount = false
}
</script>

<template>
  <div
    v-tooltip.left="{ value: 'Payment amount is not allowed to edit', disabled: !isEdit }"
    class="card payment-amount"
    :class="{ 'disible-card': isEdit }"
  >
    <div v-if="!model.hasPresetAmount" class="payment-amount-header">
      <div class="label">
        <span class="label-header">{{ $t("components-paymentLink-LinkPaymentAmount.title") }}</span>
        <span class="label-content">{{ $t("components-paymentLink-LinkPaymentAmount.content") }}</span>
      </div>

      <div class="action">
        <a href="javascript:void(0)" class="action-add-payment-amount" @click="openPaymentAmount(true)">
          {{ $t("components-paymentLink-LinkPaymentAmount.btn-add") }}
        </a>
      </div>
    </div>

    <div v-else class="payment-amount-content">
      <div class="payment-amount-content-header">
        <div class="label">
          <span class="label-header">{{ $t("components-paymentLink-LinkPaymentAmount.title") }}</span>
        </div>

        <div class="action">
          <a href="javascript:void(0)" class="action-edit-amount-number" @click="openPaymentAmount(false)">
            <SvgIcon :src="Edit" />
          </a>
          <a href="javascript:void(0)" class="action-delete-amount-number" @click="resetAmount">
            <SvgIcon :src="Delete" />
          </a>
        </div>
      </div>

      <div class="payment-amount-content-content">
        <div class="amount">
          <span>{{ $t("components-paymentLink-LinkPaymentAmount.amount") }}</span>
          <span>{{ formatCurrency(model.presetAmountJson?.amount, model.currency) }}</span>
        </div>

        <div v-if="model.presetAmountJson?.enableDiscount" class="discount">
          <div>
            {{ $t("components-paymentLink-LinkPaymentAmount.discount") }}
            {{ model.presetAmountJson.discountIsPercent ? `(${model.presetAmountJson.discount}%)` : " " }}
          </div>
          <span>{{ "-" + formatCurrency(discounted, model.currency) }}</span>
        </div>

        <div v-if="model.presetAmountJson?.enableTax" class="tax">
          <div>
            {{ $t("components-paymentLink-LinkPaymentAmount.tax") }}
            {{ `(${model.presetAmountJson.tax}%)` }}
          </div>
          <span>{{ formatCurrency(taxed, model.currency) }}</span>
        </div>

        <div v-if="model.presetAmountJson?.enableOtherFee" class="other-free">
          <div
            v-tooltip.bottom="model.presetAmountJson?.otherFeeLabel"
            class="text-truncate"
            style="width: 150px"
          >
            {{ model.presetAmountJson?.otherFeeLabel || $t("components-paymentLink-AddPaymentAmount.other-fee") }}
            {{ model.presetAmountJson.otherFeeIsPercent ? `(${model.presetAmountJson.otherFee}%)` : " " }}
          </div>
          <span>{{ formatCurrency(otherFeed, model.currency) }}</span>
        </div>
      </div>

      <hr v-if="model.currency !== model.paymentCurrency" style="margin-bottom: 16px !important; opacity: 1" />

      <template v-if="model.currency != model.paymentCurrency">
        <div class="info-group" style="margin-bottom: 16px !important">
          <div class="info-group-row">
            <div class="info-title">{{ $t("components-paymentLink-LinkPaymentAmount.total-amount") }}</div>
            <div class="info-detail info-detail-paymentamount">
              {{ formatCurrency(model.totalAmount, model.currency) }}
            </div>
          </div>
          <div class="info-group-row">
            <div class="info-detail info-detail-paymentamount-exchange">
              <span
                v-tooltip.bottom="{
                  escape: false,
                  autoHide: false,
                  value: $htmlSafe($t('common.tooltip.exchange-rate', { url: APPCONFIG.APP_LINK_EXCHANGERATECONFIG })),
                }"
              >
                {{
                  //`1 ${model.currency} = ${formatCurrency(model.exchangeRate, model.paymentCurrency)}`
                  formatExchangeRate(1, model.currency, model.exchangeRate, model.paymentCurrency)
                }}
                <SvgIcon :src="InfoIcon" />
              </span>
            </div>
          </div>
        </div>
      </template>

      <hr style="opacity: 1" />

      <div class="payment-amount-content-footer">
        <span>{{ $t("components-paymentLink-LinkPaymentAmount.title") }}</span>
        <span class="total-amount-label">{{ formatCurrency(model.paymentAmount, model.paymentCurrency) }}</span>
      </div>
    </div>
  </div>

  <AddPaymentAmount
    v-if="paymentAmount"
    :visible="paymentAmount"
    :prop-currency="model.currency"
    :prop-currency-config="currencyConfig"
    :is-add="isAddPayment"
    :prop-preset-amount-json="model.presetAmountJson"
    :prop-discounted="discounted"
    :prop-taxed="taxed"
    :prop-other-feed="otherFeed"
    :prop-total-amount="model.totalAmount"
    :prop-payment-amount="model.paymentAmount"
    :prop-payment-currency="model.paymentCurrency"
    @close-popup-add-amount="closePaymentAmount"
    @add-payment-amount="addPaymentAmount"
  />
</template>

<style lang="scss">
.payment-amount {
  padding: 16px 24px;
  border-radius: 8px;
  max-width: 384px;
  min-height: 92px;
  border: none;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .label {
      margin-right: 20px;

      &-header {
        display: block;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        color: #1c1c1c;
      }

      &-content {
        font-size: 12px;
        font-weight: 400;
        line-height: 16px;
        color: #6c6c6c;
        width: 227px;
        display: block;
        margin-top: 4px;
      }
    }

    .action {
      &-add-payment-amount {
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        color: #2e6be5;
      }
    }
  }

  &-content {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: #404040;

    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .label {
        margin-right: 20px;

        &-header {
          display: block;
          font-size: 16px;
          font-weight: 600;
          line-height: 24px;
          color: #1c1c1c;
        }
      }

      .action {
        &-edit-amount-number {
          margin-right: 20px;
        }
      }
    }

    &-content {
      padding-top: 16px;

      .amount {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      }

      .discount {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      }

      .tax {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      }

      .other-free {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }

    &-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16px;

      .total-amount-label {
        font-size: 20px;
        font-weight: 600;
        line-height: 28px;
        color: #1c1c1c;

        text-align: right;
        white-space: nowrap;
      }
    }
  }
}

.disible-card {
  opacity: 50%;

  .action {
    &-edit-amount-number {
      pointer-events: none;
      cursor: default;
    }

    &-delete-amount-number {
      pointer-events: none;
      cursor: default;
    }

    &-add-payment-amount {
      pointer-events: none;
      cursor: default;
    }
  }
}

.info-group {
  font-size: var(--Typeface-size-md, 0.875rem);
  font-weight: 400;
  line-height: 1.25rem;
  /* 142.857% */
  color: #404040;

  display: flex;
  gap: 0.25rem;
  align-items: start;
  justify-items: center;
  flex-direction: column;

  .info-detail-createupdatedate {
    color: #6c6c6c;
  }

  .info-detail-user {
    color: #404040;
    font-weight: 500;
  }

  .info-detail-paymentamount {
    font-size: var(--Typeface-size-xl, 1.25rem);
    font-weight: 600;
    line-height: 1.75rem;
    /* 140% */
    color: #1c1c1c;
    white-space: nowrap;
  }

  .info-detail-paymentamount-exchange {
    font-size: var(--Typeface-size-sm, 0.75rem);
    font-style: normal;
    font-weight: 400;
    line-height: 1rem;
    /* 133.333% */

    span {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }
  }
}

.info-group-row {
  width: 100%;
  display: flex;
  flex-direction: row;

  .info-title {
    align-self: center;
  }

  .info-detail {
    flex-grow: 1;
    text-align: right;
  }
}
</style>
