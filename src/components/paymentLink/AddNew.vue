<script setup lang="ts">
import ArrowDown from "@/assets/icons/payment-link-add-edit-arrow-down.svg"

import { EnumPaymentLinkStatus, EnumPaymentLinkType } from "@/app.const"
import { AppRouteNames } from "@/enums/routers"
import { EventCommandEnum, usePaymentLinkEventBus } from "@/events/eventBus"
import router from "@/routers/router"
import { useAuthService } from "@/services/authService"
import { usePaymentLinkService } from "@/services/paymentLinkService"
import { useToastService } from "@/services/toastService"
import type {
  InstallmentSettings,
  OnePayCards,
  OneTimeLinkProp,
  PaymentLink,
  PaymentLinkRequest,
  PresetAmountJson,
} from "@/types/apis"
import { roundCurrency } from "@/utils/formatUtil"
import moment from "moment"
import OverlayPanel from "primevue/overlaypanel"
import { computed, ref, watch } from "vue"
import { useAsyncTask } from "vue-concurrency"
import { useI18n } from "vue-i18n"

const toastService = useToastService()
//const bus = useEventBus<string>("ADD_EDIT_PAYMENT_LINK_EVENT");
const eventBus = usePaymentLinkEventBus()
const i18n = useI18n()
const { addNewPaymentLinkTask, form, formValidator, clearFormValidator, getInstallmentsTask, resetAddEditFormData } =
  usePaymentLinkService()
const { authMerchantProfile } = useAuthService()

const presetAmountJson = ref<PresetAmountJson>({
  amount: null,
  enableDiscount: false,
  discount: null,
  discountIsPercent: false,
  enableTax: false,
  tax: null,
  enableOtherFee: false,
  otherFeeLabel: "",
  otherFee: null,
  otherFeeIsPercent: false,
})
const DefaultPaymentLink: PaymentLink = {
  id: "",
  merchantProfileId: "",
  routerUri: "",
  name: "",
  description: "",
  dueDate: undefined,
  paymentMethods: 0,
  installmentSettings: [],
  hasPresetAmount: false,
  presetAmountJson: presetAmountJson.value,
  totalAmount: 0,
  currency: "",
  exchangeRate: 0,
  paymentAmount: 0,
  paymentCurrency: authMerchantProfile?.defaultCurrency,
  hasLimitPayment: true,
  maxPaymentQty: 1,
  totalPaymentQty: 0,
  inprogressPayments: 0,
  infoFieldOptions: 0,
  customerNoteLckey: [],
  fileId: null,
  fileName: null,
  createDate: 0,
  createByUserId: "",
  lastModifyDate: 0,
  lastModifyByUserId: "",
  enabled: false,
  status: EnumPaymentLinkStatus.Active,
  linkType: EnumPaymentLinkType.OneTime,
}

const paymentLink = ref<PaymentLink>(DefaultPaymentLink)

const props = withDefaults(
  defineProps<{
    //visible: boolean,
    oneTimeProp?: OneTimeLinkProp
  }>(),
  {
    //visible: false,
    oneTimeProp: undefined,
  }
)

const visibleModel = defineModel<boolean>("visible", {
  default: false,
})

// binding data from create one time link with more options
if (props.oneTimeProp) {
  if (props.oneTimeProp.paymentCurrency) {
    paymentLink.value.paymentCurrency = props.oneTimeProp.paymentCurrency
  }

  if (props.oneTimeProp.currency) {
    paymentLink.value.currency = props.oneTimeProp.currency
  }

  if (props.oneTimeProp.totalAmount) {
    paymentLink.value.totalAmount = props.oneTimeProp.totalAmount
  }

  if (props.oneTimeProp.paymentAmount) {
    paymentLink.value.paymentAmount = props.oneTimeProp.paymentAmount
  }

  if (props.oneTimeProp.linkName) {
    paymentLink.value.name = props.oneTimeProp.linkName
  }

  if (props.oneTimeProp.routerUri) {
    paymentLink.value.routerUri = props.oneTimeProp.routerUri
  }

  if (props.oneTimeProp.description) {
    paymentLink.value.description = props.oneTimeProp.description
  }

  if (props.oneTimeProp.amount) {
    presetAmountJson.value.amount = props.oneTimeProp.amount
  }

  if (props.oneTimeProp.hasPresetAmount) {
    paymentLink.value.hasPresetAmount = props.oneTimeProp.hasPresetAmount
  }

  if (props.oneTimeProp.hasLimitPayment) {
    paymentLink.value.hasLimitPayment = props.oneTimeProp.hasLimitPayment
  }

  if (props.oneTimeProp.maxPaymentQty) {
    paymentLink.value.maxPaymentQty = props.oneTimeProp.maxPaymentQty
  }

  if (props.oneTimeProp.totalPaymentQty) {
    paymentLink.value.totalPaymentQty = props.oneTimeProp.totalPaymentQty
  }

  if (props.oneTimeProp.linkType) {
    paymentLink.value.linkType = props.oneTimeProp.linkType
  }
}

const emits = defineEmits(["closePopupAdd"])

const getAllTime = (cards: OnePayCards[]): number[] => {
  const result = ref<number[]>([])

  cards.forEach((element) => {
    element.times.forEach((el) => {
      if (result.value.indexOf(el.time) < 0) result.value.push(el.time)
    })
  })

  return result.value
}

const getInstallmentSetting = async () => {
  const result: InstallmentSettings[] = []

  const data = await getInstallmentsTask.perform({ merchantId: authMerchantProfile?.id })

  if (!data) {
    return
  }

  data.installments.map((element) => {
    result.push({ bankId: element.bank_id, times: getAllTime(element.cards) })
  })

  return result
}

const isDisabledSubmit = computed(
  () =>
    !paymentLink.value.name || !paymentLink.value.routerUri || (formValidator.errors && formValidator.errors.length > 0)
)

const submitTask = useAsyncTask(async (signal, input: void) => {
  if (
    (paymentLink.value.paymentMethods & 64) > 0 &&
    paymentLink.value.installmentSettings &&
    paymentLink.value.installmentSettings.length <= 0
  ) {
    const installmentSettings = await getInstallmentSetting()

    if (installmentSettings == null || installmentSettings.length == 0) {
      console.warn("installmentSettings", "cannot load any record")
      toastService.error({})
      return
    }

    paymentLink.value.installmentSettings = installmentSettings
  }

  const request = ref<PaymentLinkRequest>({
    routerUri: paymentLink.value.routerUri.trim(),
    name: paymentLink.value.name.trim(),
    description: paymentLink.value.description?.trim(),
    dueDate: paymentLink.value.dueDate ? moment(paymentLink.value.dueDate).toDate() : null,
    paymentMethods: paymentLink.value.paymentMethods,
    installmentSettings: JSON.stringify(paymentLink.value.installmentSettings),
    hasPresetAmount: paymentLink.value.hasPresetAmount,
    presetAmountJson: paymentLink.value.hasPresetAmount ? JSON.stringify(paymentLink.value.presetAmountJson) : null,
    totalAmount: roundCurrency(paymentLink.value.totalAmount, paymentLink.value.currency),
    currency: paymentLink.value.currency,
    exchangeRate: paymentLink.value.exchangeRate,
    paymentAmount: roundCurrency(paymentLink.value.paymentAmount, paymentLink.value.paymentCurrency),
    paymentCurrency: paymentLink.value.paymentCurrency,
    hasLimitPayment: paymentLink.value.hasLimitPayment,
    maxPaymentQty: paymentLink.value.maxPaymentQty,
    totalPaymentQty: paymentLink.value.totalPaymentQty,
    infoFieldOptions: paymentLink.value.infoFieldOptions,
    customerNoteLckey: paymentLink.value.customerNoteLckey,
    fileId: paymentLink.value.fileId,
    fileName: paymentLink.value.fileName,
    enable: true,
    linkType: paymentLink.value.linkType,
  } as PaymentLinkRequest)

  await formValidator.execute()
  const isValid = formValidator.isFinished && formValidator.pass
  if (!isValid) return

  const data = await addNewPaymentLinkTask.perform(request.value)

  if (data?.id) {
    emits("closePopupAdd")
    reloadNewPayment()
    resetAddEditFormData()
    //bus.emit(data?.id);

    await router.push({
      name: AppRouteNames.PAYMENTLINK_LIST,
    })

    eventBus.emit({
      command: EventCommandEnum.Created,
      data: {
        id: data.id,
      },
    })
  } else {
    toastService.error({}) // TODO: để tạm
  }
})
const submit = async () => await submitTask.perform()

const reloadNewPayment = () => {
  const tmp = paymentLink.value
  paymentLink.value = Object.assign(tmp, DefaultPaymentLink)
}

async function onSidebarShow() {
  reloadNewPayment()
  await resetAddEditFormData()
}

async function onHideSidebar() {
  emits("closePopupAdd")

  reloadNewPayment()
  await resetAddEditFormData()
  await clearFormValidator()

  await router.push({
    name: AppRouteNames.PAYMENTLINK_LIST,
  })
}

const linkTypeOptions = ref([
  {
    label: i18n.t("components-paymentLink-add.link-type-options.one-time.lable"),
    description: i18n.t("components-paymentLink-add.link-type-options.one-time.description"),
    value: EnumPaymentLinkType.OneTime,
    icon: new URL("/src/assets/icons/one-time-link.svg", import.meta.url).href,
    active: paymentLink.value.linkType === EnumPaymentLinkType.OneTime,
  },
  {
    label: i18n.t("components-paymentLink-add.link-type-options.multi.lable"),
    description: i18n.t("components-paymentLink-add.link-type-options.multi.description"),
    value: EnumPaymentLinkType.MultiUse,
    icon: new URL("/src/assets/icons/muti-use-link.svg", import.meta.url).href,
    active: paymentLink.value.linkType === EnumPaymentLinkType.MultiUse,
  },
])

const linkTypeSelected = ref(linkTypeOptions.value.find((item) => item.value === paymentLink.value.linkType))

watch(linkTypeSelected, (linkTypeSelected) => {
  if (!linkTypeSelected) return
  if (linkTypeSelected.value === EnumPaymentLinkType.OneTime) {
    paymentLink.value.hasLimitPayment = true
    paymentLink.value.maxPaymentQty = 1
    paymentLink.value.totalPaymentQty = 0
    paymentLink.value.linkType = EnumPaymentLinkType.OneTime
  } else if (linkTypeSelected.value === EnumPaymentLinkType.MultiUse) {
    paymentLink.value.hasLimitPayment = false
    paymentLink.value.maxPaymentQty = 0
    paymentLink.value.totalPaymentQty = 0
    paymentLink.value.linkType = EnumPaymentLinkType.MultiUse
  }

  console.log("paymentLink.value.hasLimitPayment", paymentLink.value.hasLimitPayment)
})

const openLinkType = ref<InstanceType<typeof OverlayPanel> | null>(null)

const openLinkTypes = (event: Event) => {
  openLinkType.value?.toggle(event)
}

const selectType = (item: any) => {
  item.active = true
  const resetActive = linkTypeOptions.value.find((e) => e.value !== item.value)
  if (resetActive) resetActive.active = false

  linkTypeSelected.value = item
  paymentLink.value.linkType = item.value

  openLinkType.value?.hide()
}

const isOpen = ref(false)

const showLinkType = () => {
  isOpen.value = true
}

const hideLinkType = () => {
  isOpen.value = false
}
</script>

<template>
  <AppSidebar
    v-model:visible="visibleModel"
    position="full"
    class="tms-page-payment-link-add"
    :loading="submitTask.isRunning"
    @show="onSidebarShow"
    @hide="onHideSidebar"
  >
    <template #header>
      <!-- <button class="btn-close" @click="close">
        <SvgIcon :src="PaymentLinkClose" />
      </button> -->
      <span class="title">{{ $t("components-paymentLink-add.title") }}</span>
      <Button
        class="btn-save"
        :loading="submitTask.isRunning"
        :class="{
          'btn-disabled': isDisabledSubmit,
        }"
        @click="submit"
      >
        {{ $t("components-paymentLink-add.btn-save") }}
      </Button>
    </template>

    <div class="container link-type" :class="{ 'is-open': isOpen }">
      <Button class="btn-link-type" @click="openLinkTypes">
        <div style="margin-right: 8px">
          <span class="lable">
            {{ $t("components-paymentLink-add.link-type") }}
          </span>

          <span class="link-type-selected">
            {{ linkTypeSelected?.label }}
          </span>
        </div>

        <SvgIcon :src="ArrowDown" class="icon-dropdown"></SvgIcon>
      </Button>

      <OverlayPanel ref="openLinkType" class="link-type-slecet-panel" @hide="hideLinkType" @show="showLinkType">
        <div
          v-for="(item, index) of linkTypeOptions"
          :key="index"
          class="link-type-option"
          :class="{ 'link-type-option-highlight': item.active }"
          @click="selectType(item)"
        >
          <div class="link-type-option-icon">
            <img :src="item.icon" />
          </div>

          <div class="link-type-option-lables">
            <div class="lable">
              {{ item.label }}
            </div>

            <div class="description">
              {{ item.description }}
            </div>
          </div>
        </div>
      </OverlayPanel>
    </div>

    <div class="container d-flex justify-content-between content-body">
      <div class="info-option">
        <LinkInfomation v-model="paymentLink" :is-edit="false" />

        <LinkOptions v-model="paymentLink" />
      </div>

      <div class="payment">
        <LinkPaymentAmount v-model="paymentLink" :is-edit="false" />

        <LinkLimitPayment v-model="paymentLink" />
      </div>
    </div>
  </AppSidebar>
</template>

<style lang="scss">
.tms-page-payment-link-add {
  .p-sidebar-header {
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e6e6e6;

    .btn-close {
      width: 48px;
      height: 48px;
      background-color: #f5f5f5;
    }

    .title {
      font-size: 24px;
      font-weight: 600;
      line-height: 36px;
      color: #1c1c1c;
    }

    .btn-save {
      min-width: 87px;
      height: 48px;
      background-color: #2e6be5;
      border: none;
      border-radius: 4px;
      padding: 12px 24px;
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;
      color: #fff;
    }
  }

  .p-sidebar-content {
    background-color: #f0f0f0;
    height: 100%;
    padding: 0;

    .link-type {
      padding-left: 0;
      padding-right: 0;
      max-width: 1200px;
      margin-top: 20px;
      margin-bottom: 16px;

      .btn-link-type {
        min-width: 258px;
        height: 44px;
        border-radius: 4px;
        padding: 10px 16px;
        background-color: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .lable {
          color: #6c6c6c;
          font-size: 16px;
          margin-right: 4px;
        }

        .link-type-selected {
          color: #1c1c1c;
          font-weight: 600;
          font-size: 16px;
        }
      }
    }

    .content-body {
      padding-left: 0;
      padding-right: 0;
      max-width: 1200px;

      .info-option {
        width: 792px;
      }

      .payment {
        width: 384px;
      }
    }
  }
}

.add-new-payment-link-loading {
  .component-loading {
    position: fixed;
    top: 0;
    z-index: 9999;
    background-color: black;
    opacity: 0.75;
  }
}

.link-type-slecet-panel {
  margin-top: 4px;

  .p-overlaypanel-content {
    padding: 12px;
    min-height: 160px;

    .link-type-option {
      padding: 8px;
      display: flex;
      cursor: pointer;
      height: 64px;

      &-icon {
        width: 24px;
        margin-right: 8px;
      }

      &-lables {
        .lable {
          font-weight: 600;
          font-size: 16px;
        }
      }

      &:not(:last-child) {
        margin-bottom: 16px;
      }

      &:hover {
        background-color: #f1f5f9;
      }
    }
  }
}

.link-type-option-highlight {
  background: rgba(59, 130, 246, 0.24);
}

.icon-dropdown {
  transition: transform 0.3s;
}

.is-open {
  .icon-dropdown {
    transform: rotate(180deg);
  }
}

.btn-disabled {
  background-color: #e6e6e6 !important;
  color: #bababa !important;
  pointer-events: none;
}
</style>
