<script setup lang="ts">
import InprogressPayment from "@/assets/icons/inprogress-payment.svg"
import ArrowDown from "@/assets/icons/payment-link-add-edit-arrow-down.svg"

import { EnumPaymentLinkType } from "@/app.const"
import { AppRouteNames } from "@/enums/routers"
import { EventCommandEnum, usePaymentLinkEventBus } from "@/events/eventBus"
import router from "@/routers/router"
import { usePaymentLinkService } from "@/services/paymentLinkService"
import { useToastService } from "@/services/toastService"
import type { InstallmentSettings, PaymentLink, PaymentLinkRequest, PresetAmountJson } from "@/types/apis"
import { useRefHistory } from "@vueuse/core"
import moment from "moment"
import { computed, ref, watch } from "vue"
import { useAsyncTask } from "vue-concurrency"
import { useI18n } from "vue-i18n"

const toastService = useToastService()
//const bus = useEventBus<string>("ADD_EDIT_PAYMENT_LINK_EVENT");
const eventBus = usePaymentLinkEventBus()
const editMode = ref(false)

const props = defineProps<{
  linkId: uuid
}>()

//const paymentId = defineModel<string>('linkId', { required: true });
const visibleModel = defineModel<boolean>("visible", {
  default: false,
})

const emits = defineEmits(["closePopupEdit"])

const {
  form,
  formValidator,
  getByIdPaymentLinkTask,
  editTask,
  setAddEditFormData,
  resetAddEditFormData,
  clearFormValidator,
} = usePaymentLinkService()
const i18n = useI18n()

const model = await getByIdPaymentLinkTask.perform(props.linkId)

// update form after fetch data
if (model) {
  setAddEditFormData(model)
}

let presetAmountJson = ref<PresetAmountJson>()
let installmentSettings = ref<InstallmentSettings[]>()

if (model.presetAmountJson) {
  presetAmountJson.value = JSON.parse(model.presetAmountJson)
  if (presetAmountJson.value) {
    if (!presetAmountJson.value.amount) presetAmountJson.value.amount = 0
    if (!presetAmountJson.value.discount) presetAmountJson.value.discount = 0
    if (!presetAmountJson.value.tax) presetAmountJson.value.tax = 0
    if (!presetAmountJson.value.otherFee) presetAmountJson.value.otherFee = 0
  }
}
if (model.installmentSettings) {
  installmentSettings.value = JSON.parse(model.installmentSettings)
}

const paymentLink = ref<PaymentLink>({
  id: model.id,
  merchantProfileId: model.merchantProfileId,
  routerUri: model.routerUri,
  name: model.name,
  description: model.description,
  dueDate: model.dueDate,
  paymentMethods: model.paymentMethods,
  installmentSettings: installmentSettings.value,
  hasPresetAmount: model.hasPresetAmount,
  presetAmountJson: presetAmountJson.value,
  totalAmount: model.totalAmount,
  currency: model.currency,
  exchangeRate: model.exchangeRate,
  paymentAmount: model.paymentAmount,
  paymentCurrency: model.paymentCurrency,
  hasLimitPayment: model.hasLimitPayment,
  maxPaymentQty: model.maxPaymentQty,
  totalPaymentQty: model.totalPaymentQty,
  infoFieldOptions: model.infoFieldOptions,
  customerNoteLckey: model.customerNoteLckey !== null ? model.customerNoteLckey : [],
  fileId: model.fileId,
  fileName: model.fileName ? model.fileName : null,
  createDate: 0,
  createByUserId: model.createByUserId,
  lastModifyDate: 0,
  lastModifyByUserId: "",
  enabled: model.enabled,
  status: model.status,
  linkType: model.linkType,
} as PaymentLink)

const { history } = useRefHistory(paymentLink, { deep: true })

const showInprogressPayment = ref(false)
const inprogressPayment = ref(0)

const isDisabledSubmit = computed(
  () =>
    !paymentLink.value.name ||
    !paymentLink.value.routerUri ||
    !editMode.value ||
    (formValidator.errors && formValidator.errors.length > 0)
)
interface SubmitOptions {
  inprogressConfirmed: boolean
}

const submitTask = useAsyncTask(async (signal, input?: SubmitOptions) => {
  const options = input ?? {
    inprogressConfirmed: false,
  }

  const canContinue = await beforeSubmit(options)
  if (!canContinue) return

  const request = ref<PaymentLinkRequest>({
    routerUri: paymentLink.value.routerUri,
    name: paymentLink.value.name,
    description: paymentLink.value.description,
    dueDate: paymentLink.value.dueDate ? moment(paymentLink.value.dueDate).toDate() : null,
    paymentMethods: paymentLink.value.paymentMethods,
    installmentSettings: JSON.stringify(paymentLink.value.installmentSettings),
    hasPresetAmount: paymentLink.value.hasPresetAmount,
    presetAmountJson: JSON.stringify(paymentLink.value.presetAmountJson),
    totalAmount: paymentLink.value.totalAmount,
    currency: paymentLink.value.currency,
    exchangeRate: paymentLink.value.exchangeRate,
    paymentAmount: paymentLink.value.paymentAmount,
    paymentCurrency: paymentLink.value.paymentCurrency,
    hasLimitPayment: paymentLink.value.hasLimitPayment,
    maxPaymentQty: paymentLink.value.maxPaymentQty,
    totalPaymentQty: paymentLink.value.totalPaymentQty,
    infoFieldOptions: paymentLink.value.infoFieldOptions,
    customerNoteLckey: paymentLink.value.customerNoteLckey,
    fileId: paymentLink.value.fileId,
    fileName: paymentLink.value.fileName,
    enable: paymentLink.value.enabled,
    linkType: paymentLink.value.linkType,
  } as PaymentLinkRequest)

  const data = await editTask.perform(request.value, paymentLink.value.id)
  // if (data && data === 'success') {
  //   toastService.success({});
  //   resetEditData();
  //   emits("closePopupEdit");
  //   bus.emit("");
  // } else {
  //   toastService.error({});
  // }

  if (data?.id) {
    //toastService.success({}); chuyển sang hiển thị bằng eventBus ở màn List
    resetAddEditFormData()
    emits("closePopupEdit")
    //bus.emit("");

    await router.push({
      name: AppRouteNames.PAYMENTLINK_LIST,
    })

    eventBus.emit({
      command: EventCommandEnum.Edited,
      data: {
        id: data.id,
      },
    })
  } else {
    toastService.error({}) // TODO: để tạm
  }
})
const submit = async (options?: SubmitOptions) => await submitTask.perform(options)

const beforeSubmit = async (options: SubmitOptions) => {
  // Xử lý kiểm tra lại trạng thái hiện tại của link trước khi cho phép cập nhật
  const paymentLink = await getByIdPaymentLinkTask.perform(model.id)

  if (!paymentLink) {
    console.error("[PL]", "Cannot get paymentLink", model.id)
    toastService.error({}) // TODO: để tạm

    return false // stop process
  }

  if (paymentLink.inprogressPayments && paymentLink.inprogressPayments > 0 && options?.inprogressConfirmed != true) {
    showInprogressPayment.value = true
    inprogressPayment.value = paymentLink.inprogressPayments

    // link đang có thanh toán -> hiện confirm dialog
    console.warn("[PL]", "Link is InprogressPayment", model.id)

    return false // stop process
  } else {
    showInprogressPayment.value = false
  }

  return true // canContinue process
}

async function onSidebarShow() {
  clearFormValidator()
}

async function onHideSidebar() {
  resetAddEditFormData()
  emits("closePopupEdit")

  await router.push({
    name: AppRouteNames.PAYMENTLINK_LIST,
  })
}

const linkTypeOptions = ref([
  {
    label: i18n.t("components-paymentLink-add.link-type-options.one-time.lable"),
    description: i18n.t("components-paymentLink-add.link-type-options.one-time.description"),
    value: EnumPaymentLinkType.OneTime,
    icon: new URL("/src/assets/icons/one-time-link.svg", import.meta.url).href,
    active: paymentLink.value.linkType === EnumPaymentLinkType.OneTime,
  },
  {
    label: i18n.t("components-paymentLink-add.link-type-options.multi.lable"),
    description: i18n.t("components-paymentLink-add.link-type-options.multi.description"),
    value: EnumPaymentLinkType.MultiUse,
    icon: new URL("/src/assets/icons/muti-use-link.svg", import.meta.url).href,
    active: paymentLink.value.linkType === EnumPaymentLinkType.MultiUse,
  },
])

const linkTypeSelected = ref(linkTypeOptions.value.find((item) => item.value === paymentLink.value.linkType))

watch(
  history,
  (item) => {
    editMode.value = JSON.stringify(item.at(item.length - 1)?.snapshot) !== JSON.stringify(item.at(0)?.snapshot)
  },
  { deep: true }
)

clearFormValidator()
</script>

<template>
  <AppSidebar
    v-model:visible="visibleModel"
    position="full"
    class="tms-page-payment-link-edit"
    :loading="submitTask.isRunning || getByIdPaymentLinkTask.isRunning"
    @show="onSidebarShow"
    @hide="onHideSidebar"
  >
    <template #header>
      <!-- <button class="btn-close" @click="close">
        <SvgIcon :src="PaymentLinkClose" />
      </button> -->
      <span class="title">{{ $t("components-paymentLink-edit.side-bar-header.title") }}</span>
      <Button
        :loading="submitTask.isRunning"
        class="btn-save"
        :class="{
          'btn-disabled-submit': isDisabledSubmit,
        }"
        @click="() => submit()"
      >
        {{ $t("components-paymentLink-edit.side-bar-header.action-save") }}
      </Button>
    </template>

    <div
      v-if="model.inprogressPayments && model.inprogressPayments > 0"
      class="container inprogress-payment content-body"
    >
      <SvgIcon :src="InprogressPayment" class="inprogress-payment-icon" />
      <div class="inprogress-payment-content">
        <span class="inprogress-payment-content-header">
          {{ $t("components-paymentLink-edit.side-bar-content.inprogress-header") }}
        </span>
        <span class="inprogress-payment-content-body">
          {{ $t("components-paymentLink-edit.side-bar-content.inprogress-body") }}
        </span>
      </div>
    </div>

    <div class="container link-type">
      <button v-tooltip="$t('components-paymentLink-edit.tooltip')" class="btn-link-type btn-disabled">
        <div>
          <span class="lable">
            {{ $t("components-paymentLink-add.link-type") }}
          </span>

          <span class="link-type-selected">
            {{ linkTypeSelected?.label }}
          </span>
        </div>

        <SvgIcon :src="ArrowDown" class="svg-arown"></SvgIcon>
      </button>
    </div>

    <div
      v-if="model"
      class="container d-flex justify-content-between content-body"
      :class="{ 'mt-0': model.inprogressPayments && model.inprogressPayments > 0 }"
    >
      <div class="info-option">
        <!-- Chỉ genertate LinkInfomation khi đã có editData model -->
        <LinkInfomation v-model="paymentLink" :is-edit="true" />

        <LinkOptions v-model="paymentLink" />
      </div>

      <div class="payment">
        <LinkPaymentAmount v-model="paymentLink" :is-edit="true" />

        <LinkLimitPayment v-model="paymentLink" />
      </div>
    </div>
  </AppSidebar>

  <Dialog v-model:visible="showInprogressPayment" modal class="confirm-edit" :draggable="false">
    <template #header>
      <span class="title">{{ $t("components-paymentLink-edit.dialog-confirm.title") }}</span>
    </template>

    <span>
      {{ $t("components-paymentLink-edit.dialog-confirm.detail", { inprogressPayment: inprogressPayment }) }}
    </span>

    <template #footer>
      <button class="btn-cancel" @click="showInprogressPayment = false">
        {{ $t("components-paymentLink-edit.dialog-confirm.actions.cancel") }}
      </button>
      <Button
        :loading="submitTask.isRunning"
        :class="{
          'btn-disabled-submit': isDisabledSubmit,
        }"
        class="btn-confirm"
        @click="() => submit({ inprogressConfirmed: true })"
      >
        {{ $t("components-paymentLink-edit.dialog-confirm.actions.confirm") }}
      </Button>
    </template>
  </Dialog>
</template>

<style lang="scss">
.tms-page-payment-link-edit {
  .inprogress-payment {
    display: flex;
    align-items: center;
    background-color: #ffeff0;
    height: 70px;
    padding: 12px 24px !important;
    margin-top: 20px !important;
    margin-bottom: 20px;
    border-radius: 8px;
    border: 1px solid #ed98a4;

    &-icon {
      margin-right: 12px;
    }

    &-content {
      display: grid;

      &-header {
        font-weight: 600;
        color: #1c1c1c;
      }

      &-body {
        color: #404040;
      }
    }
  }

  .p-sidebar-header {
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e6e6e6;

    .btn-close {
      width: 48px;
      height: 48px;
      background-color: #f5f5f5;
    }

    .title {
      font-size: 24px;
      font-weight: 600;
      line-height: 36px;
      color: #1c1c1c;
    }

    .btn-save {
      min-width: 87px;
      height: 48px;
      justify-content: center;
      background-color: #2e6be5;
      border: none;
      border-radius: 4px;
      padding: 12px 24px;
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;
      color: #fff;
    }
  }

  .p-sidebar-content {
    background-color: #f0f0f0;
    height: 100%;
    padding: 0;

    .link-type {
      padding-left: 0;
      padding-right: 0;
      max-width: 1200px;
      margin-top: 20px;
      margin-bottom: 16px;

      .btn-link-type {
        min-width: 258px;
        height: 44px;
        border-radius: 4px;
        padding: 10px 16px;
        background-color: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border: none;

        .lable {
          color: #6c6c6c;
          font-size: 16px;
          margin-right: 4px;
        }

        .link-type-selected {
          color: #1c1c1c;
          font-weight: 600;
          font-size: 16px;
        }
      }

      .btn-disabled {
        .lable {
          opacity: 0.5;
        }

        .link-type-selected {
          opacity: 0.5;
        }

        .svg-arown {
          opacity: 0.5;
        }
      }
    }

    .content-body {
      padding-left: 0;
      padding-right: 0;
      max-width: 1200px;

      .info-option {
        width: 792px;
      }

      .payment {
        width: 384px;
      }
    }
  }
}

.confirm-edit {
  min-width: 480px;
  max-width: 480px;
  border-radius: 8px;
  padding: 24px;

  .p-dialog-header {
    padding: 0;

    .p-dialog-header-icons {
      display: none;
    }

    .title {
      font-size: 24px;
      font-weight: 600;
      line-height: 36px;
      color: #1c1c1c;
    }
  }

  .p-dialog-content {
    padding: 0;
    margin-top: 12px;
  }

  .p-dialog-footer {
    padding: 0;
    justify-content: space-between;
    margin-top: 32px;

    .btn-cancel {
      width: 208px;
      height: 48px;
      justify-content: center;
      border: none;
      background-color: #f5f5f5;
      color: #404040;
      font-weight: 700;
      border-radius: 4px;
    }

    .btn-confirm {
      width: 208px;
      height: 48px;
      justify-content: center;
      border: none;
      background-color: #d82039;
      color: #fff;
      font-weight: 700;
      border-radius: 4px;
    }
  }
}

.edit-paymet-link-loading {
  .component-loading {
    position: fixed;
    top: 0;
    z-index: 9999;
    background-color: black;
    opacity: 0.75;
  }
}

.btn-disabled-submit {
  background-color: #e6e6e6 !important;
  color: #bababa !important;
  pointer-events: none;
}
</style>
