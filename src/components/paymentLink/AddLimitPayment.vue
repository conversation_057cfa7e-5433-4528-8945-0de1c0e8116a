<script setup lang="ts">
import PaymentLinkClose from "@/assets/icons/payment-link-close.svg";

import APPCONFIG from "@/appConfig";
import { useDebounceFn } from "@vueuse/core";
import { ref } from "vue";

const isDisabledConfirm = ref(false)
const hasLimitPayment = ref(false)
const maxPaymentQty = ref(0)

const emit = defineEmits(["closePopupAddLimit", "addLimit"])

const props = withDefaults(
  defineProps<{
    visible: boolean
    propHasLimitPayment?: boolean
    propMaxPaymentQty?: number
  }>(),
  {
    visible: false,
    propHasLimitPayment: undefined,
    propMaxPaymentQty: undefined,
  }
)

const addLimit = () => {
  hasLimitPayment.value = true

  emit("addLimit", {
    hasLimitPayment: hasLimitPayment.value,
    maxPaymentQty: maxPaymentQty.value,
  })
}

const closeLimit = () => {
  emit("closePopupAddLimit")
}

const onShow = () => {
  if (props.propHasLimitPayment !== undefined) {
    hasLimitPayment.value = props.propHasLimitPayment
  }
  if (props.propMaxPaymentQty !== undefined) {
    maxPaymentQty.value = props.propMaxPaymentQty
  }
}

const checkIsValuelLesserThanZero = useDebounceFn((value?: any) => {
  if (!value || value < 0 || isNaN(value)) isDisabledConfirm.value = true
  else isDisabledConfirm.value = false
}, 100)
</script>

<template>
  <Dialog
    :visible="visible"
    modal
    class="add-litmit-payment"
    :draggable="false"
    @show="onShow"
  >
    <template #header>
      <span class="title">{{ $t("components-paymentLink-AddLimit.title") }}</span>
      <button type="button" class="btn-close" @click="closeLimit">
        <SvgIcon :src="PaymentLinkClose" />
      </button>
    </template>

    <!-- <InputNumberNew
      v-model="maxPaymentQty"
      :is-integer="true"
      :max="APPCONFIG.MAX_PAYMENT_QUANTITY"
      placeholder="Example: 100"
      locale="en-US"
      @input="checkIsValuelLesserThanZero($event.value)"
    /> -->
    <InputAmount
      v-model:model-value="maxPaymentQty"
      name="limitPayment-limit"
      mode="integer"
      :placeholder="$t('components-paymentLink-AddLimit.inputAmount-placeholder')"
      :max="APPCONFIG.MAX_PAYMENT_QUANTITY"
      @update:model-value="checkIsValuelLesserThanZero($event)"
    />

    <template #footer>
      <Button :label="$t('components-paymentLink-AddLimit.btn-cancel')" class="btn-cancel" @click="closeLimit" />

      <Button
        :label="$t('components-paymentLink-AddLimit.btn-confirm')"
        :class="{ 'btn-disabled-limit': isDisabledConfirm || maxPaymentQty === 0 }"
        class="btn-confirm"
        @click="addLimit"
      />
    </template>
  </Dialog>
</template>

<style lang="scss">
.add-litmit-payment {
  border-radius: 12px;
  padding: 20px 24px;
  min-width: 520px;

  .p-dialog-header {
    padding: 0;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .p-dialog-header-icons {
      display: none;
    }

    .title {
      font-size: 24px;
      font-weight: 600;
      line-height: 36px;
      color: #1c1c1c;
    }

    .btn-close {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      padding: 8px;
    }
  }

  .p-dialog-content {
    padding: 0;

    span.p-inputnumber {
      margin: 12px 1px;
      width: 472px;

      .p-inputtext {
        &::placeholder {
          color: #bababa;
          opacity: 1;
          /* Firefox */
        }

        &::-ms-input-placeholder {
          /* Edge 12-18 */
          color: #bababa;
        }
      }
    }
  }

  .p-dialog-footer {
    padding: 0;
    margin-top: 20px;

    .btn-cancel {
      min-width: 100px;
      height: 40px;
      border-radius: 4px;
      padding: 10px 24px;
      color: #404040;
      margin-right: 20px;
      border: none;
      background-color: #f5f5f5;
    }

    .btn-confirm {
      min-width: 120px;
      height: 40px;
      border-radius: 4px;
      padding: 10px 24px;
      color: #ffffff;
      border: none;
      background-color: #2e6be5;
    }

    .p-button-label {
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
    }
  }
}

.btn-disabled-limit {
  background-color: #e6e6e6 !important;
  color: #bababa !important;
  pointer-events: none;
}
</style>
