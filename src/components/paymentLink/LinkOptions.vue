<script setup lang="ts">
import ArrowDown from "@/assets/icons/payment-link-add-edit-arrow-down.svg"
import CLoseIcon from "@/assets/icons/payment-link-add-edit-close.svg"
import EnglishIcon from "@/assets/icons/payment-link-add-edit-locale-en.svg"
import VietNamIcon from "@/assets/icons/payment-link-add-edit-locale-vi.svg"
import UploadFileIcon from "@/assets/icons/payment-link-upload-file-icon.svg"

import { DEFAULT_VALUE_NUMBER_PAYMENT_LINK, InfoFieldOptions } from "@/app.const"
import { usePaymentLinkService } from "@/services/paymentLinkService"
import type { CustomerNoteLocale, PaymentLink } from "@/types/apis"
import { ref, watch } from "vue"
import moment from "moment"
import { useToastService } from "@/services/toastService"
import { useGlobalI18n } from "@/plugins/i18n"

const model = defineModel<PaymentLink>({ required: true })

const { getAllLocalesTask, uploadTask, downloadTask } = usePaymentLinkService()
const toastService = useToastService()
const { t } = useGlobalI18n() as { t: Function }

const locales = await getAllLocalesTask.perform()

/// infoFieldOptions
const checkCollect = ref<number[]>([])
const requieName = ref<number>(DEFAULT_VALUE_NUMBER_PAYMENT_LINK)
const requireEmail = ref(DEFAULT_VALUE_NUMBER_PAYMENT_LINK)
const requirePhone = ref(DEFAULT_VALUE_NUMBER_PAYMENT_LINK)
const requireAddress = ref(DEFAULT_VALUE_NUMBER_PAYMENT_LINK)
const requireNotes = ref(DEFAULT_VALUE_NUMBER_PAYMENT_LINK)
/// open menu
const collapse = ref(false)
const collectName = ref(false)
const collectPhone = ref(false)
const collectMail = ref(false)
const collectAddress = ref(false)
const customerNote = ref(false)
const addInfo = ref(false)
const addDueDate = ref(false)
const addAttachment = ref(false)
// customer note locale key
const customerNoteLcKey = ref<CustomerNoteLocale[]>([])

if (model.value.dueDate) addDueDate.value = true

if (model.value.fileId) addAttachment.value = true

const infoFieldOptions = ref<number[]>([InfoFieldOptions.enableName, InfoFieldOptions.requireName])

if (model.value.infoFieldOptions > 0) {
  addInfo.value = true
  infoFieldOptions.value = []

  if ((model.value.infoFieldOptions & InfoFieldOptions.enableName) > 0) {
    checkCollect.value.push(InfoFieldOptions.enableName)
    collectName.value = true
    infoFieldOptions.value.push(InfoFieldOptions.enableName)
  }
  if ((model.value.infoFieldOptions & InfoFieldOptions.enableEmail) > 0) {
    checkCollect.value.push(InfoFieldOptions.enableEmail)
    collectMail.value = true
    infoFieldOptions.value.push(InfoFieldOptions.enableEmail)
  }
  if ((model.value.infoFieldOptions & InfoFieldOptions.enablePhone) > 0) {
    checkCollect.value.push(InfoFieldOptions.enablePhone)
    collectPhone.value = true
    infoFieldOptions.value.push(InfoFieldOptions.enablePhone)
  }
  if ((model.value.infoFieldOptions & InfoFieldOptions.enableAddress) > 0) {
    checkCollect.value.push(InfoFieldOptions.enableAddress)
    collectAddress.value = true
    infoFieldOptions.value.push(InfoFieldOptions.enableAddress)
  }
  if ((model.value.infoFieldOptions & InfoFieldOptions.enableNotes) > 0) {
    checkCollect.value.push(InfoFieldOptions.enableNotes)
    customerNote.value = true
    infoFieldOptions.value.push(InfoFieldOptions.enableNotes)
  }

  if ((model.value.infoFieldOptions & InfoFieldOptions.requireName) > 0) {
    requieName.value = InfoFieldOptions.requireName
    infoFieldOptions.value.push(InfoFieldOptions.requireName)
  }
  if ((model.value.infoFieldOptions & InfoFieldOptions.requireEmail) > 0) {
    requireEmail.value = InfoFieldOptions.requireEmail
    infoFieldOptions.value.push(InfoFieldOptions.requireEmail)
  }
  if ((model.value.infoFieldOptions & InfoFieldOptions.requirePhone) > 0) {
    requirePhone.value = InfoFieldOptions.requirePhone
    infoFieldOptions.value.push(InfoFieldOptions.requirePhone)
  }
  if ((model.value.infoFieldOptions & InfoFieldOptions.requireAddress) > 0) {
    requireAddress.value = InfoFieldOptions.requireAddress
    infoFieldOptions.value.push(InfoFieldOptions.requireAddress)
  }
  if ((model.value.infoFieldOptions & InfoFieldOptions.requireNotes) > 0) {
    requireNotes.value = InfoFieldOptions.requireNotes
    infoFieldOptions.value.push(InfoFieldOptions.requireNotes)
  }
} else {
  checkCollect.value.push(InfoFieldOptions.enableName)
  collectName.value = true
  requieName.value = InfoFieldOptions.requireName

  infoFieldOptions.value.forEach((element) => {
    model.value.infoFieldOptions = model.value.infoFieldOptions | element
  })
}

if (model.value.customerNoteLckey && model.value.customerNoteLckey.length > 0) {
  customerNoteLcKey.value = model.value.customerNoteLckey
}

watch(requieName, () => {
  if (requieName.value != DEFAULT_VALUE_NUMBER_PAYMENT_LINK) infoFieldOptions.value.push(requieName.value)
  else infoFieldOptions.value.splice(infoFieldOptions.value.indexOf(InfoFieldOptions.requireName), 1)

  getInfoFieldOptions()
})

watch(requireEmail, () => {
  if (requireEmail.value != DEFAULT_VALUE_NUMBER_PAYMENT_LINK) infoFieldOptions.value.push(requireEmail.value)
  else infoFieldOptions.value.splice(infoFieldOptions.value.indexOf(InfoFieldOptions.requireEmail), 1)

  getInfoFieldOptions()
})

watch(requirePhone, () => {
  if (requirePhone.value != DEFAULT_VALUE_NUMBER_PAYMENT_LINK) infoFieldOptions.value.push(requirePhone.value)
  else infoFieldOptions.value.splice(infoFieldOptions.value.indexOf(InfoFieldOptions.requirePhone), 1)

  getInfoFieldOptions()
})

watch(requireAddress, () => {
  if (requireAddress.value != DEFAULT_VALUE_NUMBER_PAYMENT_LINK)
    infoFieldOptions.value.push(requireAddress.value)
  else
    infoFieldOptions.value.splice(infoFieldOptions.value.indexOf(InfoFieldOptions.requireAddress), 1)

  getInfoFieldOptions()
})

watch(requireNotes, () => {
  if (requireNotes.value != DEFAULT_VALUE_NUMBER_PAYMENT_LINK)
    infoFieldOptions.value.push(requireNotes.value)
  else
    infoFieldOptions.value.splice(infoFieldOptions.value.indexOf(InfoFieldOptions.requireNotes), 1)

  getInfoFieldOptions()
})

const toggleBody = () => {
  collapse.value = !collapse.value
}

const toggleName = () => {
  collectName.value = !collectName.value

  if (collectName.value) {
    checkCollect.value.forEach((element) => {
      if (infoFieldOptions.value.indexOf(element) < 0)
        infoFieldOptions.value.push(element)
    })

    requieName.value = InfoFieldOptions.requireName
  } else {
    infoFieldOptions.value.splice(infoFieldOptions.value.indexOf(InfoFieldOptions.enableName), 1)

    requieName.value = DEFAULT_VALUE_NUMBER_PAYMENT_LINK
  }
}

const togglePhone = () => {
  collectPhone.value = !collectPhone.value

  if (collectPhone.value) {
    checkCollect.value.forEach((element) => {
      if (infoFieldOptions.value.indexOf(element) < 0)
        infoFieldOptions.value.push(element)
    })
  } else {
    infoFieldOptions.value.splice(infoFieldOptions.value.indexOf(InfoFieldOptions.enablePhone), 1)
  }

  requirePhone.value = DEFAULT_VALUE_NUMBER_PAYMENT_LINK
  getInfoFieldOptions()
}

const toggleMail = () => {
  collectMail.value = !collectMail.value

  if (collectMail.value) {
    checkCollect.value.forEach((element) => {
      if (infoFieldOptions.value.indexOf(element) < 0)
        infoFieldOptions.value.push(element)
    })
  } else {
    infoFieldOptions.value.splice(infoFieldOptions.value.indexOf(InfoFieldOptions.enableEmail), 1)
  }

  requireEmail.value = DEFAULT_VALUE_NUMBER_PAYMENT_LINK
  getInfoFieldOptions()
}

const toggleAddress = () => {
  collectAddress.value = !collectAddress.value

  if (collectAddress.value) {
    checkCollect.value.forEach((element) => {
      if (infoFieldOptions.value.indexOf(element) < 0)
        infoFieldOptions.value.push(element)
    })
  } else {
    infoFieldOptions.value.splice(infoFieldOptions.value.indexOf(InfoFieldOptions.enableAddress), 1)
  }

  requireAddress.value = DEFAULT_VALUE_NUMBER_PAYMENT_LINK
  getInfoFieldOptions()
}

const toggleCustomerNote = () => {
  customerNote.value = !customerNote.value

  if (customerNote.value) {
    checkCollect.value.forEach((element) => {
      if (infoFieldOptions.value.indexOf(element) < 0)
        infoFieldOptions.value.push(element)
    })

    locales.forEach((element) => {
      customerNoteLcKey.value.push({
        locale: element.id,
        value: element.id === "en" ? "Customer note" : "Ghi chú khách hàng",
      })
    })
  } else {
    infoFieldOptions.value.splice(infoFieldOptions.value.indexOf(InfoFieldOptions.enableNotes), 1)

    customerNoteLcKey.value = []
  }

  model.value.customerNoteLckey = customerNoteLcKey.value

  requireNotes.value = DEFAULT_VALUE_NUMBER_PAYMENT_LINK
  getInfoFieldOptions()
}

const toggleInfo = () => {
  addInfo.value = !addInfo.value
  checkCollect.value = [InfoFieldOptions.enableName]
  collectPhone.value = false
  collectMail.value = false
  collectAddress.value = false
  customerNote.value = false
  infoFieldOptions.value = [InfoFieldOptions.enableName, InfoFieldOptions.requireName]
  getInfoFieldOptions()
}

const toggleDueDate = () => {
  addDueDate.value = !addDueDate.value
  model.value.dueDate = addDueDate.value ? moment().endOf("date").toDate() : null
}

const toggleAttachment = () => {
  addAttachment.value = !addAttachment.value
  isHasFile.value = false
  model.value.fileId = null
  model.value.fileName = null
}

const getInfoFieldOptions = () => {
  model.value.infoFieldOptions = 0

  infoFieldOptions.value.forEach((element) => {
    model.value.infoFieldOptions = model.value.infoFieldOptions | element
  })
}

const isHasFile = ref(false)

if (model.value.fileId && model.value.fileId != null) {
  isHasFile.value = true
}

const myUploader = async (files: any) => {
  const fileName = files.files[0].name
  if (fileName && fileName.length > 200) {
    toastService.error({
      summary: t("file-name-too-long"),
    })
    return
  }

  const data = await uploadTask.perform(files.files)

  if (data) {
    model.value.fileId = data.id
    model.value.fileName = data.fileName
    isHasFile.value = true
  }
}

const downloadFile = async () => {
  if (!model.value.fileId) return

  await downloadTask.perform(model.value.fileId)
}
</script>

<template>
  <div class="card options">
    <div class="options-header" @click="toggleBody">
      <span>
        {{ $t("components-paymentLink-LinkOptions.title") }}
      </span>

      <div class="toggle-body" :class="{ 'is-collapse': collapse }">
        <SvgIcon :src="ArrowDown" class="icon-collapse" />
      </div>
    </div>

    <div v-if="collapse" class="options-body">
      <div class="add mb-3">
        <div class="add-info">
          <span class="label-content">{{ $t("components-paymentLink-LinkOptions.info") }}</span>
          <div class="add-content" @click="toggleInfo">
            <span v-if="!addInfo">{{ $t("components-paymentLink-LinkOptions.btn-add") }}</span>
            <SvgIcon v-if="addInfo" :src="CLoseIcon" />
          </div>
        </div>

        <div v-if="addInfo" class="content-infomation">
          <div class="content-infomation-header">
            <span class="info">{{ $t("components-paymentLink-LinkOptions.collect-info") }}</span>
            <div class="group-radio">
              <span class="requied">{{ $t("components-paymentLink-LinkOptions.required") }}</span>
              <span class="option">{{ $t("components-paymentLink-LinkOptions.optional") }}</span>
            </div>
          </div>

          <div class="content-infomation-body">
            <div class="collect collect-name">
              <div class="tms-group-check-box">
                <div class="group-item">
                  <input
                    id="chk-name"
                    v-model="checkCollect"
                    :value="InfoFieldOptions.enableName"
                    class="form-check-input"
                    type="checkbox"
                    @change="toggleName"
                  />
                  <label class="form-check-label" for="chk-name">
                    {{ $t("components-paymentLink-LinkOptions.name") }}
                  </label>
                </div>
              </div>
              <div v-if="collectName" class="group-radio">
                <input
                  v-model="requieName"
                  :value="InfoFieldOptions.requireName"
                  class="form-check-input checkbox-content"
                  type="radio"
                  name="collcet-name"
                />
                <input
                  v-model="requieName"
                  value="0"
                  class="form-check-input checkbox-content"
                  type="radio"
                  name="collcet-name"
                />
              </div>
            </div>

            <div class="collect collect-phone">
              <div class="tms-group-check-box">
                <div class="group-item">
                  <input
                    id="chk-phone"
                    v-model="checkCollect"
                    :value="InfoFieldOptions.enablePhone"
                    class="form-check-input"
                    type="checkbox"
                    @change="togglePhone"
                  />
                  <label class="form-check-label" for="chk-phone">
                    {{ $t("components-paymentLink-LinkOptions.phone-number") }}
                  </label>
                </div>
              </div>
              <div v-if="collectPhone" class="group-radio">
                <input
                  v-model="requirePhone"
                  :value="InfoFieldOptions.requirePhone"
                  class="form-check-input checkbox-content"
                  type="radio"
                  name="collcet-phone"
                />
                <input
                  v-model="requirePhone"
                  value="0"
                  class="form-check-input checkbox-content"
                  type="radio"
                  name="collcet-phone"
                />
              </div>
            </div>

            <div class="collect collect-mail">
              <div class="tms-group-check-box">
                <div class="group-item">
                  <input
                    id="chk-mail"
                    v-model="checkCollect"
                    :value="InfoFieldOptions.enableEmail"
                    class="form-check-input"
                    type="checkbox"
                    @change="toggleMail"
                  />
                  <label class="form-check-label" for="chk-mail">
                    {{ $t("components-paymentLink-LinkOptions.email") }}
                  </label>
                </div>
              </div>
              <div v-if="collectMail" class="group-radio">
                <input
                  v-model="requireEmail"
                  :value="InfoFieldOptions.requireEmail"
                  class="form-check-input checkbox-content"
                  type="radio"
                  name="collcet-mail"
                />
                <input
                  v-model="requireEmail"
                  value="0"
                  class="form-check-input checkbox-content"
                  type="radio"
                  name="collcet-mail"
                />
              </div>
            </div>

            <div class="collect collect-address">
              <div class="tms-group-check-box">
                <div class="group-item">
                  <input
                    id="chk-address"
                    v-model="checkCollect"
                    :value="InfoFieldOptions.enableAddress"
                    class="form-check-input"
                    type="checkbox"
                    @change="toggleAddress"
                  />
                  <label class="form-check-label" for="chk-address">
                    {{ $t("components-paymentLink-LinkOptions.address") }}
                  </label>
                </div>
              </div>
              <div v-if="collectAddress" class="group-radio">
                <input
                  v-model="requireAddress"
                  :value="InfoFieldOptions.requireAddress"
                  class="form-check-input checkbox-content"
                  type="radio"
                  name="collcet-address"
                />
                <input
                  v-model="requireAddress"
                  value="0"
                  class="form-check-input checkbox-content"
                  type="radio"
                  name="collcet-address"
                />
              </div>
            </div>

            <div class="collect collect-customer-note">
              <div class="tms-group-check-box customer-note">
                <div class="customer-note">
                  <input
                    v-model="checkCollect"
                    :value="InfoFieldOptions.enableNotes"
                    class="form-check-input customer-note-check-box"
                    type="checkbox"
                    @change="toggleCustomerNote"
                  />
                  <input-text
                    v-if="!customerNote"
                    class="customer-note-input"
                    :placeholder="$t('components-paymentLink-LinkOptions.customer-note')"
                    readonly
                  />
                  <div v-if="customerNote">
                    <div v-for="(item, index) in locales" :key="index" class="group-input-note">
                      <InputGroup>
                        <InputGroupAddon class="locale-icon">
                          <SvgIcon v-if="item.id === 'vi'" :src="VietNamIcon" />
                          <SvgIcon v-if="item.id === 'en'" :src="EnglishIcon" />
                        </InputGroupAddon>
                        <InputTextClearable
                          v-model="customerNoteLcKey[index].value"
                          class="customer-locale-note-input"
                          maxlength="200"
                        />
                      </InputGroup>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="customerNote" class="group-radio" style="height: 36px">
                <input
                  v-model="requireNotes"
                  :value="InfoFieldOptions.requireNotes"
                  class="form-check-input checkbox-content"
                  type="radio"
                  name="collcet-customer-note"
                />
                <input
                  v-model="requireNotes"
                  value="0"
                  class="form-check-input checkbox-content"
                  type="radio"
                  name="collcet-customer-note"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="add mb-3">
        <div class="add-due-date">
          <span class="label-content">{{ $t("components-paymentLink-LinkOptions.due-date") }}</span>
          <div class="add-content" @click="toggleDueDate">
            <span v-if="!addDueDate">{{ $t("components-paymentLink-LinkOptions.btn-add") }}</span>
            <SvgIcon v-if="addDueDate" :src="CLoseIcon" />
          </div>
        </div>

        <div v-if="addDueDate" class="content-due-date">
          <!-- <TmsDateTimePicker v-model="model.dueDate" /> -->
          <InputDateTimePickerAppliable
            v-model="model.dueDate"
            placeholder="DD/MM/YYYY hh:mm aa"
          ></InputDateTimePickerAppliable>
        </div>
      </div>

      <div class="add">
        <div class="add-attachment">
          <span class="label-content">{{ $t("components-paymentLink-LinkOptions.attachment") }}</span>
          <div class="add-content" @click="toggleAttachment">
            <span v-if="!addAttachment">{{ $t("components-paymentLink-LinkOptions.btn-add") }}</span>
            <SvgIcon v-if="addAttachment" :src="CLoseIcon" />
          </div>
        </div>

        <div v-if="addAttachment" class="content-attachment">
          <FileUpload
            v-if="!isHasFile"
            mode="basic"
            accept="image/png, image/jpg, image/jpeg, .pdf, .doc, .docx"
            :max-file-size="10000000"
            :custom-upload="true"
            :choose-label="$t('components-paymentLink-LinkOptions.upload-file')"
            :auto="true"
            :invalid-file-size-message="$t('file-upload-massage.error.limit-file-size')"
            :invalid-file-type-message="$t('file-upload-massage.error.invalid-file-fomat')"
            @uploader="myUploader"
          />

          <div v-else class="choose-other-file">
            <div v-tooltip="{ value: model.fileName }" class="choose-other-file-name">
              <SvgIcon :src="UploadFileIcon" />
              <a class="text-truncate" @click="downloadFile">{{ model.fileName }}</a>
            </div>
            <FileUpload
              mode="basic"
              accept="image/png, image/jpg, image/jpeg, .pdf, .doc, .docx"
              :max-file-size="10000000"
              :custom-upload="true"
              choose-label="Change file"
              :auto="true"
              :invalid-file-size-message="$t('file-upload-massage.error.limit-file-size')"
              :invalid-file-type-message="$t('file-upload-massage.error.invalid-file-fomat')"
              @uploader="myUploader"
            />
          </div>

          <ul class="list-upload">
            <li>{{ $t("components-paymentLink-LinkOptions.note-1") }}</li>
            <li>{{ $t("components-paymentLink-LinkOptions.note-2") }}</li>
            <li>{{ $t("components-paymentLink-LinkOptions.note-3") }}</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <Teleport to="body">
    <div class="upload-file-loading">
      <AppLoading v-if="uploadTask.isRunning"> </AppLoading>
    </div>
  </Teleport>
</template>

<style lang="scss">
.options {
  margin-top: 12px;
  margin-bottom: 45px;
  border: none;

  .options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    color: #1c1c1c;
    padding: 16px 24px;

    .toggle-body {
      cursor: pointer;
    }
  }

  .options-body {
    padding: 0 24px 16px 24px;

    .add {
      &-info {
        height: 33px;
        display: flex;
        justify-content: space-between;
      }

      &-due-date {
        height: 33px;
        display: flex;
        justify-content: space-between;
      }

      &-attachment {
        margin-bottom: 8px;
        display: flex;
        justify-content: space-between;
      }

      .content-infomation {
        margin-bottom: 16px;

        .group-radio {
          min-width: 160px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        &-header {
          padding: 6px 16px;
          display: flex;
          justify-content: space-between;
        }

        &-body {
          .collect {
            display: flex;
            justify-content: space-between;
            padding: 10px 16px;
            border: 1px solid #e6e6e6;
          }

          .tms-group-check-box {
            .group-item {
              height: 24px;

              .form-check-input {
                margin: 3px 11px 3px 3px;
                width: 18px;
                height: 18px;
              }

              .form-check-label {
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
                color: #404040;
              }
            }
          }

          .form-check-input:checked[type="radio"] {
            --bs-form-check-bg-image: url("@/assets/icons/checkbox-imge.svg") !important;
            background-color: #fff !important;
            border-color: #2e6be5 !important;
          }

          .form-check-input {
            border-color: #6c6c6c;
          }

          .checkbox-content {
            margin-left: 1.875rem;
            margin-right: 1.875rem;
          }

          .customer-note {
            display: flex;

            &-check-box {
              width: 18px;
              height: 18px;
              margin: 11px 11px 3px 3px;
            }

            &-input {
              min-width: 211px;
              height: 40px;
              font-size: 14px;
              font-weight: 400;
              line-height: 20px;

              &::placeholder {
                color: #bababa;
                opacity: 1;
                /* Firefox */
              }

              &::-ms-input-placeholder {
                /* Edge 12-18 */
                color: #bababa;
              }
            }

            .group-input-note {
              .locale-icon {
                width: 36px;
                height: 36px;
                background-color: #f5f5f5;
                padding: calc(8px - 1px);
              }

              .customer-locale-note-input {
                height: 36px;
                min-width: 190px;
                // border-radius: 0 4px 4px 0;
                // border-left: none;
                // padding: 8px 16px;
                padding-top: calc(8px - 1px);
                padding-bottom: calc(8px - 1px);
              }
            }

            .group-input-note:not(:last-child) {
              margin-bottom: 0.5rem;
            }
          }
        }
      }

      .content-due-date {
        margin-bottom: 16px;
        max-width: 326px;
      }

      .content-attachment {
        .p-fileupload.p-fileupload-basic {
          .p-button.p-component.p-fileupload-choose {
            min-width: 94px;
            height: 32px;
            border-radius: 4px;
            padding: 8px 16px;
            background-color: #fff;
            font-size: 12px;
            font-weight: 500;
            line-height: 16px;
            color: #404040;
            border: none;
            outline: 1px solid #dcdcdc;

            svg {
              display: none;
            }
          }
        }

        .list-upload {
          margin-top: 8px;
          padding-left: 1.5rem;

          li {
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            color: #6c6c6c;
          }
        }

        .choose-other-file {
          &-name {
            display: flex;
            align-items: center;

            svg {
              margin-right: 4px;
            }

            a {
              text-decoration: none;
              font-size: 14;
              font-weight: 400;
              line-height: 20px;
              color: #2e6be5;
            }

            margin-bottom: 8px;
          }
        }

        .p-message {
          height: 36px;
          padding: 7px 16px;
          background-color: #ffeff0;
          border: 1px solid #ed98a4;

          .p-message-wrapper {
            padding: 0;

            .p-message-icon {
              width: 15px;
              height: 15px;
            }

            .p-message-text {
              font-weight: 400;
              font-size: 14px;
              color: #404040;
            }

            .p-message-close {
              height: 16px;
              width: 16px;

              .p-message-close-icon {
                width: 12px;
                height: 12px;
              }
            }
          }
        }
      }

      .label-content {
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        color: #404040;
      }

      .add-content {
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        color: #2e6be5;
        cursor: pointer;
      }
    }

    .add:not(:last-child) {
      border-bottom: 1px solid #e6e6e6;
    }
  }
}

.toggle-body {
  .icon-collapse {
    transition: transform 0.3s;
  }
}

.is-collapse {
  .icon-collapse {
    transform: rotate(180deg);
  }
}

.upload-file-loading {
  .component-loading {
    position: fixed;
    top: 0;
    z-index: 9999;
    background-color: black;
    opacity: 0.75;
  }
}
</style>
