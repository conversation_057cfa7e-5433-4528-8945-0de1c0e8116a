<script setup lang="ts">
import Delete from "@/assets/icons/payment-link-delete-amount-limit.svg"
import Edit from "@/assets/icons/payment-link-edit-amount-limit.svg"

import { EnumPaymentLinkType } from "@/app.const"
import type { PaymentLink } from "@/types/apis"
import { formatNumber } from "@/utils/formatUtil"
import { ref } from "vue"

const model = defineModel<PaymentLink>({ required: true })

const limitPayment = ref(false)

const openLimitPayment = (isAdd: boolean) => {
  limitPayment.value = true

  if (isAdd) model.value.maxPaymentQty = 0
}

const closeLimitPayment = () => {
  limitPayment.value = false
}

const addLimitPayment = (emitModel: any) => {
  model.value.hasLimitPayment = emitModel.hasLimitPayment
  model.value.maxPaymentQty = emitModel.maxPaymentQty
  limitPayment.value = false
}

const resetLimit = () => {
  model.value.hasLimitPayment = false
}
</script>

<template>
  <div class="card limit-number-payment">
    <div v-if="!model.hasLimitPayment" class="limit-number-payment-header">
      <div class="label">
        <span class="label-header">{{ $t("components-paymentLink-LinkLimitPayment.title") }}</span>
        <span class="label-content">{{ $t("components-paymentLink-LinkLimitPayment.content") }}</span>
      </div>

      <div class="action">
        <a href="javascript:void(0)" class="action-add-limit-number" @click="openLimitPayment(true)">
          {{ $t("components-paymentLink-LinkLimitPayment.btn-add") }}
        </a>
      </div>
    </div>

    <div v-else class="limit-number-payment-content">
      <div class="limit-number-payment-content-header">
        <div class="label">
          <span class="label-header">{{ $t("components-paymentLink-LinkLimitPayment.title") }}</span>
        </div>

        <div class="action" :class="{ 'group-action-disabled': model.linkType === EnumPaymentLinkType.OneTime }">
          <a href="javascript:void(0)" class="action-edit-limit-number" @click="openLimitPayment(false)">
            <SvgIcon :src="Edit" />
          </a>
          <a href="javascript:void(0)" class="action-delete-limit-number" @click="resetLimit">
            <SvgIcon :src="Delete" />
          </a>
        </div>
      </div>
      <div class="limit-number-payment-content-content">
        <span class="label">{{ $t("components-paymentLink-LinkLimitPayment.title") }}</span>
        <span
          v-tooltip="{
            disabled: formatNumber(model.maxPaymentQty, 0).length <= 5,
            value: formatNumber(model.maxPaymentQty, 0),
          }"
          class="label limit text-truncate"
        >
          {{ formatNumber(model.maxPaymentQty, 0) }}
        </span>
      </div>
    </div>
  </div>

  <AddLimitPayment
    :visible="limitPayment"
    :prop-has-limit-payment="model.hasLimitPayment"
    :prop-max-payment-qty="model.maxPaymentQty"
    @close-popup-add-limit="closeLimitPayment"
    @add-limit="addLimitPayment"
  />
</template>

<style lang="scss">
.limit-number-payment {
  padding: 16px 24px;
  border-radius: 8px;
  max-width: 384px;
  min-height: 92px;
  margin-top: 12px;
  border: none;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .label {
      margin-right: 20px;

      &-header {
        display: block;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        color: #1c1c1c;
      }

      &-content {
        font-size: 12px;
        font-weight: 400;
        line-height: 16px;
        color: #6c6c6c;
        width: 280px;
        display: block;
        margin-top: 4px;
      }
    }

    .action {
      &-add-limit-number {
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        color: #2e6be5;
      }
    }
  }

  &-content {
    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .label {
        margin-right: 20px;

        &-header {
          display: block;
          font-size: 16px;
          font-weight: 600;
          line-height: 24px;
          color: #1c1c1c;
        }

        &-content {
          font-size: 12px;
          font-weight: 400;
          line-height: 16px;
          color: #6c6c6c;
          width: 280px;
          display: block;
          margin-top: 4px;
        }
      }

      .action {
        &-edit-limit-number {
          margin-right: 20px;
        }
      }
    }

    &-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16px;

      .label {
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        color: #404040;
      }

      .limit {
        width: 50px;
        text-align: right;
      }
    }
  }
}

.group-action-disabled {
  .action-edit-limit-number {
    pointer-events: none;
    cursor: default;
    opacity: 50%;
  }

  .action-delete-limit-number {
    pointer-events: none;
    cursor: default;
    opacity: 50%;
  }
}
</style>
