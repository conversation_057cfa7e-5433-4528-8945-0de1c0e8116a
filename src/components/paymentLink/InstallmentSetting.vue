<script setup lang="ts">
import ArrowDown from "@/assets/icons/arrow-down.svg"
import PaymentLinkClose from "@/assets/icons/payment-link-close.svg"

import APPCONFIG from "@/appConfig"
import { banksInfo } from "@/data/paymentlink/banks-info"
import { useAuthService } from "@/services/authService"
import { usePaymentLinkService } from "@/services/paymentLinkService"
import { DataInstallments, InstallmentSettings, OnePayCards } from "@/types/apis"
import { SelectListItem } from "@/types/base"
import type AppMultipleSelectOverlayPanel from "@components/ui/AppMultipleSelectOverlayPanel.vue"
import { useToggle } from "@vueuse/core"
import { storeToRefs } from "pinia"
import { computed, ref } from "vue"
import { useI18n } from "vue-i18n"

defineProps({
  visible: Boolean,
})

const [installmentSettingModel, installmentSettingModelModifiers] = defineModel<InstallmentSettings[]>(
  "installmentSetting",
  {
    default: [],
  }
)
const [paymentMethodModel, paymentMethodModelModifiers] = defineModel<number>("paymentMethod", {
  default: [],
})
const [checkedPaymentModel, checkedPaymentModelModifiers] = defineModel<number[]>("checkedPayments", {
  default: [],
})

const { t } = useI18n()
const authService = useAuthService()
const { authMerchantProfileId } = storeToRefs(authService)
const { getInstallmentsTask } = usePaymentLinkService()
const banks = new banksInfo()

const dataInstallments = ref<DataInstallments[]>([])

const getCards = (cards: OnePayCards[], timeToInstallment: number): boolean[] => {
  const arr = ref<boolean[]>([])

  cards.forEach((element) => {
    element.times.forEach((el) => {
      if (el.time === timeToInstallment) arr.value.push(true)
    })
  })

  return arr.value
}

const getAllTime = (cards: OnePayCards[]): number[] => {
  const result = ref<number[]>([])
  cards.forEach((element) => {
    element.times.forEach((el) => {
      if (result.value.indexOf(el.time) < 0) result.value.push(el.time)
    })
  })

  if (modelPeriod.value.length > 0) {
    result.value.forEach((item) => {
      if (!modelPeriod.value.includes(item)) {
        result.value.splice(result.value.indexOf(item))
      }
    })
  }

  if (modelBanks.value.length > 0) {
    dataInstallments.value.forEach((element) => {
      if (!modelBanks.value.includes(element.bankId)) {
        element.allTimeOfBank = []
      }
    })
  }

  return result.value
}

const labelBank = computed(() =>
  modelBanks.value.length == 0 || modelBanks.value.length == dataInstallments.value.length
    ? isSelectedAllBank.value
      ? t("components-paymentLink-Installment.all-bank")
      : t("components-paymentLink-Installment.placeholder.bank")
    : listBanks.value
        .filter((item) => modelBanks.value.includes(item.value))
        .map((item) => item.label)
        .join(", ")
)

const labelPeriod = computed(() =>
  modelPeriod.value.length == 0 || modelPeriod.value.length == 7
    ? isSelectedAllPeriod.value
      ? t("components-paymentLink-Installment.all-period")
      : t("components-paymentLink-Installment.placeholder.period")
    : listItemsPeriod.value
        .filter((item) => modelPeriod.value.includes(item.value))
        .map((item) => item.label)
        .join(", ")
)

const addInstallment = () => {
  const data: InstallmentSettings[] = []

  dataInstallments.value.forEach((element) => {
    if (element.allTimeOfBank.length > 0) {
      data.push({
        bankId: element.bankId,
        times: element.allTimeOfBank,
      })
    }
  })

  installmentSettingModel.value = data

  if (data.length <= 0 && paymentMethodModel.value) {
    checkedPaymentModel.value.splice(checkedPaymentModel.value.indexOf(APPCONFIG.INSTALLMENT), 1)
  } else {
    if (!checkedPaymentModel.value.includes(APPCONFIG.INSTALLMENT))
      checkedPaymentModel.value.push(APPCONFIG.INSTALLMENT)
  }

  emit("closePopupSetting")
}

const listBanks = ref<SelectListItem[]>([])
const listItemsPeriod = ref<SelectListItem[]>([
  { label: t("components-paymentLink-Installment.3-month"), value: 3 },
  { label: t("components-paymentLink-Installment.6-month"), value: 6 },
  { label: t("components-paymentLink-Installment.9-month"), value: 9 },
  { label: t("components-paymentLink-Installment.12-month"), value: 12 },
  { label: t("components-paymentLink-Installment.15-month"), value: 15 },
  { label: t("components-paymentLink-Installment.18-month"), value: 18 },
  { label: t("components-paymentLink-Installment.24-month"), value: 24 },
])
const modelBanks = ref<any[]>([])
const modelPeriod = ref<any[]>([])
const isSelectedAllBank = ref<boolean>(true)
const isSelectedAllPeriod = ref<boolean>(true)

const emit = defineEmits(["closePopupSetting"])

const close = () => {
  emit("closePopupSetting")
}

const [isVisibled] = useToggle()

const onApplyBank = () => {
  if (modelBanks.value.length > 0) {
    const temporaryVariable = ref<string[]>([])
    const arrNumber = ref<number[]>([])

    modelBanks.value.forEach((bankId: string) => {
      for (let bank of dataInstallments.value) {
        if (temporaryVariable.value.indexOf(bank.bankId) > -1) continue

        if (bank.bankId !== bankId) {
          bank.allTimeOfBank = []
        } else {
          if (modelPeriod.value.length > 0) {
            modelPeriod.value.forEach((period: number) => {
              if (!arrNumber.value.includes(period)) {
                switch (period) {
                  case 3:
                    if (bank.threeMonth.length > 0) arrNumber.value.push(period)
                    break
                  case 6:
                    if (bank.sixMonth.length > 0) arrNumber.value.push(period)
                    break
                  case 9:
                    if (bank.nineMonth.length > 0) arrNumber.value.push(period)
                    break
                  case 12:
                    if (bank.twelveMonth.length > 0) arrNumber.value.push(period)
                    break
                  case 15:
                    if (bank.fifteenMonth.length > 0) arrNumber.value.push(period)
                    break
                  case 18:
                    if (bank.eighteenMonth.length > 0) arrNumber.value.push(period)
                    break
                  case 24:
                    if (bank.twentyFourMonth.length > 0) arrNumber.value.push(period)
                    break
                }
              }
            })

            bank.allTimeOfBank = arrNumber.value
          } else {
            bank.allTimeOfBank = getAllTime(bank.cards)
          }
        }
      }

      temporaryVariable.value.push(bankId)
    })
  } else {
    if (isSelectedAllBank.value) {
      dataInstallments.value.forEach((bank) => {
        bank.allTimeOfBank = getAllTime(bank.cards)
      })

      isSelectedAllPeriod.value = true
    } else {
      dataInstallments.value.forEach((bank) => {
        bank.allTimeOfBank = []
      })

      isSelectedAllPeriod.value = false
    }
  }
}

const onApplyPeriod = () => {
  if (modelPeriod.value.length > 0) {
    const arrNumber = ref<number[]>([])

    modelPeriod.value.forEach((period: number) => {
      dataInstallments.value.forEach((bank) => {
        bank.allTimeOfBank = bank.allTimeOfBank.filter((item) => item === period)
        if (!arrNumber.value.includes(period)) {
          arrNumber.value.push(period)
        }
      })
    })

    if (arrNumber.value.length > 0) {
      dataInstallments.value.forEach((bank) => {
        if (modelBanks.value.length > 0) {
          modelBanks.value.forEach((bankId: string) => {
            if (bank.bankId === bankId) {
              arrNumber.value.forEach((element) => {
                switch (element) {
                  case 3:
                    if (bank.threeMonth.length > 0) bank.allTimeOfBank.push(element)
                    break
                  case 6:
                    if (bank.sixMonth.length > 0) bank.allTimeOfBank.push(element)
                    break
                  case 9:
                    if (bank.nineMonth.length > 0) bank.allTimeOfBank.push(element)
                    break
                  case 12:
                    if (bank.twelveMonth.length > 0) bank.allTimeOfBank.push(element)
                    break
                  case 15:
                    if (bank.fifteenMonth.length > 0) bank.allTimeOfBank.push(element)
                    break
                  case 18:
                    if (bank.eighteenMonth.length > 0) bank.allTimeOfBank.push(element)
                    break
                  case 24:
                    if (bank.twentyFourMonth.length > 0) bank.allTimeOfBank.push(element)
                    break
                }
              })
            }
          })
        } else {
          bank.allTimeOfBank = arrNumber.value
        }
      })
    }
  } else {
    if (isSelectedAllPeriod.value) {
      dataInstallments.value.forEach((bank) => {
        bank.allTimeOfBank = getAllTime(bank.cards)
      })

      isSelectedAllBank.value = true
    } else {
      dataInstallments.value.forEach((bank) => {
        bank.allTimeOfBank = []
      })

      isSelectedAllBank.value = false
    }
  }
}

const refFilterBank = ref<InstanceType<typeof AppMultipleSelectOverlayPanel> | null>(null)
const refFilterPeriod = ref<InstanceType<typeof AppMultipleSelectOverlayPanel> | null>(null)

const isFocusBank = ref(false)
const toggleFilterBank = (event: any) => {
  refFilterBank.value?.toggle(event)
}

const isFocusPeriod = ref(false)
const toggleFilterPeriod = (event: any) => {
  refFilterPeriod.value?.toggle(event)
}

const filterBank = ref<string>()
const listBanksFiltered = computed(() => {
  const search = filterBank.value
  return listBanks.value.filter((item) => !search || item.label?.toLowerCase().includes(search.toLocaleLowerCase()))
})

const fetchData = async () => {
  const data = await getInstallmentsTask.perform({ merchantId: authMerchantProfileId.value })

  if (data.installments && data.installments.length > 0) {
    dataInstallments.value = data.installments.map((element) => {
      listBanks.value.push({
        label: banks.getNameByBankSwift(element.bank_id),
        value: element.bank_id,
      })

      return {
        bankId: element.bank_id,
        bankName: banks.getNameByBankSwift(element.bank_id),
        threeMonth: getCards(element.cards, 3),
        sixMonth: getCards(element.cards, 6),
        nineMonth: getCards(element.cards, 9),
        twelveMonth: getCards(element.cards, 12),
        fifteenMonth: getCards(element.cards, 15),
        eighteenMonth: getCards(element.cards, 18),
        twentyFourMonth: getCards(element.cards, 24),
        allTimeOfBank: getAllTime(element.cards),
        cards: element.cards,
      }
    })

    dataInstallments.value.sort((a, b) => (a.bankName > b.bankName ? 1 : b.bankName > a.bankName ? -1 : 0))
    listBanks.value.sort((a, b) => (a.label && b.label ? (a.label > b.label ? 1 : b.label > a.label ? -1 : 0) : 0))
  }

  if (installmentSettingModel.value.length > 0) {
    const temporaryVariable = ref<string[]>([])

    installmentSettingModel.value.forEach((element) => {
      modelBanks.value.push(element.bankId)

      element.times.forEach((time) => {
        if (!modelPeriod.value.includes(time)) modelPeriod.value.push(time)
      })

      for (let bank of dataInstallments.value) {
        if (temporaryVariable.value.indexOf(bank.bankId) > -1) continue

        if (bank.bankId === element.bankId) {
          bank.allTimeOfBank = element.times
        } else {
          bank.allTimeOfBank = []
        }
      }

      temporaryVariable.value.push(element.bankId)
    })
  }
}

const disabledBtnSave = computed(() => {
  return dataInstallments.value.all((item) => item.allTimeOfBank.length <= 0)
})
</script>

<template>
  <Dialog
    :visible="visible"
    modal
    class="installment-setting"
    :draggable="false"
    @show="fetchData"
  >
    <template #header>
      <div class="label">
        <span class="title">{{ $t("components-paymentLink-Installment.title") }}</span>
        <br />
        <span>{{ $t("components-paymentLink-Installment.content") }}</span>
      </div>
      <button type="button" class="btn-close" @click="close">
        <SvgIcon :src="PaymentLinkClose" />
      </button>
    </template>

    <div class="filter-bank-period">
      <div class="filter-bank">
        <label style="margin-bottom: 4px">{{ $t("components-paymentLink-Installment.bank-allow") }}</label>
        <Button
          v-tooltip="{ value: labelBank }"
          class="dropdown-banks"
          :class="{ 'filter-bank-focus': isFocusBank }"
          @click="toggleFilterBank"
        >
          <!-- <span class="text-truncate" style="width: 223px;text-align: left;"> -->
          <span class="text-truncate" style="width: 260px; text-align: left">
            {{ labelBank }}
          </span>
          <!-- <span v-if="modelBanks.length > 4" class="total-bank">+{{ modelBanks.length }}</span> -->
          <SvgIcon :src="ArrowDown" style="margin-left: 8px" />
        </Button>
        <DropdownMultipleSelectOverlayPanel
          ref="refFilterBank"
          v-model:model-value="modelBanks"
          v-model:visible="isVisibled"
          v-model:is-seclected-all="isSelectedAllBank"
          v-model:filter-value="filterBank"
          class="panel-installmentSetting--ddlFilterBank dropdown-with-filter-all-bank"
          :options="listBanksFiltered"
          option-label="label"
          option-value="value"
          :select-all-allowed="true"
          :select-all-label="$t('components-paymentLink-Installment.all-bank')"
          :select-all-value="[]"
          :auto-apply="false"
          :filter="true"
          :filter-placeholder="$t('components-paymentLink-Installment.placeholder-dropdown-bank')"
          :is-installment-setting="true"
          @apply="onApplyBank"
          @show="isFocusBank = true"
          @hide="isFocusBank = false"
        >
        </DropdownMultipleSelectOverlayPanel>
      </div>

      <div class="filter-period">
        <label style="margin-bottom: 4px">{{ $t("components-paymentLink-Installment.period-allow") }}</label>
        <Button
          v-tooltip="{ value: labelPeriod }"
          class="dropdown-periods"
          :class="{ 'filter-period-focus': isFocusPeriod }"
          @click="toggleFilterPeriod"
        >
          <span class="text-truncate" style="width: 180px; text-align: left">{{ labelPeriod }}</span>
          <SvgIcon :src="ArrowDown" style="margin-left: 22px" />
        </Button>
        <DropdownMultipleSelectOverlayPanel
          ref="refFilterPeriod"
          v-model:model-value="modelPeriod"
          v-model:visible="isVisibled"
          v-model:is-seclected-all="isSelectedAllPeriod"
          class="panel-installmentSetting--ddlFilterPeriod"
          :options="listItemsPeriod"
          option-label="label"
          option-value="value"
          :select-all-allowed="true"
          :select-all-label="$t('components-paymentLink-Installment.placeholder-dropdown-period')"
          :select-all-value="[]"
          :auto-apply="false"
          :is-installment-setting="true"
          @apply="onApplyPeriod"
          @show="isFocusPeriod = true"
          @hide="isFocusPeriod = false"
        >
        </DropdownMultipleSelectOverlayPanel>
      </div>
    </div>

    <DataTable
      :value="dataInstallments"
      class="tbl-installment-setting"
      scrollable
      scroll-height="237px"
      show-gridlines
    >
      <template #loading>
        <AppProgressSpinner></AppProgressSpinner>
      </template>

      <ColumnGroup type="header">
        <Row>
          <Column header="Id" :rowspan="2" field="bankId" hidden />
          <Column :header="$t('components-paymentLink-Installment.bank')" :rowspan="2" field="bankName" />
          <Column
            :header="$t('components-paymentLink-Installment.installment-period')"
            :colspan="7"
            header-class="border-bottom-0"
          />
        </Row>

        <Row>
          <Column :header="$t('components-paymentLink-Installment.3-month')" field="threeMonth" />
          <Column :header="$t('components-paymentLink-Installment.6-month')" field="sixMonth" />
          <Column :header="$t('components-paymentLink-Installment.9-month')" field="nineMonth" />
          <Column :header="$t('components-paymentLink-Installment.12-month')" field="twelveMonth" />
          <Column :header="$t('components-paymentLink-Installment.15-month')" field="fifteenMonth" />
          <Column :header="$t('components-paymentLink-Installment.18-month')" field="eighteenMonth" />
          <Column :header="$t('components-paymentLink-Installment.24-month')" field="twentyFourMonth" />
        </Row>
      </ColumnGroup>

      <Column field="bankId" hidden />
      <Column field="bankName" class="col-bank-name" />
      <Column field="threeMonth" class="col-bank-item">
        <template #body="slotProps">
          <div v-if="slotProps.data.threeMonth.length > 0">
            <Checkbox v-model="slotProps.data.allTimeOfBank" :value="3" />
          </div>
        </template>
      </Column>
      <Column field="sixMonth" class="col-bank-item">
        <template #body="slotProps">
          <div v-if="slotProps.data.sixMonth.length > 0">
            <Checkbox v-model="slotProps.data.allTimeOfBank" :value="6" />
          </div>
        </template>
      </Column>
      <Column field="nineMonth" class="col-bank-item">
        <template #body="slotProps">
          <div v-if="slotProps.data.nineMonth.length > 0">
            <Checkbox v-model="slotProps.data.allTimeOfBank" :value="9" />
          </div>
        </template>
      </Column>
      <Column field="twelveMonth" class="col-bank-item">
        <template #body="slotProps">
          <div v-if="slotProps.data.twelveMonth.length > 0">
            <Checkbox v-model="slotProps.data.allTimeOfBank" :value="12" />
          </div>
        </template>
      </Column>
      <Column field="fifteenMonth" class="col-bank-item">
        <template #body="slotProps">
          <div v-if="slotProps.data.fifteenMonth.length > 0">
            <Checkbox v-model="slotProps.data.allTimeOfBank" :value="15" />
          </div>
        </template>
      </Column>
      <Column field="eighteenMonth" class="col-bank-item">
        <template #body="slotProps">
          <div v-if="slotProps.data.eighteenMonth.length > 0">
            <Checkbox v-model="slotProps.data.allTimeOfBank" :value="18" />
          </div>
        </template>
      </Column>
      <Column field="twentyFourMonth" class="col-bank-item">
        <template #body="slotProps">
          <div v-if="slotProps.data.twentyFourMonth.length > 0">
            <Checkbox v-model="slotProps.data.allTimeOfBank" :value="24" />
          </div>
        </template>
      </Column>
    </DataTable>

    <template #footer>
      <Button
        class="btn-instalment btn-cancel"
        :label="$t('components-paymentLink-Installment.btn-cancel')"
        @click="close"
      />

      <Button
        class="btn-instalment btn-confirm"
        :label="$t('components-paymentLink-Installment.btn-save')"
        :class="{ 'btn-save-disabled': disabledBtnSave }"
        @click="addInstallment"
      />
    </template>
  </Dialog>
</template>

<style lang="scss">
.installment-setting {
  min-width: 780px;
  height: auto;

  .p-dialog-header {
    padding: 20px 24px;
    align-items: flex-start;
    height: 100px;

    .label {
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      color: #404040;

      .title {
        font-size: 24px;
        font-weight: 600;
        line-height: 36px;
        color: #1c1c1c;
      }
    }

    .btn-close {
      width: 40px;
      height: 40px;
      padding: 8px;
    }

    .p-dialog-header-icons {
      display: none;
    }
  }

  .p-dialog-footer {
    padding: 20px 24px;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .btn-instalment {
      height: 40px;
      border-radius: 4px;
      padding: 10px 24px;

      .p-button-label {
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
      }
    }

    .btn-cancel {
      min-width: 100px;
      color: #404040;
      margin-right: 20px;
      border: none;
      background-color: #f5f5f5;
    }

    .btn-confirm {
      min-width: 120px;
      color: #ffffff;
      border: none;
      background-color: #2e6be5;
    }

    .btn-save-disabled {
      background-color: #e6e6e6 !important;
      color: #bababa !important;
      pointer-events: none;
    }
  }

  .p-dialog-content {
    overflow: hidden;

    .tbl-installment-setting {
      th {
        //border-width: thin;
        text-align: center;

        .p-column-header-content {
          justify-content: center;
        }
      }

      // td {
      //border-width: thin;
      // }
    }

    .filter-bank-period {
      margin-bottom: 16px;
      display: flex;
      align-items: center;

      .filter-bank {
        margin-right: 20px;
        display: grid;

        .dropdown-banks {
          min-width: 320px;
          background-color: #ffffff;
          border: 1px solid #dcdcdc;
          padding: 10px 16px;
          border-radius: 4px;
          color: #404040;
          display: flex;
          justify-content: space-between;
        }
      }

      .filter-period {
        display: grid;

        .dropdown-periods {
          min-width: 240px;
          background-color: #ffffff;
          border: 1px solid #dcdcdc;
          padding: 10px 16px;
          border-radius: 4px;
          color: #404040;
          display: flex;
          justify-content: space-between;
        }
      }

      label {
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        color: #404040;
      }
    }
  }

  .col-bank-name {
    text-align: left;
    margin-left: 16px;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: #404040;
  }

  .col-bank-item {
    text-align: center;

    .p-checkbox {
      width: 18px;
      height: 18px;

      .p-checkbox-box {
        height: 18px;
      }
    }
  }
}

.panel-installmentSetting--ddlFilterBank {
  min-width: 320px;
}

.panel-installmentSetting--ddlFilterPeriod {
  min-width: 240px;
}

// .installment-setting-dialog {
//   max-height: 476px;
//   width: 320px;
//   overflow-y: hidden !important;

//   .p-overlaypanel-content {
//     padding: 16px !important;
//     position: relative;
//     max-height: inherit;
//     padding-right: 0 !important;

//     .p-form-check-group {
//       max-height: inherit;
//       overflow-y: auto;
//       padding: 0 !important;
//       gap: 0 !important;
//       padding-bottom: 60px !important;

//       .group-search-banks {
//         height: 40px;
//         margin-bottom: 20px;

//         .p-inputgroup-addon {
//           padding-right: 0.5rem;
//           border-right: none;
//         }

//         .text-search-banks {
//           padding-left: 0;

//           &:focus {
//             outline: none;
//           }

//           &::placeholder {
//             color: #BABABA  ;
//             opacity: 1; /* Firefox */
//           }
//         }
//       }

//       .p-form-check {
//         margin-bottom: 20px;
//       }
//     }

//     .p-button {
//       position: absolute;
//       bottom: 2px;
//       left: 0;
//       max-width: calc(100% - 32px);
//       margin-left: 14px;
//     }
//   }
// }

// .installment-setting-period-dialog {
//   width: 240px;

//   .p-overlaypanel-content {
//       padding: 16px !important;

//       .p-form-check-group {
//         padding: 0 !important;
//         gap: 0 !important;

//         .p-form-check {
//           margin-bottom: 20px;
//         }
//       }
//     }
// }

.total-bank {
  width: 31px;
  height: 24px;
  background-color: #f5f5f5;
  padding: 4px;
  border-radius: 4px;
  font-weight: 400;
  color: #6c6c6c;
  font-size: 12px;
  line-height: 16px;
  margin-left: 6px;
}

.dropdown-with-filter-all-bank {
  .select-item-header {
    .p-inputtext-tms {
      &::placeholder {
        color: #bababa;
      }
    }
  }
}

.filter-bank-focus,
.filter-period-focus {
  border: 1px solid #2e6be5 !important;
}
</style>
