<script setup lang="ts">
import CopyLink from "@/assets/icons/add-payment-link-success-copy.svg"
import AddSuccess from "@/assets/icons/add-payment-link-success.svg"
import DashboardDownloadQrCode from "@/assets/icons/dashboard-download-qr-code.svg"
import PaymentLinkClose from "@/assets/icons/payment-link-close.svg"

import { GetByIdPaymentLinkOutput } from "@/apis/paymentLinkApi"
import APPCONFIG from "@/appConfig"
import { useAuthService } from "@/services/authService"
import { useToastService } from "@/services/toastService"
import { getImgSrc } from "@/utils/appUtil"
import { formatCurrency, formatDateTime, formatNumber } from "@/utils/formatUtil"
import { useClipboard } from "@vueuse/core"
import { useQRCode } from "@vueuse/integrations/useQRCode"
import { ref } from "vue"

import TemplateQrCodeDownload from "../TemplateQrCodeDownload.vue"

const { authMerchantProfile } = useAuthService()
const model = defineModel<GetByIdPaymentLinkOutput | undefined>({ required: true })
const text = ref(`${APPCONFIG.APP_PL_HOST}/${model.value?.routerUri}`)
const qrcode = useQRCode(text, {
  errorCorrectionLevel: "Q",
  type: "image/jpeg",
  margin: 0,
})
const dudDate = formatDateTime(model.value?.dueDate)
const downloadQrSuccessDialog = ref<InstanceType<typeof TemplateQrCodeDownload> | null>(null)

defineProps({
  visible: Boolean,
})

const emit = defineEmits(["closePopupSuccess", "coppyLink"])

const { copy } = useClipboard()
const toastService = useToastService()

const closeSuccess = () => {
  emit("closePopupSuccess")
}

const coppyLink = async () => {
  if (!model.value) return

  await copy(text.value)
  toastService.copySuccess({})
}

const logo = getImgSrc(authMerchantProfile?.logoFileId)

const downloadQr = () => {
  const refQrCodeDownload = downloadQrSuccessDialog.value
  if (!refQrCodeDownload) return

  refQrCodeDownload.downloadQr()
}
</script>

<template>
  <Dialog :visible="visible" modal class="dialog-add-success" :draggable="false">
    <template #header>
      <div class="label-success">
        <SvgIcon :src="AddSuccess" />
        <span>{{ $t("components-paymentLink-AddSuccessPayment.title") }}</span>
      </div>

      <button type="button" class="btn-close" @click="closeSuccess">
        <SvgIcon :src="PaymentLinkClose" />
      </button>
    </template>

    <span class="link-name-success">{{ model?.name }}</span>

    <div class="payment-limit-success">
      <div v-if="model?.hasPresetAmount" class="payment-success">
        <span>{{ $t("components-paymentLink-AddSuccessPayment.payment-amount") }}</span>
        <span>{{ formatCurrency(model.paymentAmount, model.paymentCurrency) }}</span>
      </div>
      <div v-if="model?.hasLimitPayment" class="limit-success">
        <span>{{ $t("components-paymentLink-AddSuccessPayment.limit-payment") }}</span>
        <span>{{ formatNumber(model.maxPaymentQty, 0) }}</span>
      </div>
    </div>

    <div class="coppy-link-success">
      <input class="my-link" readonly type="text" :value="text" />
      <SvgIcon :src="CopyLink" class="cursor-pointer" @click="coppyLink" />
    </div>

    <div class="qr-success">
      <img :src="qrcode" alt="QR Code" style="width: 180px; height: 180px" />
      <div class="download-group">
        <Button class="btn-download-qr-success-payment" @click="downloadQr()">
          <SvgIcon :src="DashboardDownloadQrCode" />
          <span class="download-action">{{ $t("components-paymentLink-AddSuccessPayment.download-qr") }}</span>
        </Button>
      </div>
    </div>

    <Teleport to="body">
      <TemplateQrCodeDownload
        ref="downloadQrSuccessDialog"
        :logo="logo"
        :link-name="model?.name"
        :qrcode="qrcode"
        :payment-amount="model?.paymentAmount"
        :due-date="dudDate"
        :payment-currency="model?.paymentCurrency"
      />
    </Teleport>
  </Dialog>
</template>

<style lang="scss">
.dialog-add-success {
  border: none;
  border-radius: 8px;
  max-width: 480px;

  .p-dialog-header {
    background-color: #00bf5f;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
    align-items: flex-start;
    padding: 16px 16px 24px 0;

    .label-success {
      display: grid;
      justify-items: center;
      justify-content: center;
      width: 100%;
      margin-left: 40px;

      svg {
        margin-top: 8px;
      }

      span {
        font-size: 24px;
        font-weight: 600;
        line-height: 36px;
        color: #ffffff;
        margin-top: 12px;
      }
    }

    .btn-close {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      padding: 8px;
    }

    .p-dialog-header-icons {
      display: none;
    }
  }

  .p-dialog-content {
    padding: 24px 24px 0 24px;

    .link-name-success {
      font-size: 20px;
      font-weight: 500;
      line-height: 28px;
      color: #1c1c1c;

      display: block;
      
      //word-wrap: break-word;
      //word-break: break-word;
      @include global.allow_line_break();
    }

    .payment-limit-success {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 12px;

      .payment-success,
      .limit-success {
        display: grid;

        span:first-child {
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          color: #6c6c6c;
        }

        span {
          font-size: 16px;
          font-weight: 400;
          line-height: 24px;
          color: #1c1c1c;
        }
      }
    }

    .coppy-link-success {
      display: flex;
      align-items: center;
      margin-top: 20px;

      .my-link {
        min-width: 400px;
        height: 36px;
        border-radius: 4px;
        background-color: #e6f9ef;
        padding: 8px 16px;
        border: none;
        outline: 1px solid #8ae2b5;
        color: #00bf5f;
        margin-right: 12px;
      }
    }

    .qr-success {
      margin-top: 20px;
      display: grid;
      justify-content: center;
      justify-items: center;

      .download-group {
        min-width: 119px;
        min-height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 0.75rem;
        margin-bottom: 24px;

        .download-action {
          text-decoration: none;
          font-weight: 500;
          color: #2e6be5;
          margin-left: 8px;
        }
      }
    }
  }
}

.btn-download-qr-success-payment {
  background-color: #ffffff;
  padding: 0;
}
</style>
