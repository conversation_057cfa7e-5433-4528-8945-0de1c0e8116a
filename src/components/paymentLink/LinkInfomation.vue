<script setup lang="ts">
import Requied from "@/assets/icons/tms-requied.svg"

import APPCONFIG from "@/appConfig"
import { DEFAULT_VALUE_NUMBER_PAYMENT_LINK } from "@/app.const"
import { useAuthService } from "@/services/authService"
import { usePaymentLinkService } from "@/services/paymentLinkService"
import type { PaymentLink } from "@/types/apis"
import { computed, ref, watch } from "vue"

const store = usePaymentLinkService()
const { authMerchantProfile } = useAuthService()
const { form, formValidator, stringToSlug } = store
const errorFields = computed(() => formValidator.errorFields)
const checkedPayments = ref<number[]>([])
const model = defineModel<PaymentLink>({ required: true })
const paymentMethods = await store.getAllPaymentMethods()

const [isEditModel, isEditModelModifiers] = defineModel<boolean>("isEdit", {
  default: false,
})

watch(
  isEditModel,
  () => {
    form.isStopAutoGenUri = isEditModel.value
  },
  {
    immediate: true,
  }
)

paymentMethods.forEach((element) => {
  if (!authMerchantProfile) return
  if (!authMerchantProfile.cfgPaymentMethods) return

  if ((authMerchantProfile.cfgPaymentMethods & element.id) === 0) {
    paymentMethods.splice(paymentMethods.indexOf(element), 1)
  }
})

if (model.value.paymentMethods > 0) {
  paymentMethods.forEach((element) => {
    if ((element.id & model.value.paymentMethods) > 0) {
      checkedPayments.value.push(element.id)
    }
  })
} else {
  paymentMethods.forEach((element) => {
    checkedPayments.value.push(element.id)
  })

  checkedPayments.value.forEach((element) => {
    model.value.paymentMethods = model.value.paymentMethods | element
  })
}

const itemPaymentMethod = ref(
  paymentMethods.map((item) => {
    return {
      id: item.id,
      code: item.code,
      name: item.name,
      ordinal: item.ordinal,
      active: item.active,
      isDisabled: checkedPayments.value.length <= 1,
    }
  })
)

const installmentSetting = ref(false)

const paymentMethod = () => {
  itemPaymentMethod.value.forEach((element) => {
    if (checkedPayments.value.includes(element.id)) {
      if (checkedPayments.value.length <= 1) element.isDisabled = true
      else element.isDisabled = false
    }
  })

  model.value.paymentMethods = DEFAULT_VALUE_NUMBER_PAYMENT_LINK

  checkedPayments.value.forEach((element) => {
    model.value.paymentMethods = model.value.paymentMethods | element
  })
}

const openInstallmentSetting = () => {
  installmentSetting.value = true
}

const closeInstallmentSetting = () => {
  checkedPayments.value.forEach((element) => {
    model.value.paymentMethods = model.value.paymentMethods | element
  })

  installmentSetting.value = false
}

watch(
  () => form.name,
  async (name) => {
    model.value.name = name ?? ""

    if (!form.isStopAutoGenUri)
      form.routerUri = getRouterUri(name)
  }
)

watch(
  () => form.routerUri,
  async (routerUri) => {
    model.value.routerUri = routerUri ? routerUri : ""
  }
)

const getRouterUri = (name?: string) => {
  if (name) {
    const arrName = stringToSlug(name).trim().split(" ")
    if (arrName.length > 0) {
      return arrName.join("-").replaceAll("--", "-")
    }
  }

  return ""
}

const stopAutoGenUri = (e: Event) => {
  // const el = e.target as HTMLInputElement | null
  // const value = el?.value || ""
  const value = form.routerUri
  // khi có thực hiện enter router bằng tay thì dừng xử lý tự động generate router
  if (value && value.length > 0)
    form.isStopAutoGenUri = true
  else
    form.isStopAutoGenUri = false
}
</script>

<template>
  <div class="card link-information">
    <div class="link-information-header">{{ $t("components-paymentLink-LinkInfomation.title") }}</div>
    <div class="link-information-body">
      <div class="mb-3">
        <div class="group-label mb-1">
          <label for="name" class="mr-1 tms-label-add-edit">
            {{ $t("components-paymentLink-LinkInfomation.link-name") }}
          </label>
          <div class="icon-requied">
            <SvgIcon :src="Requied" />
          </div>
        </div>
        <InputTextClearable
          v-model="form.name"
          size="large"
          type="text"
          class="tms-InputText tms-InputText-name"
          :invalid="errorFields?.name?.any() ?? false"
          autocomplete="off"
          required
          maxlength="200"
        />

        <div v-if="errorFields?.name?.length" class="text-error">
          {{ errorFields.name[0].message }}
        </div>
      </div>

      <div class="mb-3">
        <div class="group-label mb-1">
          <label for="url" class="mr-1 tms-label-add-edit">
            {{ $t("components-paymentLink-LinkInfomation.link-url") }}
          </label>
          <div class="icon-requied">
            <SvgIcon :src="Requied" />
          </div>
        </div>
        <InputGroup>
          <InputGroupAddon class="tms-InputText-current-url">{{ APPCONFIG.APP_PL_HOST }}</InputGroupAddon>
          <InputTextClearable
            v-model="form.routerUri"
            type="text"
            class="tms-InputText tms-InputText-url"
            :invalid="errorFields?.routerUri?.any() ?? false"
            autocomplete="off"
            required
            maxlength="250"
            @input="stopAutoGenUri"
          />
        </InputGroup>

        <div v-if="errorFields?.routerUri?.length" class="text-error">
          {{ errorFields.routerUri[0].message }}
        </div>
      </div>

      <div class="mb-3">
        <div class="d-flex mb-1 justify-content-between">
          <label for="description" class="tms-label-add-edit">
            {{ $t("components-paymentLink-LinkInfomation.description") }}
          </label>
          <span class="text-count">{{ `${model.description?.length ?? 0}/500` }}</span>
        </div>
        <Textarea
          id="description"
          v-model="model.description"
          :maxlength="500"
          class="tms-InputText tms-InputText-description"
          @focusout="() => (model.description = model.description?.trim())"
        />
        <!-- <TextareaMaxLength id="description" v-model="model.description" :maxlength="500" class="tms-InputText tms-InputText-description"></TextareaMaxLength> -->
      </div>

      <div class="mb-3">
        <div class="group-label mb-1">
          <label class="mr-1 tms-label-add-edit">
            {{ $t("components-paymentLink-LinkInfomation.payment-method.title") }}
          </label>
          <div class="icon-requied">
            <!-- <Requied /> -->
            <SvgIcon :src="Requied" />
          </div>
        </div>
        <div class="group-check-box">
          <div v-for="item in itemPaymentMethod" :key="item.ordinal" class="tms-group-check-box">
            <div class="group-item">
              <input
                :id="item.code"
                v-model="checkedPayments"
                :value="item.id"
                :disabled="item.isDisabled"
                class="form-check-input"
                type="checkbox"
                @change="paymentMethod"
              />
              <label class="form-check-label" :for="item.code">
                {{ $t(`components-paymentLink-LinkInfomation.payment-method.${item.code}`) }}
              </label>
              <a v-if="item.id === 64" href="javascript:void(0)" class="detail" @click="openInstallmentSetting">
                ({{ $t("components-paymentLink-LinkInfomation.installment-setting-details") }})
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <InstallmentSetting
    v-if="installmentSetting"
    v-model:payment-method="model.paymentMethods"
    v-model:installment-setting="model.installmentSettings"
    v-model:checked-payments="checkedPayments"
    :visible="installmentSetting"
    @close-popup-setting="closeInstallmentSetting"
  />
</template>

<style lang="scss">
.link-information {
  padding: 24px;
  border: none;

  .link-information-header {
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
    color: #1c1c1c;
  }

  .link-information-body {
    margin-top: 16px;

    .group-label {
      display: flex;

      .icon-requied {
        width: 8px;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        margin-top: 4px;
      }
    }

    .tms-label-add-edit {
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      color: #404040;
    }

    .tms-InputText {
      min-width: 539px;
    }

    .tms-InputText-current-url {
      min-width: 169px;
      background-color: #e6f9ef;
      color: #00bf5f;
      border-color: #8ae2b5;
    }

    .tms-InputText-url {
      min-width: 370px;
    }

    .tms-InputText-description {
      padding-top: 10px;
      padding-bottom: 8px;
      height: 76px;
      width: 100%;
    }

    .group-check-box {
      display: flex;
      justify-content: space-between;

      .tms-group-check-box {
        .group-item {
          height: 24px;

          .form-check-input {
            margin: 3px 11px 3px 3px;
            width: 18px;
            height: 18px;
          }

          .form-check-label {
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            color: #404040;
          }
        }
      }

      .tms-group-check-box:not(:first-child) {
        margin-right: 85px;
      }
    }
  }

  .clear-icon {
    position: absolute;
    right: 40px;
  }

  .detail {
    margin-left: 8px;
    text-decoration: none;
    color: #2e6be5;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
  }

  .text-count {
    color: var(--text-disabled2, #bababa);
    /* Inter/B3/12_Regular */
    font-size: var(--Typeface-size-sm, 0.75rem);
    font-style: normal;
    font-weight: 400;
    line-height: 1rem; /* 133.333% */
  }
}

.border-error {
  border: 1px solid #d82039;
}

.tms-InputText-url.border-error {
  border-left: none;
}
</style>
