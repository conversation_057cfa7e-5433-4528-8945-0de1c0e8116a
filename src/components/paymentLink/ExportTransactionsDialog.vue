<script lang="ts" setup>
import APPCONFIG from "@/appConfig"
import { DateRangModel } from "@/types/base"
import { formatRangeDateTime } from "@/utils/formatUtil"
import moment from "moment"
import type Dialog from "primevue/dialog"
import type { DialogProps } from "primevue/dialog"
import { computed } from "vue"

defineOptions({
  inheritAttrs: false,
})

export interface ExportTransactionsDialog extends /* @vue-ignore */ DialogProps {}

const props = withDefaults(defineProps<ExportTransactionsDialog>(), {
  header: "Download transaction",
})

const [visibleModel] = defineModel<boolean | undefined>("visible", {
  default: false,
})
const [modelValueModel] = defineModel<DateRangModel>("modelValue", {
  default: {
    startDate: null,
    endDate: null,
  },
})

const emit = defineEmits<{
  togger: [state: boolean]
  show: []
  hide: []
  clickDownload: [search: DateRangModel]
}>()

function toggleVisible(state: boolean) {
  visibleModel.value = state
}

async function onClickBtnDownload() {
  if (modelValueModel.value?.startDate && modelValueModel.value?.endDate) emit("clickDownload", modelValueModel.value)
}

const displayTextValue = computed(() => {
  const dateRange = modelValueModel.value

  return formatRangeDateTime(dateRange.startDate, dateRange.endDate)
})
</script>

<template>
  <Dialog
    v-model:visible="visibleModel"
    class="com-ExportTransactionsDialog"
    modal
    :header="$t('components-export-transactions-dialog.header')"
    v-bind="{ ...$attrs, ...$props }"
    @show="emit('show')"
    @hide="emit('hide')"
  >
    <div class="panel-ExportTransactionsDialog-content">
      <div class="dialog-title">{{ $t("components-export-transactions-dialog.title") }}</div>

      <DateRangPickerInline
        v-model:model-value="modelValueModel"
        :auto-apply="true"
        :ranges="false"
        :max-date="moment().endOf('day').toDate()"
        :limit-days="APPCONFIG.MAX_SELECTED_DAYS_IN_DATERANGE"
        :time-picker="true"
        :time-picker24-hour="false"
        :time-picker-seconds="true"
      ></DateRangPickerInline>

      <div class="divider divider--horizontal"></div>

      <div class="panel-ExportTransactionsDialog-footer">
        <span>{{ displayTextValue }}</span>
        <div class="buttons-group">
          <Button
            severity="cancel"
            class="btn-cancel"
            :label="$t('components-export-transactions-dialog.actions.cancel')"
            @click="toggleVisible(false)"
          ></Button>
          <Button
            severity="primary"
            :label="$t('components-export-transactions-dialog.actions.download')"
            @click="onClickBtnDownload"
          ></Button>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<style lang="scss">
.com-ExportTransactionsDialog {
  width: 36.25rem;

  .dialog-title {
    color: var(--text-body, #404040);
    /* Inter/B2/14_Regular */
    font-size: var(--Typeface-size-md, 0.875rem);
    font-style: normal;
    font-weight: 400;
    line-height: 1.25rem; /* 142.857% */

    margin-bottom: 1.5rem;
  }

  .dialog-footer-daterange {
    color: var(--text-tertiary, #6c6c6c);
    /* Inter/B2/14_Regular */
    font-size: var(--Typeface-size-md, 0.875rem);
    font-style: normal;
    font-weight: 400;
    line-height: 1.25rem; /* 142.857% */
  }

  .panel-ExportTransactionsDialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .buttons-group {
    display: flex;
    gap: 0.875rem;
  }
}
</style>
