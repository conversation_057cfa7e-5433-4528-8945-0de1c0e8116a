<script lang="ts" setup>
import CopyIcon from "@assets/icons/copy-qrcode.svg"

import { useAuthService } from "@/services/authService"
import { usePaymentLinkService } from "@/services/paymentLinkService"
import { useToastService } from "@/services/toastService"
import { getImgSrc } from "@/utils/appUtil"
import { useClipboard } from "@vueuse/core"
import { useQRCode } from "@vueuse/integrations/useQRCode"
import type { DialogProps } from "primevue/dialog"
import { ref, watch } from "vue"

import TemplateQrCodeDownload from "@/components/TemplateQrCodeDownload.vue"

const { authMerchantProfile } = useAuthService()
const { copy } = useClipboard()
const toastService = useToastService()
const { getPaymentLinkFullRouterUri } = usePaymentLinkService()
const downloadQrDialog = ref<InstanceType<typeof TemplateQrCodeDownload> | null>(null)

defineOptions({
  inheritAttrs: false,
})

export interface DownloadQrDialogValueType {
  name: string
  routerUri: string
  paymentAmout?: number
  paymentCurrency?: string
  dueDate?: string
}

export interface DownloadQrDialogProps extends /* @vue-ignore */ DialogProps {
  header?: string
  value?: DownloadQrDialogValueType
}

const props = withDefaults(defineProps<DownloadQrDialogProps>(), {
  value(props) {
    return {
      name: "Ve xem phim 2D CGV Metropolis",
      routerUri: "www.secure.onepay.vn/cgv/vexemphim/2D/",
    }
  },
})

const routerUri = ref("")
const linkName = ref("")
const logo = getImgSrc(authMerchantProfile?.logoFileId)
const paymentAmount = ref(0)
const paymentCurrency = ref("")
const dueDate = ref("")

const qrcode = useQRCode(routerUri, {
  errorCorrectionLevel: "Q",
  type: "image/jpeg",
  margin: 0,
})

watch(
  () => props.value,
  (value, old) => {
    routerUri.value = getPaymentLinkFullRouterUri(value?.routerUri ?? "")
    console.log("routerUri", routerUri.value)
    linkName.value = value?.name ?? ""
    paymentAmount.value = value?.paymentAmout ?? 0
    paymentCurrency.value = value?.paymentCurrency ?? ""
    dueDate.value = value?.dueDate ?? ""
  }
)

async function copyLinkUri(input?: string) {
  const link = input ?? routerUri.value

  console.log(`Copy '${link}'`)

  await copy(link)

  toastService.copySuccess({})
}

const downloadQr = () => {
  const refQrCodeDownload = downloadQrDialog.value
  if (!refQrCodeDownload) return

  refQrCodeDownload.downloadQr()
}
</script>

<template>
  <Dialog class="p-dialog-download-qr" modal v-bind="{ ...$attrs }">
    <template #header>
      <div class="p-dialog-title w-100 text-center">
        {{ $t('components-download-qr-dialog.download-qr-header') }}
      </div>
    </template>
    <div class="panel-download-qr">
      <div class="panel-download-qr-name">
        <span class="lbl-link-name">{{ props.value.name }}</span>
      </div>
      <div class="panel-download-qr-routerUri">
        <InputText class="txt-link-routerUri" type="text" :model-value="routerUri" readonly />
        <SvgIcon :src="CopyIcon" class="ml-auto user-clickable" @click.stop="copyLinkUri()" />
      </div>
      <div class="panel-download-qr-img download-qr-dialog">
        <QrCode :value="routerUri" @downloaded="downloadQr"></QrCode>
      </div>
    </div>

    <TemplateQrCodeDownload
      ref="downloadQrDialog"
      :logo="logo"
      :link-name="linkName"
      :qrcode="qrcode"
      :payment-amount="paymentAmount"
      :due-date="dueDate"
      :payment-currency="paymentCurrency"
    />
  </Dialog>
</template>

<style lang="scss">
.p-dialog-download-qr {
  width: 480px;

  .panel-download-qr {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .panel-download-qr-name {
      margin-bottom: 12px;
    }

    .panel-download-qr-routerUri {
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;

      margin-bottom: 24px;
    }
  }

  .lbl-link-name {
    line-height: 24px; /* 150% */
    font-size: var(--Typeface-size-lg, 16px);

    display: block;

    //word-wrap: break-word;
    //word-break: break-word;
    @include global.allow_line_break();
  }

  .txt-link-routerUri {
    border-color: #8ae2b5;
    background-color: #e6f9ef;
    color: var(--text-success, #00bf5f);
    margin-right: 12px;
  }
}

.download-qr-dialog {
  .img-qr-code {
    width: 180px;
    height: 180px;
  }
}
</style>
