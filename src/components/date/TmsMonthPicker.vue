<script setup lang="ts">
import { ref } from 'vue';
import VueDatePicker from '@vuepic/vue-datepicker';
import moment from 'moment';

import DashboardCalendar from '@/assets/icons/dashboard-calendar.svg';

const date = ref({month: moment().get('month'), year: moment().get('year')});

const emit = defineEmits(['autoApply'])

const format = (date: any) => {
  return moment(date).format('MMM-YYYY');
}

const handleMonthYear = ({ instance, month, year }) => {
  emit('autoApply', month)
}
</script>

<template>
  <VueDatePicker
    ref="refMonthPicker"
    v-model="date"
    :enable-time-picker="false"
    :clearable="false"
    month-picker
    auto-apply
    class="month-picker"
    week-start="0"
    :format="format"
    @update-month-year="handleMonthYear"
  >
    <template #dp-input="{ value }">
      <div class="input-value">
        <input type="text" :value="value" readonly />
        <i class="input-value-icon">
          <!-- <DashboardCalendar /> -->
          <SvgIcon :src="DashboardCalendar" />
        </i>
      </div>
    </template>
  </VueDatePicker>
</template>

<style lang="scss">
.month-picker {
  .dp__outer_menu_wrap {
    width: 330px;
    height: 255px;
    top: auto !important;
    left: calc(220px - 340px) !important;

    .dp__menu_index {
      padding: 20px;
      border-radius: 12px;
      width: 100%;
      height: 100%;
    }

    .dp__arrow_bottom {
      bottom: unset;
      height: 0;
      width: 0;
      left: 0
    }

    .dp__arrow_top {
      top: unset;
      height: 0;
      width: 0;
      left: 0
    }

    .dp__instance_calendar {
      .dp--menu--inner-stretched {
        padding: unset;

        .dp__overlay {
          width: 300px !important;
          height: 215px !important;

          .dp__overlay_container {
            overflow: hidden;

            .dp__selection_grid_header {
              .dp--year-mode-picker {
                height: 32px;

                .dp--arrow-btn-nav {
                  width: 32px;
                  height: 32px;
                  border-radius: 4px;
                  padding: 6px;
                  border: 1px solid #E6E6E6;
                  background-color: #FFFFFF;

                  .dp__inner_nav {
                    width: 20px;
                    height: 20px;
                    color: #404040;
                    border-radius: unset;

                    &:hover {
                      background: unset;
                      color: unset;
                    }

                    svg {
                      display: none;
                    }
                  }
                }

                .dp--arrow-btn-nav:first-child {
                  .dp__inner_nav {
                    background-image: url('@/assets/icons/dashboard-previous.svg');
                    background-repeat: no-repeat, repeat;
                    margin-left: 6px;
                    background-position-y: center;
                  }
                }

                .dp--arrow-btn-nav:last-child {
                  .dp__inner_nav {
                    background-image: url('@/assets/icons/dashboard-next.svg');
                    background-repeat: no-repeat, repeat;
                    margin-left: 6px;
                    background-position-y: center;
                  }
                }

                .dp--year-select {
                  width: 41px;
                  height: 24px;
                  pointer-events: none;
                  font-size: 16px;
                  font-weight: 600;
                  line-height: 24px;
                }
              }
            }

            .dp__flex_row {
              flex: unset;
            }

            .dp__overlay_row {
              height: 40px;
              width: 100%;
              margin-top: 5px;

              .dp__overlay_col {
                width: 68px;
                height: 40px;
                padding: unset;

                .dp__overlay_cell_pad {
                  padding: unset;
                  width: 68px;
                  height: 40px;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  font-size: 14px;
                  font-weight: 400;
                  line-height: 20px;
                  color: #404040;
                }

                .dp__overlay_cell_active {
                  background-color: #2E6BE5;
                  color: #FFFFFF;
                }

                .dp__overlay_cell {
                  &:hover {
                    background-color: #F5F5F5;
                    color: #404040;
                  }
                }
              }

              .dp__overlay_col:not(:last-child) {
                margin-right: 48px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
