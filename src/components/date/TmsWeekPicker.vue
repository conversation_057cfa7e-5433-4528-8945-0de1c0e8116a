<script setup lang="ts">
import { ref } from 'vue';
import VueDatePicker from '@vuepic/vue-datepicker';
import moment from 'moment';

import DashboardPrevious from '@/assets/icons/dashboard-previous.svg';
import DashboardNext from '@/assets/icons/dashboard-next.svg';
import DashboardCalendar from '@/assets/icons/dashboard-calendar.svg';

const modelDate = ref(moment().toDate());

const getMonth = (months, month) => {
  const thisMonth = months.filter(el => el.value === month)
  return thisMonth[0].text;
}

const selectWeek = () => {
  setTimeout(() => {
    const rows = document.getElementsByClassName('dp__calendar_row');
    if (rows.length > 0) {
      [].forEach.call(rows, function (row: Element) {
        [].forEach.call(row.children, (cells: Element) => {
          [].forEach.call(cells.children, (cell: Element) => {
            if (cell.classList.contains('dp__range_start')) {
              row.classList.add("active-row");
            }
          });
        })
      });
    }
  }, 100);
}

const format = (date: any) => {
  const weekNumber = moment(date).week();
  const year = moment(date).year();

  const firstDate = moment().day('Sunday').year(year).week(weekNumber);
  const lastDate = moment(firstDate).add(6, 'days');

  return `${firstDate.format('DD/MM/YYYY')} - ${lastDate.format('DD/MM/YYYY')}`;
}

const emit = defineEmits(['autoApply'])

const dateClicked = (date: any) => {
  emit('autoApply', date);
}
</script>

<template>
  <VueDatePicker
    v-model="modelDate"
    :enable-time-picker="false"
    :clearable="false"
    auto-apply week-picker
    class="week-picker"
    :week-numbers="{ type: 'iso' }"
    week-start="0"
    :format="format"
    @open="selectWeek"
    @date-update="dateClicked"
  >
    <template #month-year="{ month, year, months, handleMonthYearChange }">
      <Button class="btn-tms-date-picker" @click="handleMonthYearChange(false)">
        <div class="left-arrow">
          <!-- <DashboardPrevious /> -->
          <SvgIcon :src="DashboardPrevious" />
        </div>
      </Button>

      <span class="text-month-year-header">{{ getMonth(months, month) }} {{ year }}</span>

      <Button class="btn-tms-date-picker" @click="handleMonthYearChange(true)">
        <div class="right-arrow">
          <!-- <DashboardNext /> -->
          <SvgIcon :src="DashboardNext" />
        </div>
      </Button>
    </template>

    <template #dp-input="{ value }">
      <div class="input-value">
        <input type="text" :value="value" readonly />
        <i class="input-value-icon">
          <!-- <DashboardCalendar /> -->
          <SvgIcon :src="DashboardCalendar" />
        </i>
      </div>
    </template>
  </VueDatePicker>
</template>

<style lang="scss">
.week-picker {
  .dp__outer_menu_wrap {
    width: 352px;
    min-height: 332px;
    top: auto !important;
    left: calc(220px - 352px) !important;

    .dp__menu_index {
      padding: 24px;
      border-radius: 12px;
      width: 100%;
      height: 100%;

      .dp__arrow_bottom {
        bottom: unset;
        height: 0;
        width: 0;
        left: 0
      }

      .dp__arrow_top {
        top: unset;
        height: 0;
        width: 0;
        left: 0
      }

      .dp__instance_calendar {
        .dp__menu_inner {
          width: 304px;
          padding: 0;

          .dp--header-wrap {
            padding-bottom: 20px;

            .dp__month_year_wrap {
              justify-content: space-between;
              align-items: center;
            }
          }

          .dp__calendar {
            min-height: 232px;
            width: 304px;

            .dp__calendar_header {
              font-size: 14px;
              font-weight: 400;
              line-height: 20px;
              height: 32px;

              .dp__calendar_header_item {
                width: 32px;
                height: 32px;
                flex-grow: 0;
              }

              .dp__calendar_header_item:first-child {
                margin-right: 24px !important;
                color: #BABABA;
              }

              .dp__calendar_header_item:not(:last-child) {
                margin-right: 4px;
              }
            }

            .dp__calendar_header_separator {
              display: none;
            }

            .dp__calendar {
              min-height: 192px;
              width: 304px;
              font-size: 14px;
              font-weight: 400;
              line-height: 20px;

              .dp__calendar_row {
                justify-content: unset;
                margin: unset;
                height: 32px;
                margin-top: 8px;

                .dp__calendar_item {
                  width: 32px;
                  height: 32px;
                  flex-grow: 0;

                  .dp__cell_inner {
                    width: 32px;
                    height: 32px;
                    padding: unset;
                  }

                  .dp__cell_auto_range_start {
                    border-start-start-radius: unset;
                    border-end-start-radius: unset;
                    border-inline-start: unset;
                    border-top: unset;
                    border-bottom: unset;
                  }

                  .dp__cell_auto_range {
                    border-top: unset;
                    border-bottom: unset;
                  }

                  .dp__cell_auto_range_end {
                    border-start-end-radius: unset;
                    border-end-end-radius: unset;
                    border-top: unset;
                    border-bottom: unset;
                    border-inline-end: unset;
                  }
                }

                .dp__week_num {
                  margin-right: 24px !important;
                }

                .dp__calendar_item:not(:last-child) {
                  margin-right: 4px;
                }

                .dp__today {
                  background-color: #EDF3FF;
                  border: none;
                  color: #2E6BE5;
                }

                .dp__date_hover {
                  &:hover {
                    background-color: #F5F5F5;
                    color: #404040;
                  }
                }

                .dp__active_date {
                  background-color: #2E6BE5;
                  color: #FFFFFF;
                }

                &:hover {
                  background-color: #F5F5F5;
                }
              }

              .active-row {
                background-color: #2E6BE5;

                &:hover {
                  background-color: #2E6BE5 !important;
                }

                .dp__week_num {
                  background: unset;
                  color: #FFFFFF;
                }

                .dp__calendar_item {
                  .dp__range_start {
                    background: unset;
                    color: #FFFFFF;
                  }

                  .dp__range_between_week {
                    background: unset;
                    color: #FFFFFF;
                    border-top: 0;
                    border-bottom: 0;
                  }

                  .dp__range_end {
                    background: unset;
                    color: #FFFFFF;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .btn-tms-date-picker {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    padding: 6px;
    border: 1px solid #E6E6E6;
    background-color: #FFFFFF;

    .left-arrow {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .right-arrow {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .text-month-year-header {
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
  }
}
</style>
