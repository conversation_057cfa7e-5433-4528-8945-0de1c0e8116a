<script setup lang="ts">
import { ref } from 'vue';
import VueDatePicker from '@vuepic/vue-datepicker';

const date = ref();
const dp = ref();

const getMonth = (months, month) => {
  const thisMonth = months.filter(el => el.value === month)
  return thisMonth[0].text;
}

const selectDate = () => {
  dp.value.selectDate();
}

const unSelectDate = () => {
  dp.value.closeMenu();
}
</script>

<template>
  <VueDatePicker
    ref="dp" v-model="date" :enable-time-picker="false" range multi-calendars class="multi-date-range"
    week-start="0"
  >
    <template #month-year="{ month, year, months, handleMonthYearChange }">
      <Button class="btn-tms-date-picker" @click="handleMonthYearChange(false)">
        <div class="left-arrow">
          <svg width="7" height="12" viewBox="0 0 7 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M5.66669 1L1.37379 5.29289C0.983271 5.68342 0.98327 6.31658 1.37379 6.70711L5.66669 11"
              stroke="#404040" stroke-width="1.5" stroke-linecap="round"
            />
          </svg>
        </div>
      </Button>

      <span class="text-month-year-header">{{ getMonth(months, month) }} {{ year }}</span>

      <Button class="btn-tms-date-picker" @click="handleMonthYearChange(true)">
        <div class="right-arrow">
          <svg width="7" height="12" viewBox="0 0 7 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1.33331 11L5.62621 6.70711C6.01673 6.31658 6.01673 5.68342 5.62621 5.29289L1.33331 1"
              stroke="#404040" stroke-width="1.5" stroke-linecap="round"
            />
          </svg>
        </div>
      </Button>
    </template>

    <template #action-buttons>
      <div class="date-footer">
        <span class="range-picker">{{ date }}</span>
        <Button class="btn-tms-cancel" label="Cancel" @click="unSelectDate"></Button>
        <Button class="btn-tms-apply" label="Apply" @click="selectDate"></Button>
      </div>
    </template>
  </VueDatePicker>
</template>

<style lang="scss">
.multi-date-range {
  .dp__outer_menu_wrap {
    .dp__menu {
      width: 576px;
      height: 413px;
      padding: 24px;
      border-radius: 12px;

      .dp__arrow_bottom {
        bottom: unset;
        height: 0;
        width: 0;
        left: 0
      }

      .dp__arrow_top {
        top: unset;
        height: 0;
        width: 0;
        left: 0
      }

      .dp__instance_calendar {
        margin-bottom: 24px;

        .dp__menu_inner {
          padding: 0;

          .dp__instance_calendar:nth-child(1) {
            margin-right: 32px;
          }

          .dp__instance_calendar {
            width: 248px;
          }

          .dp__instance_calendar {
            .dp--header-wrap {
              padding-bottom: 12px;

              .dp__month_year_wrap {
                justify-content: space-between;
                align-items: center;
              }
            }

            .dp__calendar {
              width: 248px;
              height: 208px;
              margin: auto;

              .dp__calendar_header {
                font-family: Inter;
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
                text-align: center;
                height: 32px;
              }

              .dp__calendar_header_separator {
                display: none;
              }

              .dp__calendar {
                font-family: Inter;
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
                text-align: center;

                .dp__calendar_row {
                  margin-bottom: 4px;
                  margin-top: 0;
                  height: 32px;

                  .dp__calendar_item {
                    width: 32px;
                    height: 32px;

                    .dp__cell_inner {
                      width: 32px;
                      height: 32px;
                    }
                  }
                }

                .dp__today {
                  background-color: #EDF3FF;
                  border: none;
                  color: #2E6BE5;
                }

                .dp__date_hover {
                  &:hover {
                    background-color: #F5F5F5;
                    color: #404040;
                  }
                }

                .dp__range_start {
                  background-color: #2E6BE5;
                  color: #FFFFFF;
                  border-radius: 4px;
                }

                .dp__range_end {
                  background-color: #2E6BE5;
                  color: #FFFFFF;
                  border-radius: 4px;
                }

                .dp__range_between {
                  background-color: #EDF3FF;
                  color: #404040;
                }
              }
            }
          }
        }
      }

      .dp__action_row {
        margin-top: 12px;

        .dp__selection_preview {
          display: none;
        }

        .dp__action_buttons {
          width: 100%;
          flex: none;

          .date-footer {
            width: 100%;
            padding-top: 20px;
            border-top: 1px solid #E6E6E6;
            display: flex;
            justify-content: right;
            align-items: center;

            .btn-tms-cancel {
              width: 88px;
              height: 40px;
              border-radius: 4px;
              padding: 10px 20px;
              background-color: #F5F5F5;
              font-family: Inter;
              font-size: 14px;
              font-weight: 600;
              line-height: 20px;
              text-align: center;
              border: none;
              color: #404040;
              margin-right: 16px;
            }

            .btn-tms-apply {
              width: 88px;
              height: 40px;
              border-radius: 4px;
              padding: 10px 20px;
              background-color: #2E6BE5;
              font-family: Inter;
              font-size: 14px;
              font-weight: 600;
              line-height: 20px;
              text-align: center;
              border: none;
              color: #FFFFFF;
            }
          }
        }
      }
    }

    .text-month-year-header {
      font-family: Inter;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      text-align: center;
    }

    .btn-tms-date-picker {
      width: 32px;
      height: 32px;
      border-radius: 4px;
      padding: 6px;
      border: 1px solid #E6E6E6;
      background-color: #FFFFFF;

      .left-arrow {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .right-arrow {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style>
