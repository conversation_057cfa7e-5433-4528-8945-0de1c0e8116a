<script setup lang="ts">
import { ref } from 'vue';
import { useDebounceFn } from "@vueuse/core";
import VueDatePicker from '@vuepic/vue-datepicker';
import moment from 'moment';
import APPCONFIG from "@/appConfig";

import DashboardPrevious from '@/assets/icons/dashboard-previous.svg';
import DashboardNext from '@/assets/icons/dashboard-next.svg';
import DashboardCalendar from '@/assets/icons/dashboard-calendar.svg';
import TimeIcon from '@/assets/icons/calendar-time-icon.svg';

const model = defineModel({type: String, required: false });

const dp = ref();
const date = ref();
const hour = ref();
const minute = ref();
const selectedTime = ref('am');
const optionTime = [
  { text: 'AM', value: 'am' },
  { text: 'PM', value: 'pm' }
];

if (model.value) {
  date.value = model.value;
}

const selectDate = () => {
  dp.value.selectDate();

  date.value = moment(date.value)
  if (selectedTime.value == 'am')
    date.value.hour(hour.value);
  else
    date.value.hour(hour.value + 12);
  date.value.minute(minute.value)

  model.value = date.value.toString();
}

const unSelectDate = () => {
  dp.value.closeMenu();
}

const openDatepicker = () => {
  if (moment().hour() > 12) {
    hour.value = moment().hour() - 12;
  } else {
    hour.value = moment().hour();
  }
  minute.value = !minute.value ? moment().minute() : minute.value;

  selectedTime.value = moment().hour() >= 12 ? 'pm' : 'am';
}

const getMonth = (months, month) => {
  const thisMonth = months.filter(el => el.value === month)
  return thisMonth[0].text;
}

const getHour = useDebounceFn((eventHour) => {
  hour.value = eventHour;
}, APPCONFIG.DEBOUNCED_DELAY_INPUT);

const getMinute = useDebounceFn((eventMinute) => {
  minute.value = eventMinute;
}, APPCONFIG.DEBOUNCED_DELAY_INPUT);
</script>

<template>
  <VueDatePicker
    ref="dp" v-model="date" position="left" :is-24="false" week-start="0" :clearable="false"
    @open="openDatepicker"
  >
    <template #month-year="{ month, year, months, handleMonthYearChange }">
      <Button class="btn-tms-date-picker" @click="handleMonthYearChange(false)">
        <div class="left-arrow">
          <SvgIcon :src="DashboardPrevious" />
        </div>
      </Button>

      <span class="text-month-year-header">{{ getMonth(months, month) }} {{ year }}</span>

      <Button class="btn-tms-date-picker" @click="handleMonthYearChange(true)">
        <div class="right-arrow">
          <SvgIcon :src="DashboardNext" />
        </div>
      </Button>
    </template>

    <template #dp-input="{ value }">
      <div class="input-value">
        <input
          v-if="value" type="text"
          :value="moment(value, 'MM/DD/YYYY, hh:mm A').format(APPCONFIG.FORMAT_DATETIME_MOMENT)" readonly
          placeholder="DD/MM/YYYY hh:mm A"
        />
        <input v-else type="text" :value="value" readonly placeholder="DD/MM/YYYY hh:mm A" />
        <i class="input-value-icon">
          <SvgIcon :src="DashboardCalendar" />
        </i>
      </div>
    </template>

    <template #time-picker>
      <div class="time-picker">
        <InputNumber
          v-model="hour" name="hour" class="hour" autocomplete="off" :min="0" :max="12"
          @input="getHour($event.value)"
        />

        <SvgIcon :src="TimeIcon" />

        <InputNumber
          v-model="minute" name="minute" class="minute" autocomplete="off" :min="0" :max="59"
          @input="getMinute($event.value)"
        />

        <SvgIcon :src="TimeIcon" />

        <select v-model="selectedTime" class="time">
          <option v-for="option in optionTime" :key="option.value" :value="option.value">
            {{ option.text }}
          </option>
        </select>
      </div>
    </template>

    <template #action-buttons>
      <div class="date-footer">
        <Button class="btn-tms-cancel" label="Cancel" @click="unSelectDate"></Button>
        <Button class="btn-tms-apply" label="Apply" @click="selectDate"></Button>
      </div>
    </template>
  </VueDatePicker>
</template>

<style lang="scss">
.dp__main {
  width: 272px;
}

.dp__outer_menu_wrap {
  width: 320px;
  min-height: 332px;
  top: auto !important;

  .dp__menu_index {
    padding: 24px;
    border-radius: 12px;
    width: 100%;
    height: 100%;

    .dp__arrow_bottom {
      bottom: unset;
      height: 0;
      width: 0;
      left: 0
    }

    .dp__arrow_top {
      top: unset;
      height: 0;
      width: 0;
      left: 0
    }

    .dp__instance_calendar {
      .dp__menu_inner {
        min-width: 272px;
        padding: 0;

        .dp--header-wrap {
          padding-bottom: 20px;

          .dp__month_year_wrap {
            justify-content: space-between;
            align-items: center;
          }
        }

        .dp__calendar {
          min-height: 232px;
          min-width: 272px;

          .dp__calendar_header {
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            height: 32px;

            .dp__calendar_header_item {
              width: 32px;
              height: 32px;
              flex-grow: 0;
            }

            .dp__calendar_header_item:not(:last-child) {
              margin-right: 8px;
            }
          }

          .dp__calendar_header_separator {
            display: none;
          }

          .dp__calendar {
            min-height: 192px;
            min-width: 272px;
            font-family: Inter;
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            text-align: center;

            .dp__calendar_row {
              justify-content: unset;
              margin: unset;
              height: 32px;
              margin-top: 8px;

              .dp__calendar_item {
                width: 32px;
                height: 32px;
                flex-grow: 0;
              }

              .dp__cell_inner {
                width: 32px;
                height: 32px;
                padding: unset;

              }

              .dp__calendar_item:not(:last-child) {
                margin-right: 8px;
              }

              .dp__today {
                background-color: #EDF3FF;
                border: none;
                color: #2E6BE5;
              }

              .dp__date_hover {
                &:hover {
                  background-color: #F5F5F5;
                  color: #404040;
                }
              }

              .dp__active_date {
                background-color: #2E6BE5;
                color: #FFFFFF;
              }
            }
          }
        }
      }
    }
  }
}

.btn-tms-date-picker {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  padding: 6px;
  border: 1px solid #E6E6E6;
  background-color: #FFFFFF;

  .left-arrow {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .right-arrow {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.text-month-year-header {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}

.time-picker {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;

  .hour {
    .p-inputtext {
      padding: 4px 6px;
      width: 46px;
      height: 28px;
      margin-right: 16px;
      text-align: center;

      &:focus-visible {
        outline: none;
        border: 1px solid #2E6BE5;
      }
    }
  }

  .minute {
    .p-inputtext {
      padding: 4px 6px;
      width: 46px;
      height: 28px;
      margin-right: 16px;
      margin-left: 16px;
      text-align: center;

      &:focus-visible {
        outline: none;
        border: 1px solid #2E6BE5;
      }
    }
  }

  .time {
    width: 58px;
    height: 28px;
    border-radius: 3px;
    padding: 4px 8px;
    border: 1px solid #DCDCDC;
    font-family: Inter;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    text-align: left;
    -webkit-appearance: none;
    background-image: url('../../assets/icons/time-picker-arrow-down.svg');
    background-repeat: no-repeat, repeat;
    background-position: right 8px top 50%, 0 0;
    margin-left: 16px;

    &:focus-visible {
      outline: none;
      border: 1px solid #2E6BE5;
    }
  }
}

.input-value {
  position: relative;
  width: 273px;

  input {
    cursor: pointer;
    border-radius: 4px;
    border: 1px solid #DCDCDC;
    box-shadow: none;
    height: 36px;
    width: 100%;
    font-family: Inter;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    text-align: left;
    padding-left: 16px;

    &:focus {
      outline: none;
    }

    &::placeholder {
      color: #BABABA;
      opacity: 1;
      /* Firefox */
    }

    &::-ms-input-placeholder {
      /* Edge 12-18 */
      color: #BABABA;
    }
  }

  &-icon {
    height: 20px;
    cursor: pointer;
    position: absolute;
    top: 50%;
    right: 16px;
    transform: translateY(-50%);

    svg {
      vertical-align: unset;
    }
  }
}

.dp__action_row {
  margin-top: 12px;

  .dp__selection_preview {
    display: none;
  }

  .dp__action_buttons {
    width: 100%;
    flex: none;

    .date-footer {
      width: 100%;
      padding-top: 20px;
      border-top: 1px solid #E6E6E6;
      display: flex;
      justify-content: right;
      align-items: center;

      .btn-tms-cancel {
        width: 88px;
        height: 40px;
        border-radius: 4px;
        padding: 10px 20px;
        background-color: #F5F5F5;
        font-family: Inter;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        text-align: center;
        border: none;
        color: #404040;
        margin-right: 16px;
      }

      .btn-tms-apply {
        width: 88px;
        height: 40px;
        border-radius: 4px;
        padding: 10px 20px;
        background-color: #2E6BE5;
        font-family: Inter;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        text-align: center;
        border: none;
        color: #FFFFFF;
      }
    }
  }
}
</style>
