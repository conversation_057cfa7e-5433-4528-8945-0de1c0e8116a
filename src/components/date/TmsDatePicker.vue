<script setup lang="ts">
import { ref } from 'vue';
import moment from 'moment';
import VueDatePicker from '@vuepic/vue-datepicker';

import DashboardPrevious from '@/assets/icons/dashboard-previous.svg';
import DashboardNext from '@/assets/icons/dashboard-next.svg';
import DashboardCalendar from '@/assets/icons/dashboard-calendar.svg';

const date = ref(moment().toDate());

const getMonth = (months, month) => {
  const thisMonth = months.filter(el => el.value === month)
  return thisMonth[0].text;
}

const format = (date: any) => {
  return moment(date).format('DD-MM-YYYY');
}

const emit = defineEmits(['autoApply'])

const dateClicked = (date: any) => {
  emit('autoApply', date);
}
</script>

<template>
  <VueDatePicker
    v-model="date"
    :enable-time-picker="false"
    :clearable="false"
    auto-apply
    class="date-picker"
    week-start="0"
    :format="format"
    @date-update="dateClicked"
  >
    <template #month-year="{ month, year, months, handleMonthYearChange }">
      <Button class="btn-tms-date-picker" @click="handleMonthYearChange(false)">
        <div class="left-arrow">
          <!-- <DashboardPrevious /> -->
          <SvgIcon :src="DashboardPrevious" />
        </div>
      </Button>

      <span class="text-month-year-header">{{ getMonth(months, month) }} {{ year }}</span>

      <Button class="btn-tms-date-picker" @click="handleMonthYearChange(true)">
        <div class="right-arrow">
          <!-- <DashboardNext /> -->
          <SvgIcon :src="DashboardNext" />
        </div>
      </Button>
    </template>

    <template #dp-input="{ value }">
      <div class="input-value">
        <input type="text" :value="value" readonly />
        <!-- <i class="input-value-icon">
          <DashboardCalendar />
        </i> -->
        <SvgIcon :src="DashboardCalendar" class="input-value-icon" />
      </div>
    </template>
  </VueDatePicker>
</template>

<style lang="scss">
.date-picker {
  .dp__outer_menu_wrap {
    width: 320px;
    min-height: 332px;
    top: auto !important;
    left: calc(220px - 320px) !important;

    .dp__menu_index {
      padding: 24px;
      border-radius: 12px;
      width: 100%;
      height: 100%;

      .dp__arrow_bottom {
        bottom: unset;
        height: 0;
        width: 0;
        left: 0
      }

      .dp__arrow_top {
        top: unset;
        height: 0;
        width: 0;
        left: 0
      }

      .dp__instance_calendar {
        .dp__menu_inner {
          width: 272px;
          padding: 0;

          .dp--header-wrap {
            padding-bottom: 20px;

            .dp__month_year_wrap {
              justify-content: space-between;
              align-items: center;
            }
          }

          .dp__calendar {
            min-height: 232px;
            width: 272px;

            .dp__calendar_header {
              font-size: 14px;
              font-weight: 400;
              line-height: 20px;
              height: 32px;

              .dp__calendar_header_item {
                width: 32px;
                height: 32px;
                flex-grow: 0;
              }

              .dp__calendar_header_item:not(:last-child) {
                margin-right: 8px;
              }
            }

            .dp__calendar_header_separator {
              display: none;
            }

            .dp__calendar {
              min-height: 192px;
              width: 272px;
              font-family: Inter;
              font-size: 14px;
              font-weight: 400;
              line-height: 20px;
              text-align: center;

              .dp__calendar_row {
                justify-content: unset;
                margin: unset;
                height: 32px;
                margin-top: 8px;

                .dp__calendar_item {
                  width: 32px;
                  height: 32px;
                  flex-grow: 0;
                }

                .dp__cell_inner {
                  width: 32px;
                  height: 32px;
                  padding: unset;

                }

                .dp__calendar_item:not(:last-child) {
                  margin-right: 8px;
                }

                .dp__today {
                  background-color: #EDF3FF;
                  border: none;
                  color: #2E6BE5;
                }

                .dp__date_hover {
                  &:hover {
                    background-color: #F5F5F5;
                    color: #404040;
                  }
                }

                .dp__active_date {
                  background-color: #2E6BE5;
                  color: #FFFFFF;
                }
              }
            }
          }
        }
      }
    }
  }

  .btn-tms-date-picker {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    padding: 6px;
    border: 1px solid #E6E6E6;
    background-color: #FFFFFF;

    .left-arrow {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .right-arrow {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .text-month-year-header {
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
  }
}
</style>
