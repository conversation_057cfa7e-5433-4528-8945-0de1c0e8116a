<template>
  <div class="compoennt-qrcode">
    <img :src="qrcode" alt="QR Code" class="img-qr-code" />
    <Button
      icon="pi pi-download"
      :label="$t('page-Dashboard.btn-download-qr')"
      class="btn-download-qr"
      @click.stop="downloadQr()"
    ></Button>
  </div>
</template>

<script lang="ts" setup>
import { useQRCode } from "@vueuse/integrations/useQRCode";
import { computed } from "vue";

defineOptions({
  inheritAttrs: false,
})

const props = withDefaults(
  defineProps<{
    value: string
  }>(),
  {
    value: "",
  }
)

const emit = defineEmits<{
  (e: "downloaded", value: string): void
}>()

const qrcodeText = computed(() => props.value ?? "")

const qrcode = useQRCode(qrcodeText, {
  errorCorrectionLevel: "Q",
  type: "image/jpeg",
  margin: 0,
})

async function downloadQr() {
  emit("downloaded", qrcodeText.value)
}
</script>

<style lang="scss">
.compoennt-qrcode {
  display: flex;
  flex-direction: column;

  .btn-download-qr {
    background-color: unset !important;
    color: #2e6be5;
    padding: 10px 0;
    align-items: center;
    justify-content: center;

    .p-button-label {
      flex: none;
    }
  }
}
</style>
