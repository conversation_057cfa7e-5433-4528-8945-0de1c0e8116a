<script setup lang="ts">
import AutoUpdate from "@/assets/icons/icon-noti-update-currency.svg"

import type { CurrencyUpdateRequest, PaymentLinkCurrencyItem } from "@/apis/userApis"
import APPCONFIG from "@/appConfig"
import { useAuthService } from "@/services/authService"
import { usePaymentLinkService } from "@/services/paymentLinkService"
import { useToastService } from "@/services/toastService"
import { useUserService } from "@/services/userService"
import { formatCurrency, formatDateTime } from "@/utils/formatUtil"
import { refDebounced } from "@vueuse/core"
import { useAsyncValidator } from "@vueuse/integrations/useAsyncValidator"
import { Rules } from "async-validator"
import { storeToRefs } from "pinia"
import type { DialogProps } from "primevue/dialog"
import { computed, ref } from "vue"
import { useAsyncTask } from "vue-concurrency"
import { useI18n } from "vue-i18n"

export interface CurrencySettingDialogDialogProps extends /* @vue-ignore */ DialogProps {}

const props = withDefaults(defineProps<CurrencySettingDialogDialogProps>(), {
  draggable: false,
})

const [visibleModel] = defineModel<boolean>("visible")

const authService = useAuthService()
const { getAllCurrencyTask, updateAllCurrency } = useUserService()
const { getCurrencyConfigTask } = usePaymentLinkService()
const { t } = useI18n()
const toastService = useToastService()

const { authMerchantProfileId, authMerchantProfile } = storeToRefs(authService)

const currencyCodeName = ref<string>("")
const currencyCodeNameDebounced = refDebounced(currencyCodeName, APPCONFIG.DEBOUNCED_DELAY_INPUT)

const listPaymentLinkCurrency = ref<PaymentLinkCurrencyItem[]>([])
const listPaymentLinkCurrencyFilter = computed(() => {
  const search = currencyCodeNameDebounced.value?.toLowerCase()
  return listPaymentLinkCurrency.value.filter(
    (el) => el.currencyCode.toLowerCase().includes(search) || el.currencyName.toLowerCase().includes(search)
  )
})
const listPaymentLinkCurrencyUpdated = ref<PaymentLinkCurrencyItem[]>([])

//#region validation
const rules: Rules = {
  items: {
    type: "array",
    required: true,
    defaultField: {
      type: "object",
      fields: {
        exchangeRate: {
          type: "number",
          required: true,
          validator(rule, value, callback, source, options) {
            //console.log('value', value, "source", source)
            const canInput = source.updateType === 1 && source.enabled
            if (canInput && !value) {
              callback(new Error("Exchange rate must be greater than 0"))
            } else {
              callback()
            }
          },
        },
      },
    },
  },
}

// Sử dụng để hiển thị invalid trực tiếp trên view datatable
const listPaymentLinkCurrencyFilter_ValidateSource = computed(() => {
  return { items: listPaymentLinkCurrencyFilter.value }
})
const { errorFields } = useAsyncValidator(listPaymentLinkCurrencyFilter_ValidateSource, rules)

// Sử dụng để check khi thực hiện Save changes
const listPaymentLinkCurrency_ValidateSource = computed(() => {
  return { items: listPaymentLinkCurrency.value }
})
const {
  isFinished: isFinishedValidateListCurrency,
  pass: passValidateListCurrency,
  execute: executeValidateListCurrency,
} = useAsyncValidator(listPaymentLinkCurrency_ValidateSource, rules)
const isValidListPaymentLinkCurrency = computed(() => {
  return isFinishedValidateListCurrency.value && passValidateListCurrency.value
})
//#endregion

const fetchDataCurrencyTask = useAsyncTask(async (signal, options: void) => {
  //const listOnePayCurrencyItem = await getAllCurrencyTask.perform()
  //const listCurrencySetting = await getCurrencyConfigTask.perform(authMerchantProfileId.value)

  // load đồng thời cả 2 request
  const [listOnePayCurrencyItem, listCurrencySetting] = await Promise.all([
    getAllCurrencyTask.perform(),
    getCurrencyConfigTask.perform(authMerchantProfileId.value),
  ])

  listPaymentLinkCurrency.value =
    listOnePayCurrencyItem?.map((element) => {
      const CurrencyHasBeenConfigured = listCurrencySetting?.find((item) => item.currency === element.currency_code)
      let imgSrc = new URL(`/src/assets/icons/flags/${element.currency_code}.svg`, import.meta.url).href
      imgSrc = imgSrc.endsWith("undefined") ? "" : imgSrc

      let item: PaymentLinkCurrencyItem = {
        id: "",
        currencyCode: element.currency_code,
        currencyName: element.currency_name_en,
        exchangeRate: element.exchange_Rate,
        exchangeRateAuto: element.exchange_Rate, // exchange of auto mode
        enabled: false,
        updateType: 0,
        lastUpdate: undefined,
        updateBy: undefined,
        type: element.type,
        imgSrc: imgSrc,
      }

      if (CurrencyHasBeenConfigured) {
        // Merge với dữ liệu CurrencyHasBeenConfigured
        item = Object.assign(item, {
          id: CurrencyHasBeenConfigured.id,
          exchangeRate: CurrencyHasBeenConfigured.exchangeRate,
          enabled: CurrencyHasBeenConfigured?.enabled,
          updateType: CurrencyHasBeenConfigured?.updateType,
          lastUpdate: CurrencyHasBeenConfigured?.lastModifyDate,
          updateBy: CurrencyHasBeenConfigured?.lastModifyBy,
        } as PaymentLinkCurrencyItem)
      }

      return item
    }) ?? []

  if (authMerchantProfile.value && authMerchantProfile.value.defaultCurrency === "VND") {
    const itemWithCurrencyVnd = listPaymentLinkCurrency.value.find((item) => item.currencyCode === "VND")

    if (itemWithCurrencyVnd) {
      listPaymentLinkCurrency.value.splice(listPaymentLinkCurrency.value.indexOf(itemWithCurrencyVnd), 1)
    }
  }

  listPaymentLinkCurrencyUpdated.value = [] // clear list of updated items sau khi tạo list listPaymentLinkCurrency mới.
})
const fetchDataCurrency = () => fetchDataCurrencyTask.perform()

const saveCurrencyTask = useAsyncTask(async (signal, options: void) => {
  const validateResult = await executeValidateListCurrency()
  if (!validateResult.pass) {
    toastService.error({
      summary: t("component-currency-setting.invalid-list-currency"),
    })
    return
  }

  const request: CurrencyUpdateRequest[] = listPaymentLinkCurrencyUpdated.value.map((el) => {
    return {
      //merchantprofile_id: authMerchantProfileId.value,
      id: el.id,
      currency: el.currencyCode,
      update_type: el.updateType,
      exchange_rate: el.exchangeRate ? el.exchangeRate.toString() : "",
      enabled: el.enabled,
    }
  })

  const response = await updateAllCurrency
    .perform(request)
    .then((data) => {
      return data
    })
    .catch((error) => {
      return error
    })

  if (response && response.status === 200) {
    toastService.success({})

    await fetchDataCurrency()
  } else {
    toastService.error({})
  }
})
const onSaveCurrency = () => saveCurrencyTask.perform()

const onCloseDialogTable = () => {
  // clear data
  listPaymentLinkCurrency.value = []
  currencyCodeName.value = ""
}

const switchStatus = (item: PaymentLinkCurrencyItem) => {
  // NhatND pass điều kiện khi là manual thì cho phép nhập
  if (item.type === "NOT_VCB") item.updateType = 1
  // if (!item.enabled) {
  //   //item.updateType = -1 // NghiaNL tạm comment vì ko hiểu Nhật làm gì. tài liệu ko mô tả giá trị này và nó bị update lên server.
  // }
}

const markDataUpdated = (item: PaymentLinkCurrencyItem) => {
  const isItemBeingEdited =
    listPaymentLinkCurrencyUpdated.value.findIndex((o) => o.currencyCode == item.currencyCode) > -1
  if (!isItemBeingEdited) {
    listPaymentLinkCurrencyUpdated.value.push(item)
  }
}
</script>

<template>
  <Dialog
    v-model:visible="visibleModel"
    :header="$t('component-currency-setting.header')"
    :draggable="false"
    modal
    v-bind="{ ...$attrs, ...$props }"
    class="modal-currency-config"
    @show="fetchDataCurrency"
    @hide="onCloseDialogTable"
  >
    <InputTextFilterClearable
      v-model="currencyCodeName"
      :placeholder="$t('component-currency-setting.placeholder')"
      wrapper-class="input-search-currency"
    />

    <div class="noti-auto-update">
      <SvgIcon :src="AutoUpdate" />
      <span class="label">
        {{ $t("component-currency-setting.label") }}
      </span>
    </div>

    <div class="data-table-currency">
      <AppDataTable
        ref="refTableCurrency"
        :value="listPaymentLinkCurrencyFilter"
        :paginator="false"
        :loading="fetchDataCurrencyTask.isRunning || saveCurrencyTask.isRunning"
        scrollable
        scroll-height="469px"
      >
        <Column field="currencyCode" :header="$t('component-currency-setting.table.code')">
          <template #body="slotProps">
            <div class="d-flex align-items-center">
              <img
                v-if="slotProps.data.imgSrc"
                :src="slotProps.data.imgSrc"
                :alt="slotProps.data.currencyCode"
                class="currency-icon"
              />
              <span v-else class="currency-icon"></span>
              <span class="currency-text">
                {{ slotProps.data.currencyCode }}
              </span>
            </div>
          </template>
        </Column>

        <Column field="currencyName" :header="$t('component-currency-setting.table.name')" />

        <Column field="enabled" :header="$t('component-currency-setting.table.status')">
          <template #body="slotProps">
            <ToggleSwitch
              v-model="slotProps.data.enabled"
              @change="switchStatus(slotProps.data), markDataUpdated(slotProps.data)"
            />
          </template>
        </Column>

        <Column field="updateType" :header="$t('component-currency-setting.table.update-type')">
          <template #body="slotProps">
            <div v-if="!slotProps.data.enabled">&nbsp;</div>

            <div v-else-if="slotProps.data.type === 'VCB'" class="group-radio">
              <div class="radio-button-auto">
                <RadioButton
                  v-model="slotProps.data.updateType"
                  input-id="auto"
                  :value="0"
                  @change="markDataUpdated(slotProps.data)"
                />
                <label for="auto" class="ml-2">{{ $t("component-currency-setting.auto") }}</label>
              </div>

              <div class="radio-button-manual">
                <RadioButton
                  v-model="slotProps.data.updateType"
                  input-id="manual"
                  :value="1"
                  @change="markDataUpdated(slotProps.data)"
                />
                <label for="manual" class="ml-2">{{ $t("component-currency-setting.manual") }}</label>
              </div>
            </div>

            <div v-else-if="slotProps.data.type === 'NOT_VCB'">
              {{ $t("component-currency-setting.manual") }}
            </div>

            <div v-else>
              {{ "-" }}
            </div>
          </template>
        </Column>

        <Column field="exchangeRate" :header="$t('component-currency-setting.table.exchange')" style="text-align: right">
          <template #body="slotProps">
            <span v-if="!slotProps.data.enabled">&nbsp;</span>

            <span v-else-if="slotProps.data.updateType === 0">
              {{ slotProps.data.exchangeRateAuto ? formatCurrency(slotProps.data.exchangeRateAuto, undefined) : "-" }}
            </span>

            <!-- <InputNumberNew
              v-else-if="slotProps.data.updateType === 1"
              v-model="slotProps.data.exchangeRate"
              class="exchange-rate"
              :is-integer="false"
              @update:model-value="markDataUpdated(slotProps.data)"
            /> -->
            <InputAmount
              v-else-if="slotProps.data.updateType === 1"
              v-model:model-value="slotProps.data.exchangeRate"
              name="currencySetting-exchangeRate"
              class="exchange-rate"
              mode="float"
              :invalid="errorFields && !!errorFields[`items.${slotProps.index}.exchangeRate`]?.length"
              :currency="undefined"
              @update:model-value="markDataUpdated(slotProps.data)"
            />
          </template>
        </Column>

        <Column field="lastUpdate" :header="$t('component-currency-setting.table.last-update')">
          <template #body="slotProps">
            {{ formatDateTime(slotProps.data.lastUpdate) || "-" }}
          </template>
        </Column>

        <Column field="updateBy" :header="$t('component-currency-setting.table.update-by')">
          <template #body="slotProps">
            {{ slotProps.data.updateBy || "-" }}
          </template>
        </Column>
      </AppDataTable>
    </div>

    <template #footer>
      <button class="btn-cancel" @click="visibleModel = false">{{ $t("common.buttons.cancel") }}</button>
      <Button
        class="btn-confirm"
        :label="$t('component-currency-setting.table.save')"
        :loading="saveCurrencyTask.isRunning"
        :disabled="!isValidListPaymentLinkCurrency"
        @click="onSaveCurrency"
      ></Button>
    </template>
  </Dialog>
</template>

<style lang="scss">
.modal-currency-config {
  min-width: 1106px;

  .p-dialog-header {
    padding: 1rem 1.5rem;

    .title {
      font-size: 24px;
      font-weight: 600;
      line-height: 36px;
      color: #1c1c1c;
    }
  }

  .p-dialog-content {
    overflow: hidden;

    .input-search-currency {
      max-width: 20rem;
      //height: 40px;
      // padding-top: 4px;
      // padding-bottom: 4px;
    }

    .noti-auto-update {
      height: 36px;
      display: flex;
      align-items: center;
      padding: 8px 20px;
      background-color: #edf3ff;
      margin-top: 12px;

      .label {
        margin-left: 12px;
        font-size: 12px;
        color: #404040;
      }
    }

    .data-table-currency {
      margin-top: 12px;

      .p-column-title {
        width: 100%;
      }

      .group-radio {
        display: flex;

        .radio-button-auto {
          margin-right: 20px;
        }

        .radio-button-auto,
        .radio-button-manual {
          display: inline-flex;
          align-items: center;
        }
      }

      .exchange-rate {
        height: 1.75rem;
        width: 5.25rem;
        padding: 0.25rem 0.75rem;

        color: var(--text-body, #404040);
        /* Inter/B3/12_Regular */
        font-family: var(--Typeface-family-Inter, Inter);
        font-size: var(--Typeface-size-sm, 0.75rem);
        font-style: normal;
        font-weight: 400;
        line-height: 1rem; /* 133.333% */
      }

      .currency-icon {
        width: 1.25rem;
        height: 1.25rem;
        margin-right: 0.5rem;
      }
      .currency-text {
        font-weight: 600;
        margin-left: 0.5rem;
      }

      .p-datatable-wrapper {
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 10px;
          border: 3px solid #dcdcdc;
        }
      }
    }
  }

  .p-dialog-footer {
    padding: 0 24px 24px 24px;

    .btn-cancel {
      min-width: 100px;
      height: 40px;
      border-radius: 4px;
      padding: 10px 24px;
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      color: #404040;
      margin-right: 20px;
      border: none;
    }

    .btn-confirm {
      min-width: 120px;
      height: 40px;
      border-radius: 4px;
      padding: 10px 24px;
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      color: #ffffff;
      border: none;
      background-color: #2e6be5;
    }
  }
}
</style>
