<script setup lang="ts">
import StarCheckedIcon from "@assets/icons/star-checked.svg"
import StarUnCheckIcon from "@assets/icons/star-uncheck.svg"
import CloseIcon from "@assets/icons/close.svg"

import { useAuthService } from "@/services/authService"
import { storeToRefs } from "pinia"
import { computed, ref, watch } from "vue"
import type { SidebarProps } from "primevue/sidebar"
import { getImgSrc } from "@/utils/appUtil"

export interface MerchantSelecterSidebarProps extends SidebarProps {}

const props = withDefaults(defineProps<MerchantSelecterSidebarProps>(), {})

const visibleModel = defineModel<boolean>("visible")

interface IMerchantMenuItem {
  id: string // giá trị của Merchan Id
  label: string // tên của Merchan
  favorited: boolean // trạng thái yêu thích
}

const authService = useAuthService()

const { selectMerchantProfileTask } = authService
const { authUserMerchantsList, authMerchantProfile, pinMerchant, authMerchantProfileId } = storeToRefs(authService)

const authMerchantProfileName = computed(() => authMerchantProfile.value?.name)
const authMerchantProfileLogo = computed(() => getImgSrc(authMerchantProfile.value?.logoFileId))

const searchMerchant = ref("")
const merchantsOfUser = ref<IMerchantMenuItem[]>([])

// load 1 lần. ko dùng computed. dùng watch để giữ favorited/selected
watch(
  authUserMerchantsList,
  (data) => {
    console.log(authMerchantProfile.value)
    merchantsOfUser.value =
      data
        .map((m) => {
          return {
            id: m.merchant_id,
            label: m.merchant_name,
            favorited: m.pin,
          } as IMerchantMenuItem
        })
        .sort((item, next) => {
          const a = item.label || ''
          const b = next.label || ''
          return a.localeCompare(b)
        }) ?? []
  },
  { immediate: true }
)

function searchText(source: string, searchValue: string): boolean {
  return searchValue ? source.search(new RegExp(searchValue, "i")) > -1 : true
}

const favoriteMerchants = computed(() => {
  return merchantsOfUser.value.filter((item) => item.favorited == true && searchText(item.label, searchMerchant.value))
})
const allMerchants = computed(() => {
  return merchantsOfUser.value.filter((item) => item.favorited == false && searchText(item.label, searchMerchant.value))
})

async function setFavorited(item: IMerchantMenuItem, state: boolean) {
  console.log("setFavorited")
  item.favorited = state
  await pinMerchant.value.perform({ merchantId: item.id, pin: item.favorited })
}

function selectMerchant(item: IMerchantMenuItem) {
  console.log("selectMerchant", item.id)

  if (authMerchantProfile.value?.id == item.id) {
    visibleModel.value = false
  }

  selectMerchantProfileTask.perform({
    merchantId: item.id,
  })

  visibleModel.value = false

  // TODO: call service bus to reload all data in page
}

function onSidebarVisible() {
  searchMerchant.value = "" // clear search input state
}
</script>

<template>
  <Sidebar
    id="offcanvasSelectMerchans"
    ref="offcanvasSelectMerchans"
    v-model:visible="visibleModel"
    :show-close-icon="false"
    v-bind="{ ...$attrs, ...$props }"
    class="offcanvas-merchans"
    aria-labelledby="offcanvasExampleLabel"
    @show="onSidebarVisible"
  >
    <template #header>
      <div class="merchans-info">
        <div>
          <button type="button" class="btn btn-close text-reset" @click="visibleModel = false">
            <SvgIcon :src="CloseIcon" />
          </button>
        </div>
        <ul class="navbar-nav navbar-nav-menu-merchan-search">
          <li class="nav-item no-hover">
            <button v-tooltip="authMerchantProfileName ?? ''" class="nav-link nav-link-header">
              <span class="nav-item-text">{{ authMerchantProfileName }}</span>
            </button>
          </li>
          <li class="nav-item no-hover">
            <InputTextFilterClearable
              v-model="searchMerchant"
              :placeholder="$t('left-menu.input-filter-profiles-placeholder')"
            />
          </li>
        </ul>
      </div>
    </template>

    <div class="offcanvas-body">
      <hr class="mt-0" />

      <div v-if="favoriteMerchants.length > 0" class="merchans-favorites">
        <label class="group-title">{{ $t("component-merchant-profiles.favorite") }}</label>
        <ul class="navbar-nav navbar-nav-menu-merchan-favorites">
          <li v-for="item in favoriteMerchants" :key="item.id" class="nav-item">
            <button
              class="nav-link"
              :class="{ disabled: authMerchantProfile?.id == item.id }"
              @click="selectMerchant(item)"
            >
              <span class="nav-item-text">{{ item.label }}</span>
              <SvgIcon
                :src="StarCheckedIcon"
                class="nav-item-icon-right user-clickable"
                @click.stop="setFavorited(item, false)"
              />
            </button>
          </li>
        </ul>

        <hr class="mt-2" />
      </div>

      <div v-if="allMerchants.length" class="merchans-allprofiles">
        <label class="group-title">{{ $t("component-merchant-profiles.all-profiles") }}</label>
        <ul class="navbar-nav navbar-nav-menu-merchan-allprofiles">
          <li v-for="item in allMerchants" :key="item.id" class="nav-item">
            <button
              class="nav-link"
              :class="{ disabled: authMerchantProfile?.id == item.id }"
              @click="selectMerchant(item)"
            >
              <span class="nav-item-text">{{ item.label }}</span>
              <SvgIcon
                :src="StarUnCheckIcon"
                class="nav-item-icon-right user-clickable"
                @click.stop="setFavorited(item, true)"
              />
            </button>
          </li>
        </ul>
      </div>
    </div>
  </Sidebar>
</template>

<style lang="scss">
.offcanvas-merchans {
  width: 312px;

  .p-sidebar-header {
    padding: 1rem 1.5rem;
  }
  .p-sidebar-content {
    padding: 1rem 1.5rem;
    padding-top: 0;
  }
  .btn-close {
    padding: 0.5rem;
  }

  .merchans-info {
    overflow-x: auto;
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .merchans-favorites,
  .merchans-allprofiles {
    .group-title {
      font-size: 0.75rem;
      font-style: normal;
      font-weight: 600;
      line-height: 1rem;
    }
  }
}

.navbar-nav-menu-left,
.navbar-nav-menu-merchan-current,
.navbar-nav-menu-merchan-search,
.navbar-nav-menu-merchan-favorites,
.navbar-nav-menu-merchan-allprofiles {
  display: flex;
  flex-flow: column;
  gap: var(--layout-spacing-spacing-xs, 16px);
  padding: var(--layout-spacing-spacing-md, 12px) var(--layout-spacing-spacing-none, 0px);

  .nav-link {
    width: 100%;
    padding: 0;
    display: flex;
    align-items: center;
    color: var(--text-body, #404040);

    &.disabled {
      pointer-events: inherit;
      cursor: default;
      color: var(--text-disabled2, #BABABA);
    }

    &.router-link-active {
      font-weight: 600;
      color: var(--text-headings, #1c1c1c);
    }
  }

  .nav-item {
    padding: 0.5rem 0.5rem 0.5rem 1.5rem;
    height: 2.5rem;

    &:hover:not(.no-hover),
    &:has(.nav-link.router-link-active) {
      background-color: #e6e6e6;
    }
  }

  .nav-item-icon,
  .nav-item-icon-right {
    width: 1.5rem;
    height: 1.5rem;
  }
  .nav-item-icon {
    margin-right: 0.5rem;
  }
  .nav-item-icon-right {
    margin-left: auto;
  }

  .nav-item-text {
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.navbar-nav-menu-merchan-current {
  border-bottom: 1px solid var(--border-tertiary, #e6e6e6);

  .nav-link {
    font-weight: 600;
    color: var(--text-headings, #1c1c1c);
  }

  .nav-item {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    font-weight: 600;
    color: var(--text-headings, #1c1c1c);
  }

  .nav-item-icon {
    padding: 4.85px;
    border-radius: var(--layout-spacing-spacing-xxs, 4px);
    border: 1px solid var(--border-primary, #dcdcdc);
    width: 2rem;
    min-width: 2rem;
    height: 2rem;
  }
}

.navbar-nav-menu-merchan-search {
  gap: var(--layout-spacing-spacing-xs, 12px);

  .nav-item {
    padding: 0;
  }

  .nav-link {
    color: var(--text-headings, #1c1c1c);
  }

  .nav-item-icon {
    width: 3rem;
    height: 3rem;
  }

  .nav-item-text {
    font-size: 1rem;
    line-height: 1.5;
    font-weight: 600;
  }
}

.navbar-nav-menu-merchan-favorites {
  gap: var(--layout-spacing-spacing-xs, 8px);

  .nav-item-icon-right {
    width: 1.25rem;
    height: 1.25rem;
  }
}

.navbar-nav-menu-merchan-allprofiles {
  gap: var(--layout-spacing-spacing-xs, 8px);

  .nav-item-icon-right {
    visibility: hidden;
    width: 1.25rem;
    height: 1.25rem;
  }

  .nav-item:hover {
    .nav-item-icon-right {
      visibility: visible;
    }
  }
}
</style>