<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref, watch, computed } from "vue"
import { onSrcUpdate as onSrcUpdateRaw, onUnmount as onUnmountRaw } from "vite-awesome-svg-loader/integration-utils"
import { SvgImageProps } from "vite-awesome-svg-loader/vue-integration"
import { isNull, isUndefined } from "lodash-es"

defineOptions({
  inheritAttrs: false,
})

export interface SvgIconProps extends SvgImageProps {
  size?: string | number
  color?: string
}

const props = defineProps<SvgIconProps>()
const id = ref("")
const svgAttrs = ref<Record<string, any>>({})
const sizeStyle = computed(() => (props.size ? { width: props.size, height: props.size } : null))
const colorStyle = computed(() => (props.color ? { color: props.color } : null))

const onSrcUpdate = (prevSrc: string | undefined, src: string) => {
  const res = onSrcUpdateRaw(prevSrc, src)

  if (res.id) {
    id.value = res.id
  }

  if (res.attrs) {
    // Custom lại thư viện đoạn này
    if (res.id) {
      const elm = document.getElementById(res.id)
      const width = elm?.getAttribute("width")
      const height = elm?.getAttribute("height")
      if (!isNull(width) && !isUndefined(width)) res.attrs.width = width
      if (typeof height !== "undefined" && height !== null) res.attrs.height = height
    }

    svgAttrs.value = res.attrs
  }
}

watch(
  () => props.src,
  (newSrc, oldSrc) => onSrcUpdate(oldSrc, newSrc)
)

// onBeforeMount doesn't trigger DOM update in Nuxt 3.8.2
onMounted(() => onSrcUpdate(undefined, props.src))
onBeforeUnmount(() => onUnmountRaw(id.value))
</script>

<template>
  <svg v-bind="{ alt: '', ...svgAttrs, ...$attrs }" :style="[sizeStyle, colorStyle]">
    <use v-bind="{ ...(useElAttrs || {}), href: '#' + id }" />
  </svg>
</template>
