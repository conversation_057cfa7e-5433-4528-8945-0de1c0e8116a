<script lang="ts">
const defaultPanelProps = (): Record<string | number | symbol, any> => {
  const component = Panel as DefineComponent<{}, {}, any>
  const props = {
    ...component.extends?.props,
    ...component.props
  }
  let propsDefaults: Record<string | number | symbol, any> = {}
  $.each(props, (key, value) => {
    propsDefaults[key] = value.default
  })
  return propsDefaults
}
</script>

<script setup lang="ts">

import type { PanelProps, PanelEmits, PanelSlots, PanelToggleEvent } from "primevue/panel"
import Panel from "primevue/panel"
import { defineAsyncComponent, type DefineComponent } from "vue"
//const SvgIcon = defineAsyncComponent(() => import('@components/ui/SvgIcon.vue'))

defineOptions({
  inheritAttrs: false
})

const props = withDefaults(defineProps<PanelProps>(), {
  ...defaultPanelProps()
  // Extend or change defaults value of props here:
})

/**
 * Emitted when the value changes.
 * @param {boolean} value - New value.
 */
const [collapsedValueModel, collapsedValueModelModifiers] = defineModel<boolean>("collapsed", {
  default: false
})

const emit = defineEmits<{
  /**
   * Callback to invoke when a tab toggle.
   */
  toggle: [event: PanelToggleEvent] // named tuple syntax
}>()

const slots = defineSlots<PanelSlots>()
</script>

<template>
  <Panel
    v-bind="{ ...$attrs, ...$props, ...$emit }"
    v-model:collapsed="collapsedValueModel"
    class="app-panel"
  >
    <template #togglericon="slotProps">
      <slot name="togglericon" v-bind="slotProps">
        <!-- <SvgIcon :name="slotProps.collapsed ? 'svgicon-chevron-down' : 'svgicon-chevron-up'"></SvgIcon> -->
        <AppSvgNamedIcon :name="slotProps.collapsed ? 'chevron-down' : 'chevron-up'"></AppSvgNamedIcon>
      </slot>
    </template>

    <template #header="slotProps">
      <slot name="header" v-bind="slotProps"></slot>
    </template>

    <template #icons>
      <slot name="icons"></slot>
    </template>

    <slot></slot>

    <!-- <template v-for="(_, name) in $slots" v-slot:[name]="slotData">
      <slot :name="name" v-bind="slotData ?? {}"></slot>
    </template> -->
  </Panel>
</template>

<style lang="scss"></style>
