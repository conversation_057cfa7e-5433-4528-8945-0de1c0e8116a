<script lang="ts" setup>
import { SelectListItem } from "@/types/base"
import { generateShortUUid } from "@/utils/uuidUtil"
import { watchPausable } from "@vueuse/core"
import OverlayPanel from "primevue/overlaypanel"
import { computed, ref, toRaw, toValue, watch } from "vue"

defineOptions({
  inheritAttrs: false,
})

const DefaultOptionLabel = "label"
const DefaultOptionValue = "value"
const DefaultOptionDisabled = "disabled"
//const { t } = useI18n()

export type FunctionOptionCallback = () => string

const props = withDefaults(
  defineProps<{
    /**
     * Nếu true thì không cần hiện nút apply. model được cập nhật ngay khi chọn.
     * @default false
     */
    autoApply?: boolean
    /**
     * Nếu true: support hiển thị filter. Tạm thời chỉ hỗ trợ filter trên dữ liệu options có sẵn.
     */
    filter?: boolean
    filterPlaceholder?: string
    selectAllAllowed?: boolean
    selectAllLabel?: string
    selectAllValue?: any[]
    inputId?: string
    /**
     * option items of the component.
     * @default []
     */
    options: any[]
    /**
     * Property name or getter function to use as the label of an option.
     * @default "label"
     */
    optionLabel?: string | FunctionOptionCallback
    /**
     * Property name or getter function to use as the value of an option, defaults to the option itself when not defined.
     * @default "value"
     */
    optionValue?: string | FunctionOptionCallback
    /**
     * Property name or getter function to use as the disabled flag of an option, defaults to false when not defined.
     * @default "disabled"
     */
    optionDisabled?: string | FunctionOptionCallback

    isInstallmentSetting?: boolean
  }>(),
  {
    selectAllAllowed: false,
    autoApply: false,
    filter: false,
    filterPlaceholder: undefined,
    inputId(props) {
      return generateShortUUid()
    },
    selectAllValue(props) {
      return []
    },
    options(props) {
      return []
    },
    selectAllLabel: "All",
    optionLabel: DefaultOptionLabel,
    optionValue: DefaultOptionValue,
    optionDisabled: DefaultOptionDisabled,
  }
)

const [visibleModel] = defineModel<boolean>("visible", {
  default: false,
})

const [modelValueModel] = defineModel<any[]>("modelValue", {
  default: [],
})

const [filterValueModel] = defineModel<string | undefined>("filterValue", {
  default: undefined,
})

const [isSeclectedAllModel] = defineModel<boolean>("isSeclectedAll", {
  default: false,
})

const emit = defineEmits<{
  /**
   * Emits whenever the picker opens/closes overlay panel
   */
  toggle: [open: boolean]
  /**
   * Callback to invoke when the overlay is shown.
   *
   * @memberof OverlayPanel
   */
  show: []
  /**
   * Callback to invoke when the overlay is hidden.
   *
   * @memberof OverlayPanel
   */
  hide: []
  /**
   * Emits when the user click on apply button
   * @param value - json object containing the dates: {startDate, endDate}
   */
  apply: [value: any[]]

  /**
   * Emits when user enter value in input
   * @param value - string text that to filter
   **/
  filter: [value?: string]
}>()

const opRef = ref<InstanceType<typeof OverlayPanel> | null>(null)

const _isSelectedAll = ref<boolean>(false) // 2 state: true: select all; false: otherwise
const _modelValue = ref<any[]>([]) // chỉ set khi onShow
const showSelectAll = computed(() => props.selectAllAllowed && !filterValueModel.value)

const items = computed(() => {
  const optionLabel = typeof props.optionLabel === "string" ? props.optionLabel : props.optionLabel()
  const optionValue = typeof props.optionValue === "string" ? props.optionValue : props.optionValue()
  const optionDisabled = typeof props.optionDisabled === "string" ? props.optionDisabled : props.optionDisabled()
  return $.map(props.options, (item) => {
    return {
      data: item,
      label: item[optionLabel],
      value: item[optionValue],
      disabled: item[optionDisabled] ?? false,
    } as SelectListItem
  })
})

function compareArray(array1: any[], array2: any[]) {
  return JSON.stringify(array1.sort()) == JSON.stringify(array2.sort())
}

//#region process autoApply
const {
  pause: pauseAutoApply,
  resume: resumeAutoApply,
  isActive: isActiveAutoApply,
} = watchPausable(
  _modelValue,
  () => {
    if (props.autoApply) onApplyData()
  },
  {
    deep: true,
  }
)

watch(
  () => props.autoApply,
  (autoApply) => {
    console.log("DropdownMultipleSelectOverlayPanel", "props.autoApply", autoApply)
    if (autoApply) {
      resumeAutoApply()
    } else {
      pauseAutoApply()
    }
  },
  {
    immediate: true,
  }
)
//#endregion

function onShow() {
  // Reset trạng thái theo model truyền vào
  _modelValue.value = modelValueModel.value
  _isSelectedAll.value = isSeclectedAllModel.value

  // Trường hợp có check all mà ko có item nào được chọn -> trường hợp check all
  if (_isSelectedAll.value == true && _modelValue.value.length == 0) {
    _modelValue.value = items.value.map((item) => item.value)
  }
  // clear filter
  filterValueModel.value = undefined
}

function onHide() {
  // clear filter
  filterValueModel.value = undefined
  if (!props.isInstallmentSetting && modelValueModel.value.length <= 0) {
    isSeclectedAllModel.value = true
  }
}

//#region Public methods

/**
 * Toggles the visibility of the overlay.
 * @param {Event} event - Browser event.
 * @param {*} [target] - Optional target if event.currentTarget should not be used.
 *
 * @memberof OverlayPanel
 */
const toggle = (event: Event, target?: any): void => {
  opRef.value?.toggle(event, target)
}

/**
 * Shows the overlay.
 * @param {Event} event - Browser event.
 * @param {*} [target] - Optional target if event.currentTarget should not be used.
 *
 * @memberof OverlayPanel
 */
const show = (event: Event, target?: any): void => {
  opRef.value?.show(event, target)
}

/**
 * Hides the overlay.
 *
 * @memberof OverlayPanel
 */
const hide = (): void => {
  opRef.value?.hide()
}

defineExpose({
  toggle,
  show,
  hide,
})

//#endregion Public methods

function onClickSelectAll() {
  const currentState = _isSelectedAll.value
  const newState = !currentState

  _isSelectedAll.value = !currentState 
  _modelValue.value = newState == true ? items.value.map((item) => item.value) : []
}

function onClickSelectItem(itemValue: any) {
  const modelValue = _modelValue.value
  const optionItems = items.value
  const currentState = _isSelectedAll.value

  const isCheckedAll = compareArray(
    modelValue,
    optionItems.map((item) => item.value)
  )
  const isCheckedNone = modelValue.length == 0

  if (isCheckedAll) {
    if (currentState == false) _isSelectedAll.value = true
  } else {
    if (currentState == true) _isSelectedAll.value = false
  }
}

function onApplyData() {
  const isSelectAll = _isSelectedAll.value
  const selectedValues = isSelectAll ? props.selectAllValue : _modelValue.value
  console.log("DropdownMultipleSelectOverlayPanel", "execute applyData", {
    isSelectAll,
    selectedValues : selectedValues.join(","),
  })
  isSeclectedAllModel.value = isSelectAll
  modelValueModel.value = selectedValues
  emit("apply", modelValueModel.value)
}

const onClickApply = () => {
  onApplyData()
  hide()
}

function onToggle(open: boolean) {
  //console.log('AppDateRangPicker.onToggle', open)
  visibleModel.value = open
  emit("toggle", open)
  if (open) {
    emit("show")
  } else {
    emit("hide")
  }
}

watch(filterValueModel, (filterValue) => {
  emit("filter", filterValue)
})
</script>

<template>
  <OverlayPanel
    ref="opRef"
    class="component-DropdownMultipleSelectOverlayPanel"
    :class="{
      'auto-apply': props.autoApply,
      'manual-apply': !props.autoApply,
      //'installment-setting-dialog': isInstallmentSetting,
      //'installment-setting-period-dialog': isInstallmentSettingPeriod,
    }"
    v-bind="{ ...$attrs }"
    @show="
      () => {
        onToggle(true)
        onShow()
      }
    "
    @hide="
      () => {
        onToggle(false)
        onHide()
      }
    "
  >
    <div class="select-item-container">
      <div class="select-item-header">
        <InputTextFilterClearable
          v-if="filter"
          v-model:model-value="filterValueModel"
          :placeholder="filterPlaceholder"
        ></InputTextFilterClearable>

        <div v-show="showSelectAll" class="select-item checkAll">
          <Checkbox
            :model-value="_isSelectedAll"
            :binary="true"
            :input-id="`${props.inputId}_ALL`"
            @change="() => onClickSelectAll()"
          />
          <!-- <TriStateCheckbox 
            v-model="isSeclectedAllTemp"
            :input-id="`${props.inputId}_ALL`"
            @change="onClickSelectAll()"
          >
          </TriStateCheckbox> -->

          <label class="form-check-label" :for="`${props.inputId}_ALL`" style="font-weight: 600">
            {{ props.selectAllLabel }}
            <!-- {{ $t("common.payment-link-status.All") }} -->
          </label>
        </div>
      </div>
      <div class="select-item-body select-item-wrapper">
        <div v-for="item of items" :key="String(item.value)" class="select-item">
          <Checkbox
            v-model="_modelValue"
            :input-id="`${props.inputId}_${item.value}`"
            :value="item.value"
            @change="() => onClickSelectItem(item.value)"
          />
          <label class="form-check-label" :for="`${props.inputId}_${item.value}`">
            <!-- {{ fomatLabel(item.label) }} -->
            {{ item.label }}
            <!-- {{ $t("common.payment-link-status.", {label: item.label}) }} -->
          </label>
        </div>
      </div>
      <div v-if="!autoApply" class="select-item-footer">
        <Button v-if="!autoApply" class="btn-apply" :label="$t('common.buttons.apply')" @click="onClickApply" />
      </div>
    </div>
  </OverlayPanel>
</template>

<style lang="scss">
.component-DropdownMultipleSelectOverlayPanel {
  min-width: 12.5rem;
  margin-top: 0.375rem;

  .p-overlaypanel-content {
    padding: 1rem;
  }

  .checkAll {
    font-weight: 600;
    line-height: 1.25rem; /* 142.857% */
  }

  .btn-apply {
    width: 100%;
    padding: 0.375rem 1.25rem;
  }

  $select-items-gap: 20px;

  .select-item-container,
  .select-item-header,
  .select-item-body,
  .select-item-wrapper,
  .select-item-footer {
    display: flex;
    flex-direction: column;
    gap: $select-items-gap;
  }

  .select-item-wrapper {
    overflow: auto;
    max-height: 300px;
  }

  .select-item {
    display: flex;
    gap: 1rem;
  }
}
</style>
