<script lang="ts" setup>
  import SearchIcon from "@assets/icons/search.svg"
  import InputText from "primevue/inputtext"
  //import { EmitFn } from "primevue/ts-helpers";
  //type Simplify<T> = { [K in keyof T]: T[K] }

  defineOptions({
    inheritAttrs: false,
  })

  const props = withDefaults(defineProps<AppFilterInputProps>(), {})
  //const emits = defineEmits<EmitFn<AppFilterInputEmits>>()
  //const emits = defineEmits<Simplify<AppFilterInputEmits>>()
  //const emits = defineEmits<{"update:modelValue":[value: string | undefined]}>()
  //emits('update:modelValue')
</script>

<script lang="ts">
  import type { InputTextProps, InputTextEmits } from "primevue/inputtext"

  export * as InputText from "primevue/inputtext"

  export interface AppFilterInputProps extends /* @vue-ignore */ InputTextProps {
    iconClass?: string
    inputTextClass?: string
  }

  // export interface AppFilterInputEmits extends /* @vue-ignore */ InputTextEmits {
  // }

  export interface AppFilterInputEmits {
    /**
     * Emitted when the value changes.
     * @param {string} value - New value.
     */
    "update:modelValue"(value: string | undefined): void
  }
</script>

<template>
  <IconField icon-position="left" class="p-icon-field-search">
    <InputIcon>
      <!-- <SearchIcon /> -->
      <SvgIcon :src="SearchIcon" :class="props.iconClass" />
    </InputIcon>
    <InputText v-bind="{ ...$attrs }" />
  </IconField>
</template>
