<script lang="ts" setup>
import { formatRangeDateOnly, formatRangeDateTime } from "@/utils/formatUtil";
import type OverlayPanel from "primevue/overlaypanel";
import { computed, ref, watch } from "vue";
import type { AppDateRangPickerInlineValueModelType } from "./AppDateRangPickerInline.vue";

defineOptions({
  inheritAttrs: false,
})

export type AppDateRangPickerOverlayPanelValueModelType = AppDateRangPickerInlineValueModelType

const props = withDefaults(
  defineProps<{
    /**
     * minimum date allowed to be selected
     * @default null
     */
    minDate?: string | Date | null
    /**
     * maximum date allowed to be selected
     * @default null
     */
    maxDate?: string | Date | null
    /**
     * Show the dropdowns for time (hour/minute) selection below the calendars
     */
    timePicker?: boolean
    /**
     * Disabled state. If true picker do not popup on click.
     */
    disabled?: boolean
    /**
     * Makes the picker readonly. No button in footer. No ranges. Cannot change.
     */
    readonly?: boolean
  }>(),
  {
    minDate: null,
    maxDate: null,
    timePicker: false,
    disabled: false,
  }
)

const [visibleModel, visibleModelModifiers] = defineModel<boolean>("visible", {
  default: false,
})

const [modelValueModel, modelValueModelModifiers] = defineModel<AppDateRangPickerOverlayPanelValueModelType>("modelValue", {
  default: {
    startDate: null,
    endDate: null,
  },
})

const [startDateModel, startDateModelModifiers] = defineModel<Date>("startDate", {})

const [endDateModel, endDateModelModifiers] = defineModel<Date>("endDate", {})

const emit = defineEmits<{
  /**
   * Emits whenever the picker opens/closes overlay panel
   */
  toggle: [open: boolean],
  /**
   * Callback to invoke when the overlay is shown.
   *
   * @memberof OverlayPanel
   */
  show: [],
  /**
   * Callback to invoke when the overlay is hidden.
   *
   * @memberof OverlayPanel
   */
  hide: [],
  /**
   * Emits when the user selects a range from the picker
   * @param value - json object containing the dates: {startDate, endDate}
   */
  update: [value: AppDateRangPickerInlineValueModelType],
  /**
   * Emits when the user click on apply button
   * @param value - json object containing the dates: {startDate, endDate}
   */
  apply: [value: AppDateRangPickerInlineValueModelType],
  /**
   * Emits when the user click on cancel button.
   */
  cancel: [],
}>()

const opRef = ref<InstanceType<typeof OverlayPanel> | null>(null)

const $modelValue = ref<AppDateRangPickerInlineValueModelType>({})

const dateRangeText = computed(() => {
  const value = $modelValue.value
  return props.timePicker
    ? formatRangeDateTime(value.startDate, value.endDate)
    : formatRangeDateOnly(value.startDate, value.endDate)
})

watch(
  modelValueModel,
  (value) => {
    startDateModel.value = value?.startDate
    endDateModel.value = value?.endDate
    $modelValue.value = value
  },
  {
    immediate: true,
    deep: true,
  }
)

//#region Public methods

/**
 * Toggles the visibility of the overlay.
 * @param {Event} event - Browser event.
 * @param {*} [target] - Optional target if event.currentTarget should not be used.
 *
 * @memberof OverlayPanel
 */
const toggle = (event: Event, target?: any): void => {
  opRef.value?.toggle(event, target)
}

/**
 * Shows the overlay.
 * @param {Event} event - Browser event.
 * @param {*} [target] - Optional target if event.currentTarget should not be used.
 *
 * @memberof OverlayPanel
 */
const show = (event: Event, target?: any): void => {
  opRef.value?.show(event, target)
}

/**
 * Hides the overlay.
 *
 * @memberof OverlayPanel
 */
const hide = (): void => {
  opRef.value?.hide()
}

defineExpose({
  toggle,
  show,
  hide,
})

//#endregion Public methods

//#region Private methods

function onToggle(open: boolean) {
  //console.log('AppDateRangPicker.onToggle', open)
  visibleModel.value = open
  emit("toggle", open)
  if (open) {
    emit("show")
  } else {
    emit("hide")
  }
}

function onUpdate(value: AppDateRangPickerInlineValueModelType) {
  //console.log('AppDateRangPickerOverlayPanel.onUpdate', value)
  emit("update", value)
}

function onClickApply() {
  // appy to v-model:modelValueModel
  modelValueModel.value = $modelValue.value
  emit("apply", modelValueModel.value)
  hide()
}

function onClickCancel() {
  emit("cancel")
  hide()
}

//#endregion Private methods
</script>

<template>
  <OverlayPanel
    ref="opRef"
    class="component-opDateRange"
    v-bind="{ ...$attrs }"
    @show="onToggle(true)"
    @hide="onToggle(false)"
  >
    <div class="opDateRange-body">
      <AppDateRangPickerInline
        v-model:modelValue="$modelValue"
        v-model:startDate="startDateModel"
        v-model:endDate="endDateModel"
        opens="inline"
        :time-picker="props.timePicker"
        :readonly="props.readonly"
        :disabled="props.disabled"
        :max-date="props.maxDate"
        :min-date="props.minDate"
        :date-range="$modelValue"
        :ranges="false"
        :always-show-calendars="true"
        :auto-apply="true"
        @update="onUpdate"
      />
    </div>
    <hr class="opDateRange-separate" />
    <div class="opDateRange-footer">
      <div class="opDateRange-footer-daterange-text">
        {{ dateRangeText }}
      </div>
      <div class="opDateRange-footer-daterange-buttons">
        <Button severity="secondary" label="Cancel" @click="onClickCancel()"></Button>
        <Button severity="primary" label="Apply" @click="onClickApply()"></Button>
      </div>
    </div>
  </OverlayPanel>
</template>

<style lang="scss">
.component-opDateRange {
  display: inline-block;

  .p-overlaypanel-content {
    padding: 0;
  }

  .opDateRange-separate {
    margin: 0 24px;
  }

  .opDateRange-footer {
    display: flex;
    align-items: center;
    padding: 16px 24px;

    .opDateRange-footer-daterange-text {
      flex-grow: 1;
      color: #6c6c6c;
    }

    .opDateRange-footer-daterange-buttons {
      display: flex;
      gap: 1rem;
    }
  }
}
</style>
