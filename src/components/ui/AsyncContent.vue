<script lang="ts" setup generic="T, U extends any[]">
import { useLoadingWithMinTime } from "@/utils/promiseUtil"
import ErrorImg from "@assets/images/errors/Error.png"
import { computed } from "vue"
import { Task } from "vue-concurrency"

defineOptions({
  inheritAttrs: false,
})

/**
 * @example
 * <template>
  <AsyncContent :task="myTask" v-slot="{ lastValue }">
    <div v-if="lastValue">
      {{ lastValue }}
    </div>
  </AsyncContent>
</template>
 */

const props = defineProps<{
  // Ưu tiên sử dụng task để quản lý hiển thị.
  task?: Task<T, U>
  // Sử dụng cho trường hợp ko sử dụng task.
  loading?: boolean
  // Sử dụng cho trường hợp ko sử dụng task.
  error?: Error
  // Sử dụng cho trường hợp ko sử dụng task.
  data?: T
}>()

//const isRunning = ref<boolean>(false)
//const isError = ref<boolean>(false)
//const hasTask = computed(() => (!!props.task))

const isLoading = computed(() => (props.task ? Boolean(props.task.isRunning) : Boolean(props.loading)))
const isError = computed(() => (props.task ? Boolean(props.task.isError) : Boolean(props.error)))

const isLoadingDelay = useLoadingWithMinTime(isLoading, 500)

const lastValue = computed(() => (props.task ? props.task.last?.value : props.data))
const lastError = computed(() => (props.task ? props.task.last?.error : props.error))

// watchDebounced(
//   [() => props.task?.isRunning, () => props.loading],
//   ([isTaskRunning, isPropLoading]) => {
//     if (props.task) {
//       isRunning.value = Boolean(isTaskRunning)
//     } else {
//       isRunning.value = Boolean(isPropLoading)
//     }
//   },
//   {
//     immediate: true,
//     debounce: 200,
//     maxWait: 500,
//   }
// )

// watchDebounced(
//   [() => props.task?.isError, () => props.error],
//   ([isTaskError, isPropError]) => {
//     if (props.task) {
//       isError.value = Boolean(isTaskError)
//     } else {
//       isError.value = Boolean(isPropError)
//     }
//   },
//   {
//     immediate: true,
//     debounce: 200,
//     maxWait: 500,
//   }
// )
</script>

<template>
  <slot v-if="isLoadingDelay" name="loading">
    <AppLoading></AppLoading>
  </slot>
  <slot v-else-if="isError" name="error" :error="lastError">
    <div class="component-asyncContent" v-bind="{ ...$attrs }">
      <div class="card card--error card--error-other">
        <div class="card-body">
          <div class="card-body-img">
            <img :src="ErrorImg" alt="error" />
          </div>
          <div class="card-body-title">
            <div class="card-title">{{ $t("component-async-content.error") }}</div>
            <div class="card-subtitle" data-code="">
              {{ lastError.message || "The request has failed" }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </slot>
  <slot v-else :last-value="lastValue"></slot>
</template>

<style lang="scss">
.component-asyncContent {
  min-width: 100%;
  min-height: 100%;

  padding: 1rem;

  .card--error {
    max-width: 75rem;
    min-height: 19.75rem;
    margin: 0 auto;

    .card-body {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 3rem 1.5rem;

      gap: 1rem;

      .is-mobile & {
        flex-direction: column-reverse;
        gap: 0.75rem;

        padding: 1.5rem 1rem;
        text-align: center;
      }
    }
  }

  .card-body-title {
    width: 20rem;
    text-align: center;
  }
  .card-body-img {
    width: 20rem;

    > img {
      width: 100%;
    }
  }

  .card-title {
    color: var(--text-headings, #1c1c1c);
    /* Inter/H1/24_Semibold */
    font-size: var(--Typeface-size-2xl, 1.5rem);
    font-style: normal;
    font-weight: 600;
    line-height: 2.25rem; /* 150% */

    margin-bottom: 0.25rem;
  }
  .card-subtitle {
    color: var(--text-tertiary, #6c6c6c);
    font-size: var(--Typeface-size-md, 0.875rem);
    font-style: normal;
    font-weight: 400;
    line-height: 1.25rem; /* 142.857% */
  }
}
</style>
