<script setup lang="ts">
import primevueUtil from "@/utils/primevueUtil"
import Textarea, { type TextareaProps, type TextareaEmits, type TextareaSlots } from "primevue/textarea"
import { computed, ref, type TextareaHTMLAttributes } from "vue";

defineOptions({
  inheritAttrs: false
})

export interface TextareaMaxLengthProps extends /* @vue-ignore */ TextareaProps {
  maxlength: number | undefined
}

const props = withDefaults(defineProps<TextareaMaxLengthProps>(), {
  ...{},
  ...primevueUtil.getDefaultProps(Textarea),
  // Extend or change defaults value of props here:
})

const lengthCount = computed(() => modelValueModel.value?.length ?? 0)
const maxlength = computed(() => props.maxlength)

/**
 * Emitted when the value changes.
 * @param {boolean} value - New value.
 */
const [modelValueModel] = defineModel<Nullable<string>>("modelValue")

// const emit = defineEmits<{
//   /**
//    * Callback to invoke when a tab toggle.
//    */
//   toggle: [event: PanelToggleEvent] // named tuple syntax
// }>()

</script>

<template>
  <div class="com-textarea-maxlength-wrapper">
    <Textarea
      v-bind="{ ...$attrs, ...$props, ...$emit }"
      v-model:modelValue="modelValueModel"
      rows="3"
      class="com-textarea-maxlength"
    />
    <span v-if="maxlength && maxlength > 0" class="text-count" v-text="`${lengthCount}/${maxlength}`"></span>
  </div>
</template>

<style lang="scss">
.com-textarea-maxlength-wrapper {
  display: inline-block;
  position: relative;

  textarea.com-textarea-maxlength {
    padding-bottom: 1rem;
  }

  .text-count {
    position: absolute;
    right: 1rem;
    bottom: 0.5rem;

    color: var(--text-disabled2, #BABABA);
    /* Inter/B3/12_Regular */
    font-size: var(--Typeface-size-sm, .75rem);
    font-style: normal;
    font-weight: 400;
    line-height: 1rem; /* 133.333% */
  }
}
</style>
