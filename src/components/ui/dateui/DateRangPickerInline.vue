<script lang="ts" setup>
import { DateRangModel } from "@/types/base"
import moment from "moment";
import { watch, computed } from "vue"
import DateRangePicker from "vue3-daterange-picker"
import { useLocaleService } from "@/services/localeService"

defineOptions({
  inheritAttrs: false,
})

export type DateRangPickerInline_ClassesType = {
  off: boolean
  weekend: boolean
  today: boolean
  active: boolean
  "in-range": boolean
  "start-date": boolean
  "end-date": boolean
  disabled: boolean
}

export type DateRangPickerInline_DateFormatFnCallback = (
  classes: DateRangPickerInline_ClassesType,
  date: Date
) => DateRangPickerInline_ClassesType

const props = withDefaults(
  defineProps<{
    /**
     * minimum date allowed to be selected
     * @default null
     */
    minDate?: string | Date | null
    /**
     * maximum date allowed to be selected
     * @default null
     */
    maxDate?: string | Date | null
    /**
     * G<PERSON>ới hạn số ngày đượ<PERSON> chọn.
     * @default null
     */
    limitDays?: number | null
    /**
     * Show the week numbers on the left side of the calendar
     */
    showWeekNumbers?: boolean
    /**
     * Only show a single calendar, with or without ranges.
     *
     * Set true or 'single' for a single calendar with no ranges, single dates only.
     * Set 'range' for a single calendar WITH ranges.
     * Set false for a double calendar with ranges.
     */
    singleDatePicker?: boolean | string
    /**
     * Show the dropdowns for month and year selection above the calendars
     */
    showDropdowns?: boolean
    /**
     * Show the dropdowns for time (hour/minute) selection below the calendars
     */
    timePicker?: boolean
    /**
     * Determines the increment of minutes in the minute dropdown
     */
    timePickerIncrement?: number
    /**
     * Use 24h format for the time
     */
    timePicker24Hour?: boolean
    /**
     * Allows you to select seconds except hour/minute
     */
    timePickerSeconds?: boolean
    /**
     * Auto apply selected range. If false you need to click an apply button
     */
    autoApply?: boolean
    /**
     * Object containing locale data used by the picker. See example below the table
     *
     * @default *see below
     */
    localeData?: object
    /**
     * Ẩn/Hiện menu ranges bên trái.
     */
    showRangesMenu?: boolean
    /**
     * You can set this to false in order to hide the ranges selection. Otherwise it is an object with key/value. See below
     * @default *see below
     */
    ranges?: object | boolean
    /**
     * which way the picker opens - "center", "left", "right" or "inline"
     */
    opens?: "center" | "left" | "right" | "inline"
    /**
       function(classes, date) - special prop type function which accepts 2 params:
        "classes" - the classes that the component's logic has defined,
        "date" - tha date currently processed.
        You should return Vue class object which should be applied to the date rendered.
      */
    // eslint-disable-next-line vue/require-default-prop
    dateFormat?: DateRangPickerInline_DateFormatFnCallback
    /**
     * If set to false and one of the predefined ranges is selected then calendars are hidden.
     * If no range is selected or you have clicked the "Custom ranges" then the calendars are shown.
     */
    alwaysShowCalendars?: boolean
    /**
     * Disabled state. If true picker do not popup on click.
     */
    disabled?: boolean
    /**
     * Class of html picker control container
     */
    controlContainerClass?: object | string
    /**
     * Append the dropdown element to the end of the body
     * and size/position it dynamically. Use it if you have
     * overflow or z-index issues.
     * @type {Boolean}
     */
    appendToBody?: boolean
    /**
     * Whether to close the dropdown on "esc"
     */
    closeOnEsc?: boolean
    /**
     * Makes the picker readonly. No button in footer. No ranges. Cannot change.
     */
    readonly?: boolean
    /**
     * This is the v-model prop which the component uses. This should be an object containing startDate and endDate props.
     * Each of the props should be a string which can be parsed by Date, or preferably a Date Object.
     * @default {
     * startDate: null,
     * endDate: null
     * }
     */
    dateRange?: object
  }>(),
  {
    minDate: null,
    maxDate: null,
    limitDays: null,
    showWeekNumbers: false,
    singleDatePicker: false,
    showDropdowns: false,
    timePicker: false,
    timePickerIncrement: 1,
    timePicker24Hour: true,
    timePickerSeconds: false,
    autoApply: false,
    localeData(props) {
      return {}
    },
    showRangesMenu: true,
    ranges(props) {
      let today = new Date()
      today.setHours(0, 0, 0, 0)
      let todayEnd = new Date()
      todayEnd.setHours(11, 59, 59, 999)

      let yesterdayStart = new Date()
      yesterdayStart.setDate(today.getDate() - 1)
      yesterdayStart.setHours(0, 0, 0, 0)

      let yesterdayEnd = new Date()
      yesterdayEnd.setDate(today.getDate() - 1)
      yesterdayEnd.setHours(11, 59, 59, 999)

      let thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1)
      let thisMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0, 11, 59, 59, 999)

      return {
        Today: [today, todayEnd],
        Yesterday: [yesterdayStart, yesterdayEnd],
        "This month": [thisMonthStart, thisMonthEnd],
        "This year": [new Date(today.getFullYear(), 0, 1), new Date(today.getFullYear(), 11, 31, 11, 59, 59, 999)],
        "Last month": [
          new Date(today.getFullYear(), today.getMonth() - 1, 1),
          new Date(today.getFullYear(), today.getMonth(), 0, 11, 59, 59, 999),
        ],
      }
    },
    opens: "center",
    alwaysShowCalendars: true,
    disabled: false,
    controlContainerClass: "form-control reportrange-text",
    appendToBody: false,
    closeOnEsc: true,
    dateRange(props) {
      return {
        startDate: null,
        endDate: null,
      }
    },
  }
)

const [modelValueModel, modelValueModelModifiers] = defineModel<DateRangModel>("modelValue", {
  default: {
    startDate: null,
    endDate: null,
  },
})

const [startDateModel, startDateModelModifiers] = defineModel<Date>("startDate", {})

const [endDateModel, endDateModelModifiers] = defineModel<Date>("endDate", {})

watch(
  modelValueModel,
  (value) => {
    startDateModel.value = value?.startDate
    endDateModel.value = value?.endDate

    if (!props.timePicker) {
      // Nếu chỉ filter theo ngày thì thực hiện từ đầu ngày tới cuối ngày.
      startDateModel.value = moment(startDateModel.value).startOf("day").toDate()
      endDateModel.value = moment(endDateModel.value).endOf("day").toDate()
    }
  },
  {
    immediate: true,
    deep: true,
  }
)

const localeService = useLocaleService()

const daysOfWeek = computed(() => {
  if (localeService.locale === 'vi') {
    return ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7']
  }

  return ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa']
})

const monthNames = computed(() => {
  if (localeService.locale === 'vi') {
    return ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6', 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12']
  }

  return ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
})

// function onElementVisibility(state) {
//   console.log("onElementVisibility", state) // not work
//   if (state) {
//     $(".hourselect", ".vue-daterange-picker-tms").removeClass("mr-1")
//     $(".minuteselect", ".vue-daterange-picker-tms").removeClass("ml-1")
//   }
// }

const emit = defineEmits<{
  /**
   * Emits when the user selects a range from the picker and clicks "apply" (if autoApply is true).
   * @param value - json object containing the dates: {startDate, endDate}
   */
  update: [value: DateRangModel] // Thằng vue3-daterange-picker trong code của nó thực tế ko có dù trong tài liệu có ghi https://innologica.github.io/vue2-daterange-picker/#events. -> tự tạo =@update:modelValue
  /**
   * Emits when the user selects a range from the picker.
   * @param value - json object containing the dates: {startDate, endDate}
   */
  select: [value: DateRangModel]
}>()

function onUpdate(value: DateRangModel) {
  //console.log('AppDateRangPickerInline.onUpdate', value)
  if (props.limitDays && value.startDate && value.endDate) {
    const start = value.startDate
    const end = value.endDate
    //const endCheck = addDays(start, props.limitDays)
    //const endCheck = moment(start).add(props.limitDays, "day").endOf("day").toDate()
    // Dựa theo logic hiện tại:
    // Logic cắt: từ ngày T năm N đến ngày T-1 năm N+1. Ví dụ: chọn 01/04/2023 đến 05/06/2024, cắt thành 01/04/2023 đến 31/03/2024
    const endCheck = moment(start).add(1, "year").add(-1, "day").endOf("day").toDate()
    if (end > endCheck) {
      value.endDate = endCheck

      //console.log('AppDateRangPickerInline.onUpdate (limitDays updated)', value)
    }
  }

  modelValueModel.value = value
  emit("update", value)
}

function onSelect(value: DateRangModel) {
  //console.log('AppDateRangPickerInline.onSelect', value)
  emit("select", value)
}

function addDays(date: Date | string, days: number): Date {
  var result = new Date(date)
  result.setDate(result.getDate() + days)
  return result
}
</script>

<template>
  <div class="vue-daterange-picker-tms">
    <!-- :date-range="props.dateRange" -->
    <DateRangePicker
      v-bind="{ ...$attrs, ...$props }"
      :model-value="modelValueModel"
      :date-range="modelValueModel"
      opens="inline"
      :single-date-picker="props.singleDatePicker"
      :time-picker="props.timePicker"
      :time-picker24-hour="props.timePicker24Hour"
      :ranges="props.showRangesMenu ? props.ranges : false"
      :min-date="props.minDate"
      :max-date="props.maxDate"
      :always-show-calendars="props.alwaysShowCalendars"
      :auto-apply="props.autoApply"
      :disabled="props.disabled"
      :readonly="props.readonly"
      :locale-data="{
        daysOfWeek: daysOfWeek,
        monthNames: monthNames,
      }"
      @select="onSelect"
      @update:model-value="onUpdate"
    >
    </DateRangePicker>
  </div>
</template>

<style lang="scss">
.vue-daterange-picker-tms {
  .vue-daterange-picker.inline {
    .reportrange-text.form-control {
      display: none; // ẩn input bị hiện do boostrap .form-control
    }
    .daterangepicker {
      border: none;
      margin-top: 0;
    }

    .daterangepicker {
      .drp-calendar {
        width: auto;
        max-width: none;
      }
    }

    .drp-calendar:not(.single) {
      &.left {
        margin-right: 16px;

        .next {
          visibility: hidden;
          border-color: transparent;
          pointer-events: none;
        }
      }
      &.right {
        margin-left: 16px;

        .prev {
          visibility: hidden;
          border-color: transparent;
          pointer-events: none;
        }
      }
    }

    // bỏ padding quanh các calendar
    .drp-calendar {
      &.left {
        padding: 0;
        .calendar-table {
          padding-right: 0;
        }
      }
      &.right {
        padding: 0;
      }
    }

    // .calendar-table {
    //   $cell-spacing: 0.5rem;
    //   //$cell-spacing: 0; // disable cell spacing

    //   table {
    //     border-collapse: separate; // Cellspacing supported
    //     border-spacing: $cell-spacing; // Cellspacing

    //     margin-left: -$cell-spacing; // bù lại khoảng trống bị tạo ra xung quanh table
    //     margin-right: -$cell-spacing; // bù lại khoảng trống bị tạo ra xung quanh table
    //   }
    // }

    .calendar-table th,
    .calendar-table td {
      $cell-width: 2rem;

      //color: var(--text-body, #404040);
      /* Inter/B2/14_Regular */
      font-size: 0.875rem;
      font-style: normal;
      font-weight: 400;
      line-height: 1.25rem; /* 142.857% */

      min-width: $cell-width;
      width: $cell-width;
      height: $cell-width;

      border: none;
      cursor: default;
    }

    .calendar-table .month {
      color: var(--text-headings, #1c1c1c);
      /* Inter/B1/16_Semibold */
      font-size: var(--Typeface-size-lg, 1rem);
      font-style: normal;
      font-weight: 600;
      line-height: 1.5rem; /* 150% */
    }

    .calendar-table .prev,
    .calendar-table .next {
      border-radius: var(--layout-radius-radius-xs, 0.25rem);
      border: 1px solid var(--border-tertiary, #e6e6e6);

      &.available {
        cursor: pointer;
      }
    }

    .calendar-time {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;

      .hourselect,
      .minuteselect,
      .ampmselect {
        border-radius: 0.1875rem;
        //border: 1px solid var(--border-info, #2e6be5);
        border: 1px solid var(--border-primary, #dcdcdc);
        &:focus {
          border-color: var(--border-info, #2e6be5);
        }
        background: var(--surface-ghost, #fff);
        text-align: center;

        color: var(--text-body, #404040);
        /* Inter/B2/14_Regular */
        font-size: var(--Typeface-size-md, 0.875rem);
        font-style: normal;
        font-weight: 400;
        line-height: 1.25rem; /* 142.857% */

        width: 2.875rem;

        margin: 0 !important;
        margin-left: 0 !important;
      }

      .secondselect {
        display: none; // tip-trick để hiển thị GUI như design
      }

      .ampmselect {
        min-width: 56px;
        //appearance: none; // dùng nếu cần ẩn dấu >
      }
    }
  }
}
</style>
