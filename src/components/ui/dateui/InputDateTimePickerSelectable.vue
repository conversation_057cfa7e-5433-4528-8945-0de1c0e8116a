<script lang="ts" setup>
import CalendarIcon from "@assets/icons/calendar.svg"
import ClearInputIcon from "@/assets/icons/clear-input.svg"

import { formatDateOnly, formatDateTime, formatRangeDateOnly, formatRangeDateTime } from "@/utils/formatUtil"
import { useFocus, useToggle, watchDebounced } from "@vueuse/core"
import OverlayPanel from "primevue/overlaypanel"
import { computed, ref, watch } from "vue"
import type { DateRangPickerInline_DateFormatFnCallback } from "./DateRangPickerInline.vue"
import { DateRangModel, SelectListItem } from "@/types/base"

defineOptions({
  inheritAttrs: false,
})

const props = withDefaults(
  defineProps<{
    /**
     * minimum date allowed to be selected
     * @default null
     */
    minDate?: string | Date | null
    /**
     * maximum date allowed to be selected
     * @default null
     */
    maxDate?: string | Date | null
    /**
     * Show the week numbers on the left side of the calendar
     */
    showWeekNumbers?: boolean
    /**
     * Show the dropdowns for time (hour/minute) selection below the calendars
     */
    timePicker?: boolean
    /**
     * Auto apply selected range. If false you need to click an apply button
     */
    autoApply?: boolean
    /**
     * You can set this to false in order to hide the ranges selection. Otherwise it is an object with key/value. See below
     * @default *see below
     */
    ranges?: object | boolean
    /**
     * which way the picker opens - "center", "left", "right" or "inline"
     */
    opens?: "center" | "left" | "right" | "inline"
    /**
     function(classes, date) - special prop type function which accepts 2 params:
      "classes" - the classes that the component's logic has defined,
      "date" - tha date currently processed.
      You should return Vue class object which should be applied to the date rendered.
      */
    // eslint-disable-next-line vue/require-default-prop
    dateFormat?: DateRangPickerInline_DateFormatFnCallback
    /**
     * Disabled state. If true picker do not popup on click.
     */
    disabled?: boolean
    /**
     * Class of html picker control container
     */
    controlContainerClass?: object | string
    /**
     * Append the dropdown element to the end of the body
     * and size/position it dynamically. Use it if you have
     * overflow or z-index issues.
     * @type {Boolean}
     */
    appendToBody?: boolean
    /**
     * Makes the picker readonly. No button in footer. No ranges. Cannot change.
     */
    readonly?: boolean
    /**
     * Cho phép input có khả năng clear.
     */
    clearable?: boolean
    type?: "daily" | "weekly" | "monthly"
    /**
     * List các options ('daily' | 'weekly' | 'monthly')
     */
    typeOptions?: SelectListItem[]
  }>(),
  {
    minDate: null,
    maxDate: null,
    showWeekNumbers: false,
    showDropdowns: false,
    timePicker: true,
    // timePickerIncrement: 5,
    // timePicker24Hour: true,
    // timePickerSeconds: false,
    autoApply: false,
    localeData(props) {
      return {}
    },
    ranges(props) {
      let today = new Date()
      today.setHours(0, 0, 0, 0)
      let todayEnd = new Date()
      todayEnd.setHours(11, 59, 59, 999)

      let yesterdayStart = new Date()
      yesterdayStart.setDate(today.getDate() - 1)
      yesterdayStart.setHours(0, 0, 0, 0)

      let yesterdayEnd = new Date()
      yesterdayEnd.setDate(today.getDate() - 1)
      yesterdayEnd.setHours(11, 59, 59, 999)

      let thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1)
      let thisMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0, 11, 59, 59, 999)

      return {
        Today: [today, todayEnd],
        Yesterday: [yesterdayStart, yesterdayEnd],
        "This month": [thisMonthStart, thisMonthEnd],
        "This year": [new Date(today.getFullYear(), 0, 1), new Date(today.getFullYear(), 11, 31, 11, 59, 59, 999)],
        "Last month": [
          new Date(today.getFullYear(), today.getMonth() - 1, 1),
          new Date(today.getFullYear(), today.getMonth(), 0, 11, 59, 59, 999),
        ],
      }
    },
    opens: "center",
    alwaysShowCalendars: true,
    disabled: false,
    controlContainerClass: "form-control reportrange-text",
    appendToBody: false,
    closeOnEsc: true,
    clearable: true,
    typeOptions() {
      return [
        { label: "Daily", value: "daily" },
        { label: "Weekly", value: "weekly" },
        { label: "Monthly", value: "monthly" },
      ]
    },
  }
)

const [typeModel] = defineModel<string>("type", {
  default: "daily",
})

const [modelValueModel, modelValueModelModifiers] = defineModel<DateRangModel | undefined>("modelValue", {
  default: {
    startDate: null,
    endDate: null,
  },
})

const emit = defineEmits<{
  /**
   * Emits whenever the picker opens/closes
   */
  toggle: [open: boolean]
  /**
   * Emits when the user selects a range from the picker and clicks "apply" (if autoApply is true).
   * @param value - json object containing the dates: {startDate, endDate}
   */
  update: [value: DateRangModel] // Thằng vue3-daterange-picker trong code của nó thực tế ko có dù trong tài liệu có ghi https://innologica.github.io/vue2-daterange-picker/#events. -> tự tạo =@update:modelValue
  /**
   * Emits when the user selects a range from the picker.
   * @param value - json object containing the dates: {startDate, endDate}
   */
  select: [value: DateRangModel]
}>()

const op = ref<InstanceType<typeof OverlayPanel>>()

const [isShowCalendar, toggleShowCalendar] = useToggle()

watch(isShowCalendar, (isShowCalendar) => {
  emit("toggle", isShowCalendar)
})

const displayTextValue = computed(() => {
  const dateRange = modelValueModel.value
  if (typeModel.value == 'daily')
    return props.timePicker ? formatDateTime(dateRange?.startDate) : formatDateOnly(dateRange?.startDate)

  return props.timePicker
    ? formatRangeDateTime(dateRange?.startDate, dateRange?.endDate)
    : formatRangeDateOnly(dateRange?.startDate, dateRange?.endDate)
})

function onSelectDate(value: DateRangModel) {
  emit("select", value)
}

function onUpdateDate(value: DateRangModel) {
  emit("update", value)
}

//#region Support ẩn hiện icon clear
const refInputText = ref<HTMLInputElement>()
const { focused: isInputTextFocused } = useFocus(refInputText)
const isInputTextFocusedDelay = ref<boolean>()
watchDebounced(
  isInputTextFocused,
  (isInputTextFocused) => {
    isInputTextFocusedDelay.value = isInputTextFocused
  },
  { debounce: 250, maxWait: 250 }
)
//#endregion
</script>

<template>
  <!-- <InputGroup>
    <Dropdown v-model="typeModel" :options="props.typeOptions" option-label="label"></Dropdown>

    <IconField class="inputtext-clearable" @click="op?.toggle($event)">
      <InputText :model-value="displayTextValue" readonly v-bind="{ ...$attrs }" />

      <InputIcon v-if="props.clearable" class="user-clickable" @click="modelValueModel = undefined">
        <SvgIcon :src="modelValueModel ? ClearInputIcon : CalendarIcon" />
      </InputIcon>
      <InputIcon v-else>
        !-- <CalendarIcon /> --
        <SvgIcon :src="CalendarIcon" />
      </InputIcon>
    </IconField>
  </InputGroup> -->

  <IconField class="inputtext-clearable">
    <InputGroup>
      <Dropdown v-model="typeModel" :options="props.typeOptions" option-label="label" option-value="value"></Dropdown>
      <InputText
        ref="refInputText"
        :model-value="displayTextValue"
        readonly
        v-bind="{ ...$attrs }"
        @click="op?.toggle($event)"
      />
    </InputGroup>

    <InputIcon
      v-if="props.clearable && modelValueModel && isInputTextFocusedDelay"
      class="user-clickable"
      style="z-index: 1"
      @click="modelValueModel = undefined"
    >
      <SvgIcon :src="ClearInputIcon" />
    </InputIcon>
    <InputIcon v-else style="z-index: 1">
      <!-- <CalendarIcon /> -->
      <SvgIcon :src="CalendarIcon" />
    </InputIcon>
  </IconField>

  <OverlayPanel
    ref="op"
    class="op-inputdatepicker-calendar"
    @show="toggleShowCalendar(true)"
    @hide="toggleShowCalendar(false)"
  >
    <div class="d-flex flex-column gap-3 justify-content-center">
      <DateRangPickerInline
        v-bind="{ ...$props }"
        v-model="modelValueModel"
        :date-range="modelValueModel"
        :single-date-picker="true"
        :auto-apply="true"
        :show-week-numbers="typeModel == 'weekly'"
        :time-picker="props.timePicker"
        :time-picker24-hour="false"
        :time-picker-seconds="true"
        :min-date="props.minDate"
        :max-date="props.maxDate"
        :ranges="false"
        :disabled="props.disabled"
        :readonly="props.readonly"
        @select="onSelectDate"
        @update:model-value="onUpdateDate"
      ></DateRangPickerInline>
    </div>
  </OverlayPanel>
</template>

<style lang="scss">
.op-inputdatepicker-calendar {
  .p-overlaypanel-content {
    padding: 1rem 1.5rem;
  }
}
</style>
