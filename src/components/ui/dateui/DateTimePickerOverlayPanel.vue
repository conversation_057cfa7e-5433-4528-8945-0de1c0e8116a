<script lang="ts" setup>
import CalendarIcon from "@assets/icons/calendar.svg"
import ClearInputIcon from "@/assets/icons/clear-input.svg"

import { formatDateOnly, formatDateTime, formatRangeDateOnly, formatRangeDateTime } from "@/utils/formatUtil"
import { useToggle } from "@vueuse/core"
import OverlayPanel from "primevue/overlaypanel"
import { computed, ref, watch } from "vue"
import type { DateRangPickerInline_DateFormatFnCallback } from "./DateRangPickerInline.vue"
import { DateRangModel } from "@/types/base"

defineOptions({
  inheritAttrs: false,
})

export type DateTimePickerOverlayPanel_ModelType = DateRangModel

const props = withDefaults(
  defineProps<{
    /**
     * minimum date allowed to be selected
     * @default null
     */
    minDate?: string | Date | null
    /**
     * maximum date allowed to be selected
     * @default null
     */
    maxDate?: string | Date | null
    /**
     * Giới hạn số ngày đư<PERSON> chọn.
     * @default null
     */
    limitDays?: number | null
    /**
     * Show the week numbers on the left side of the calendar
     */
    showWeekNumbers?: boolean
    /**
     * Only show a single calendar, with or without ranges.
     *
     * Set true or 'single' for a single calendar with no ranges, single dates only.
     * Set 'range' for a single calendar WITH ranges.
     * Set false for a double calendar with ranges.
     */
    singleDatePicker?: boolean | string
    /**
     * Show the dropdowns for time (hour/minute) selection below the calendars
     */
    timePicker?: boolean
    /**
     * Auto apply selected range. If false you need to click an apply button
     */
    autoApply?: boolean
    /**
     * You can set this to false in order to hide the ranges selection. Otherwise it is an object with key/value. See below
     * @default *see below
     */
    ranges?: object | boolean
    /**
     * which way the picker opens - "center", "left", "right" or "inline"
     */
    opens?: "center" | "left" | "right" | "inline"
    /**
     function(classes, date) - special prop type function which accepts 2 params:
      "classes" - the classes that the component's logic has defined,
      "date" - tha date currently processed.
      You should return Vue class object which should be applied to the date rendered.
      */
    // eslint-disable-next-line vue/require-default-prop
    dateFormat?: DateRangPickerInline_DateFormatFnCallback
    /**
     * Disabled state. If true picker do not popup on click.
     */
    disabled?: boolean
    /**
     * Class of html picker control container
     */
    controlContainerClass?: object | string
    /**
     * Makes the picker readonly. No button in footer. No ranges. Cannot change.
     */
    readonly?: boolean
  }>(),
  {
    minDate: null,
    maxDate: null,
    limitDays: null,
    showWeekNumbers: false,
    singleDatePicker: false,
    showDropdowns: false,
    timePicker: false,
    // timePickerIncrement: 5,
    // timePicker24Hour: true,
    // timePickerSeconds: false,
    autoApply: false,
    localeData(props) {
      return {}
    },
    ranges(props) {
      let today = new Date()
      today.setHours(0, 0, 0, 0)
      let todayEnd = new Date()
      todayEnd.setHours(11, 59, 59, 999)

      let yesterdayStart = new Date()
      yesterdayStart.setDate(today.getDate() - 1)
      yesterdayStart.setHours(0, 0, 0, 0)

      let yesterdayEnd = new Date()
      yesterdayEnd.setDate(today.getDate() - 1)
      yesterdayEnd.setHours(11, 59, 59, 999)

      let thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1)
      let thisMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0, 11, 59, 59, 999)

      return {
        Today: [today, todayEnd],
        Yesterday: [yesterdayStart, yesterdayEnd],
        "This month": [thisMonthStart, thisMonthEnd],
        "This year": [new Date(today.getFullYear(), 0, 1), new Date(today.getFullYear(), 11, 31, 11, 59, 59, 999)],
        "Last month": [
          new Date(today.getFullYear(), today.getMonth() - 1, 1),
          new Date(today.getFullYear(), today.getMonth(), 0, 11, 59, 59, 999),
        ],
      }
    },
    opens: "center",
    alwaysShowCalendars: true,
    disabled: false,
    controlContainerClass: "form-control reportrange-text",
    closeOnEsc: true,
  }
)

const [modelValueModel, modelValueModelModifiers] = defineModel<Nullable<DateRangModel>>("modelValue", {
  default: {
    startDate: null,
    endDate: null,
  },
})

const emit = defineEmits<{
  /**
   * Emits whenever the picker opens/closes
   */
  toggle: [open: boolean]
  /**
   * Emits when the user selects a range from the picker and clicks "apply" (if autoApply is true).
   * @param value - json object containing the dates: {startDate, endDate}
   */
  update: [value: DateRangModel] // Thằng vue3-daterange-picker trong code của nó thực tế ko có dù trong tài liệu có ghi https://innologica.github.io/vue2-daterange-picker/#events. -> tự tạo =@update:modelValue
  /**
   * Emits when the user selects a range from the picker.
   * @param value - json object containing the dates: {startDate, endDate}
   */
  select: [value: DateRangModel]
  /**
   * Emits when the user click on apply button
   * @param value - json object containing the dates: {startDate, endDate}
   */
  apply: [value: DateRangModel]
  /**
   * Emits when the user click on cancel button
   */
  cancel: []
}>()

const op = ref<InstanceType<typeof OverlayPanel>>()

const [isShowCalendar, toggleShowCalendar] = useToggle()

watch(isShowCalendar, (isShowCalendar) => {
  resetModelValueTemp()
  emit("toggle", isShowCalendar)
})

const modelValueTemp = ref<DateRangModel>({
  startDate: undefined,
  endDate: undefined,
})

watch(
  modelValueModel,
  (modelValueModel) => {
    resetModelValueTemp()
  },
  {
    immediate: true,
  }
)

function resetModelValueTemp() {
  modelValueTemp.value = {
    startDate: modelValueModel.value?.startDate ?? undefined,
    endDate: modelValueModel.value?.endDate ?? undefined,
  }
}

function applyModelValueTemp() {
  modelValueModel.value = {
    startDate: modelValueTemp.value?.startDate ?? undefined,
    endDate: modelValueTemp.value?.endDate ?? undefined,
  }
}

const displayTextValue = computed(() => {
  const dateRange = modelValueTemp.value
  if (props.singleDatePicker == true || props.singleDatePicker == "single")
    return props.timePicker ? formatDateTime(dateRange.startDate) : formatDateOnly(dateRange.startDate)

  return props.timePicker
    ? formatRangeDateTime(dateRange.startDate, dateRange.endDate)
    : formatRangeDateOnly(dateRange.startDate, dateRange.endDate)
})

function onSelectDate(value: DateRangModel) {
  emit("select", value)
}

function onUpdateDate(value: DateRangModel) {
  emit("update", value)
}

function onClickApply() {
  applyModelValueTemp()
  const dateRange = modelValueTemp.value
  emit("apply", dateRange)
  op.value?.hide()
}

function onClickCancel() {
  emit("cancel")
  op.value?.hide()
}

//#region Public methods

/**
 * Toggles the visibility of the overlay.
 * @param {Event} event - Browser event.
 * @param {*} [target] - Optional target if event.currentTarget should not be used.
 *
 * @memberof OverlayPanel
 */
const toggle = (event: Event, target?: any): void => {
  op.value?.toggle(event, target)
}

/**
 * Shows the overlay.
 * @param {Event} event - Browser event.
 * @param {*} [target] - Optional target if event.currentTarget should not be used.
 *
 * @memberof OverlayPanel
 */
const show = (event: Event, target?: any): void => {
  op.value?.show(event, target)
}

/**
 * Hides the overlay.
 *
 * @memberof OverlayPanel
 */
const hide = (): void => {
  op.value?.hide()
}

defineExpose({
  toggle,
  show,
  hide,
})

//#endregion
</script>

<template>
  <OverlayPanel
    ref="op"
    class="op-datetimepickeroverlaypanel-calendar"
    @show="toggleShowCalendar(true)"
    @hide="toggleShowCalendar(false)"
  >
    <div class="d-flex flex-column gap-3 justify-content-center">
      <DateRangPickerInline
        v-bind="{ ...$props }"
        v-model="modelValueTemp"
        :date-range="modelValueTemp"
        :single-date-picker="props.singleDatePicker"
        :auto-apply="true"
        :time-picker="props.timePicker"
        :time-picker24-hour="false"
        :time-picker-seconds="true"
        :ranges="false"
        :min-date="props.minDate"
        :max-date="props.maxDate"
        :limit-days="props.limitDays"
        :disabled="props.disabled"
        :readonly="props.readonly"
        @select="onSelectDate"
        @update:model-value="onUpdateDate"
      ></DateRangPickerInline>
      <div class="divider divider-horizontal m-0"></div>
      <div class="op-datetimepickeroverlaypanel-calendar-footer d-flex align-items-center justify-content-end gap-3">
        <span v-if="singleDatePicker == 'range' || !singleDatePicker">{{ displayTextValue }}</span>
        <div class="mx-auto"></div>
        <Button severity="secondary" :label="$t('common.buttons.cancel')" @click="onClickCancel()"></Button>
        <Button severity="primary" :label="$t('common.buttons.apply')" @click="onClickApply()"></Button>
      </div>
    </div>
  </OverlayPanel>
</template>

<style lang="scss">
// .op-datetimepickeroverlaypanel-calendar {
// }
</style>
