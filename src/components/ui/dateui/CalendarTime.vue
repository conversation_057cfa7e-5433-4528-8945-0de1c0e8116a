<!-- eslint-disable @intlify/vue-i18n/no-raw-text -->
<template>
  <div class="calendar-time" v-bind="{ ...$attrs }">
    <select v-model="hour" class="hourselect form-control mr-1" :disabled="readonly">
      <option v-for="h in hourOptions" :key="h" :value="h">{{ formatNumber(h) }}</option>
    </select>
    <span>:</span>
    <select v-model="minute" class="minuteselect form-control ml-1" :disabled="readonly">
      <option v-for="m in minuteOptions" :key="m" :value="m">{{ formatNumber(m) }}</option>
    </select>
    <template v-if="secondPicker">
      <span>:</span>
      <select v-model="second" class="secondselect form-control ml-1" :disabled="readonly">
        <option v-for="s in secondOptions" :key="s" :value="s">{{ formatNumber(s) }}</option>
      </select>
    </template>
    <span>:</span>
    <select v-if="!hour24" v-model="ampm" class="ampmselect" :disabled="readonly">
      <option value="AM">AM</option>
      <option value="PM">PM</option>
    </select>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  // filters: {
  // },
  props: {
    miniuteIncrement: {
      type: Number,
      default: 5,
    },
    hour24: {
      type: Boolean,
      default: true,
    },
    secondPicker: {
      type: Boolean,
      default: false,
    },
    currentTime: {
      type: Date,
      default() {
        return new Date()
      },
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  emits: {
    update: (payload: { hours: number, minutes: number, seconds: number }) => { return true }
  },
  data() {
    let current = this.currentTime ? this.currentTime : new Date()
    let hours = current.getHours()
    return {
      //hour: this.hour24 ? hours : hours % 12 || 12,
      hour: this.hour24 ? hours : hours % 12,
      minute: current.getMinutes() - (current.getMinutes() % this.miniuteIncrement),
      second: current.getSeconds(),
      ampm: hours < 12 ? "AM" : "PM",
    }
  },
  computed: {
    hourOptions() {
      let values: number[] = []
      if (this.hour24) {
        for (let i = 0; i < 24; i++) {
          values.push(i) // 0-24
        }
      } else {
        for (let i = 0; i < 12; i++) {
          values.push(i) // 0-11
        }
      }
      return values
    },
    minuteOptions() {
      let values: number[] = []
      for (let i = 0; i < 60; i = i + this.miniuteIncrement) {
        values.push(i) // 0-60 (miniuteIncrement)
      }
      return values
    },
    secondOptions() {
      let values: number[] = []
      for (let i = 0; i < 60; i = i++) {
        values.push(i) // 0-60
      }
      return values
    },
  },
  watch: {
    hour() {
      this.onChange()
    },
    minute() {
      this.onChange()
    },
    second() {
      this.onChange()
    },
    ampm() {
      this.onChange()
    },
  },
  methods: {
    formatNumber: (value: number): string => {
      if (value < 10) {
        return "0" + value.toString()
      }
      return value.toString()
    },
    getHour(): number {
      if (this.hour24) {
        return this.hour
      } else {
        if (this.hour === 12) {
          return this.ampm === "AM" ? 0 : 12
        } else {
          return this.hour + (this.ampm === "PM" ? 12 : 0)
        }
      }
    },
    onChange() {
      this.$emit("update", {
        hours: this.getHour(),
        minutes: this.minute,
        seconds: this.second,
      })
    },
  },
})
</script>
