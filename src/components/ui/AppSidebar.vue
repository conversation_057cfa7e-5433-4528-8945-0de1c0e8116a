<!-- <script lang="ts">
import Sidebar from "primevue/sidebar"

// TEST cách thức extend props+default của 3rd party components
type SidebarInstanceType = InstanceType<typeof Sidebar>
function getPrimePropsDefaults<T extends SidebarInstanceType>(component: T) {
  console.log('getPrimePropsDefaults', component.$props)
}

function getSidebarPropsDefaults() {
  const props = { ...(Sidebar as any).extends.props, ...(Sidebar as any).props }
  let propsDefaults = {}
  $.each(props, (key, value) => {
    propsDefaults[key] = value.default
  })

  //getPrimePropsDefaults(Sidebar)

  return propsDefaults;
}

//const sidebarProps = { ...(Sidebar as any).extends.props, ...(Sidebar as any).props }
//type sidebarPropsType = ExtractDefaultPropTypes<Sidebar>
//const xx: sidebarPropsType = {}
//const xx: ExtractDefaultPropTypes<Sidebar>

//type x = InstanceType<typeof Sidebar>
//type MyComponentProps = Omit<InstanceType<typeof Sidebar>, `$${string}`>
//const x : MyComponentProps | null = null

// not work
//type NewType = EmitFn<SidebarEmits>;
//type NewType = ExtractPropTypes<EmitFn<SidebarEmits>>
// const NewTypeConst = {
//     show: () => {},
//     hide: () => {},
// }

//class Modal22 implements NewType {}
//const NewTypeConst2: EmitFn<SidebarEmits> = null
//class Modal22 implements EmitFn<SidebarEmits> {}
//class Modal22 implements ExtractPropTypes<EmitFn<SidebarEmits>> {}
//const xxx = new Modal22()

export default {}
</script> -->

<script setup lang="ts">
import Sidebar from "primevue/sidebar"
import type { SidebarProps, SidebarSlots, SidebarEmits } from "primevue/sidebar"

import CloseIcon from "@assets/icons/close-currentColor.svg"
import {
  ref,
  watch,
} from "vue"
import primevueUtil from "@/utils/primevueUtil"

defineOptions({
  inheritAttrs: false,
})

//const props = withDefaults(defineProps<SidebarProps>(), {}) // Dùng thì mất giá trị default autoZIndex=true
//const props = defineProps<SidebarProps>() // Dùng thì cũng mất giá trị default autoZIndex=true như trên
// Bỏ ko dùng cả 2 thì giữ được autoZIndex=true
// -> nên chỉ định nghĩa dùng cái gì thì nhét vào chứ kế thừa thằng con kiểu dùng v-bind="{ ...$attrs, ...$props, ...$emit }" thì ko ổn

export interface AppSidebarProps extends /* @vue-ignore */ SidebarProps {
  loading?: boolean
}

const props = withDefaults(defineProps<AppSidebarProps>(), {
  //...getSidebarPropsDefaults(),
  ...primevueUtil.getDefaultProps(Sidebar),
  //...sidebarPropsType
  // Extend or change defaults value of props here:
  ...{
    loading: false,
  },
})
//const props = defineProps<SidebarProps>()
//props.autoZIndex

/**
 * Emitted when the value changes.
 * @param {boolean} value - New value.
 */
const [modelValueModel, modelValueModelModifiers] = defineModel<boolean>("visible", {
  default: false,
})

//type NewType = PropType<SidebarEmits>;
//type NewType = PropType<EmitFn<SidebarEmits>>;
//type NewType = EmitFn<SidebarEmits>;
//type NewType = ExtractPropTypes<EmitFn<SidebarEmits>>

//const emit = defineEmits(xxx)

const emit = defineEmits<{
  /**
   * Callback to invoke when sidebar gets shown.
   */
  show: [] // named tuple syntax
  /**
   * Callback to invoke when sidebar gets hidden.
   */
  hide: [] // named tuple syntax
}>()

const slots = defineSlots<{
  default(): any
  header?: () => any
}>()

const innerLoading = ref<boolean>()
watch(
  () => props.loading,
  (loading) => (innerLoading.value = loading),
  { immediate: true }
)
</script>

<template>
  <Sidebar
    v-bind="{ ...$attrs, ...$props, ...$emit }"
    v-model:visible="modelValueModel"
    class="comp-AppSideBar"
    @show="emit('show')"
    @hide="emit('hide')"
  >
    <template #container="{ closeCallback }">
      <div class="p-sidebar-header" data-pc-section="header">
        <!-- <Button
          class="p-sidebar-close p-sidebar-icon p-link"
          aria-label="Close"
          data-pc-section="closebutton"
          data-pc-group-section="iconcontainer"
        ></Button> -->
        <Button severity="download" class="p-sidebar-close p-sidebar-icon p-link" @click="closeCallback">
          <template #icon>
            <span class="p-button-icon">
              <SvgIcon :src="CloseIcon" />
            </span>
          </template>
        </Button>

        <slot name="header">
          <div class="p-sidebar-header-content">{{ props.header }}</div>
        </slot>
      </div>
      <div class="p-sidebar-content" data-pc-section="content">
        <slot></slot>
      </div>
      <AppLoading v-if="innerLoading" class="position-absolute p-component-overlay"></AppLoading>
    </template>
  </Sidebar>
</template>

<style lang="scss">
.comp-AppSideBar {
  .p-sidebar-header-content {
    width: 100%;
    text-align: center;
  }
}
</style>
