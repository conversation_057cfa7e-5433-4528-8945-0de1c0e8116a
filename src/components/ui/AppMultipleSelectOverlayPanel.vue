<script lang="ts" setup>
  import Search from '@/assets/icons/search.svg';

import APPCONFIG from "@/appConfig";
import { SelectListItem } from "@/types/base";
import { generateShortUUid } from "@/utils/uuidUtil";
import { useDebounceFn } from "@vueuse/core";
import OverlayPanel from "primevue/overlaypanel";
import { ref, watch } from "vue";

  defineOptions({
    inheritAttrs: false,
  })

  const DefaultOptionLabel = "label"
  const DefaultOptionValue = "value"
  const DefaultOptionDisabled = "disabled"

  export type FunctionOptionCallback = () => string

  const props = withDefaults(
    defineProps<{
      selectAllAllowed?: boolean
      selectAllLabel?: string
      selectAllValue?: any[]
      inputId?: string
      /**
       * option items of the component.
       * @default []
       */
      options: any[]
      /**
       * Property name or getter function to use as the label of an option.
       * @default "label"
       */
      optionLabel?: string | FunctionOptionCallback
      /**
       * Property name or getter function to use as the value of an option, defaults to the option itself when not defined.
       * @default "value"
       */
      optionValue?: string | FunctionOptionCallback
      /**
       * Property name or getter function to use as the disabled flag of an option, defaults to false when not defined.
       * @default "disabled"
       */
      optionDisabled?: string | FunctionOptionCallback

      isInstallmentSetting?: boolean
      isInstallmentSettingPeriod?: boolean
    }>(),
    {
      inputId(props) {
        return generateShortUUid()
      },
      selectAllValue(props) {
        return []
      },
      options(props) {
        return []
      },
      selectAllLabel: "All",
      optionLabel: DefaultOptionLabel,
      optionValue: DefaultOptionValue,
      optionDisabled: DefaultOptionDisabled,
    }
  )

  const [visibleModel, visibleModelModifiers] = defineModel<boolean>("visible", {
    default: false,
  })

  const [modelValueModel, modelValueModelModifiers] = defineModel<any[]>("modelValue", {
    default: [],
  })

  const [modelFilter, modelFilterModifiers] = defineModel<string>('modelFilter', {
    default: null
  })

  const [isSeclectedAllModel, isSeclectedAllModelModifiers] = defineModel<boolean>('isSeclectedAll', {
    default: false
  })

  const emit = defineEmits<{
    /**
     * Emits whenever the picker opens/closes overlay panel
     */
    toggle: [open: boolean]
    /**
     * Callback to invoke when the overlay is shown.
     *
     * @memberof OverlayPanel
     */
    show: []
    /**
     * Callback to invoke when the overlay is hidden.
     *
     * @memberof OverlayPanel
     */
    hide: []
    /**
     * Emits when the user click on apply button
     * @param value - json object containing the dates: {startDate, endDate}
     */
    apply: [value: any[]]

    /**
     * Emits when user enter value in input
     * @param value - string text that to filter
    **/
    filter: [value?: string]
  }>()

  const opRef = ref<InstanceType<typeof OverlayPanel> | null>(null)

  const items = ref<SelectListItem[]>([])
  const isSelectAll = ref<boolean>(false)
  const _modelValue = ref<any[]>([])

  //   export interface AppMultipleSelectOverlayPanelItem {
  //     data: any
  //     label: string
  //     value: any
  //     disabled: boolean
  //   }

  watch(
    () => props.options,
    (options) => {
      const optionLabel = typeof props.optionLabel === "string" ? props.optionLabel : props.optionLabel()
      const optionValue = typeof props.optionValue === "string" ? props.optionValue : props.optionValue()
      const optionDisabled = typeof props.optionDisabled === "string" ? props.optionDisabled : props.optionDisabled()
      items.value = $.map(options, (item) => {
        return {
          data: item,
          label: item[optionLabel],
          value: item[optionValue],
          disabled: item[optionDisabled] ?? false,
        } as SelectListItem
      })
    },
    {
      deep: true,
      immediate: true,
    }
  )

  // NOTE: không watch isSelectAll mà dùng onClickSelectAll
  watch(
    _modelValue,
    (values) => {
      //isSelectAll.value = !values || values.length == 0 || values.length == items.value.length
      if (isSelectAll.value && values.length != items.value.length) {
        isSelectAll.value = false
      } else if (!isSelectAll.value && values.length == items.value.length) {
        isSelectAll.value = true
      }
    },
    {
      deep: true,
      immediate: true,
    }
  )

  function onShow() {
    // Reset trạng thái theo model truyền vào
    _modelValue.value = modelValueModel.value
    if (_modelValue.value.length == 0) {
      _modelValue.value = items.value.map((item) => item.value)
    }
  }

  //#region Public methods

  /**
   * Toggles the visibility of the overlay.
   * @param {Event} event - Browser event.
   * @param {*} [target] - Optional target if event.currentTarget should not be used.
   *
   * @memberof OverlayPanel
   */
  const toggle = (event: Event, target?: any): void => {
    opRef.value?.toggle(event, target)
  }

  /**
   * Shows the overlay.
   * @param {Event} event - Browser event.
   * @param {*} [target] - Optional target if event.currentTarget should not be used.
   *
   * @memberof OverlayPanel
   */
  const show = (event: Event, target?: any): void => {
    opRef.value?.show(event, target)
  }

  /**
   * Hides the overlay.
   *
   * @memberof OverlayPanel
   */
  const hide = (): void => {
    opRef.value?.hide()
  }

  defineExpose({
    toggle,
    show,
    hide,
  })

  //#endregion Public methods

  function onClickSelectAll() {
    // Set select All hoặc unselect All
    _modelValue.value = isSelectAll.value
      ? items.value.map((item) => item.value)
      : []
    // TODO: emit nếu cần
  }

  function onClickApply() {
    isSeclectedAllModel.value = isSelectAll.value;
    modelValueModel.value = isSelectAll.value ? props.selectAllValue : _modelValue.value
    emit("apply", modelValueModel.value)
    hide()
  }

  function onToggle(open: boolean) {
    //console.log('AppDateRangPicker.onToggle', open)
    visibleModel.value = open
    emit("toggle", open)
    if (open) {
      emit("show")
    } else {
      emit("hide")
    }
  }

  const filterBank = useDebounceFn(() => {
    emit('filter', modelFilter.value);
  }, APPCONFIG.DEBOUNCED_DELAY_INPUT);
</script>

<template>
  <OverlayPanel
    ref="opRef"
    class="component-AppMultipleSelectOverlayPanel"
    :class="{ 'installment-setting-dialog': isInstallmentSetting, 'installment-setting-period-dialog': isInstallmentSettingPeriod }"
    @show="onToggle(true);onShow();"
    @hide="onToggle(false)"
  >
    <div class="p-form-check-group p-form-check-group-column">
      <InputGroup v-if="isInstallmentSetting" class="group-search-banks">
        <InputGroupAddon>
          <SvgIcon :src="Search" />
        </InputGroupAddon>
        <InputText v-model="modelFilter" placeholder="Search Bank" class="text-search-banks" @input="filterBank"></InputText>
      </InputGroup>
      <div class="p-form-check checkAll">
        <Checkbox v-model="isSelectAll" :binary="true" :input-id="`${props.inputId}_ALL`" @change="onClickSelectAll()" />
        <label class="form-check-label" :for="`${props.inputId}_ALL`" style="font-weight: 600;">
          {{ props.selectAllLabel }}
          <!-- {{ $t("common.payment-link-status.All") }} -->
        </label>
      </div>
      <div v-for="item of items" :key="String(item.value)" class="p-form-check">
        <Checkbox v-model="_modelValue" :input-id="`${props.inputId}_${item.value}`" :value="item.value" />
        <label class="form-check-label" :for="`${props.inputId}_${item.value}`">
          {{ item.label }}
          <!-- {{ $t("common.payment-link-status.", {label: item.label}) }} -->
        </label>
      </div>
    </div>

    <Button class="w-100" :label="$t('common.buttons.apply')" @click="onClickApply()" />
  </OverlayPanel>
</template>

<style lang="scss">
.component-AppMultipleSelectOverlayPanel {
  min-width: 12.5rem;
  .p-overlaypanel-content {
    padding: 1rem;
  }
  .checkAll {
    font-weight: 600;
    line-height: 1.25rem; /* 142.857% */
  }
  .p-form-check-group {
    padding-left: 0;
  }
}
</style>
