<script lang="ts" setup>
import { watch } from "vue"
import DateRangePicker from "vue3-daterange-picker"

export interface AppDateRangPickerValueModelType {
  startDate?: Date
  endDate?: Date
}

export interface AppDateRangPickerEmits {
  /**
   * Emits whenever the picker opens/closes
   * @param open - the current state of the picker
   */
  "toggle"(open: boolean): void
  /**
   * Emits when the user selects a range from the picker and clicks "apply" (if autoApply is true).
   * @param value - json object containing the dates: {startDate, endDate}
   */
  "update"(value: AppDateRangPickerValueModelType): void
}

defineOptions({
  inheritAttrs: false,
})

type AppDateRangPicker_ClassesType = {
  off: boolean,
  weekend: boolean,
  today: boolean,
  active: boolean,
  'in-range': boolean,
  'start-date': boolean,
  'end-date': boolean,
  disabled: boolean,
};

type AppDateRangPicker_DateFormatFnCallback = (classes: AppDateRangPicker_ClassesType, date: Date) => AppDateRangPicker_ClassesType;

const props = withDefaults(
  defineProps<{
    /**
     * minimum date allowed to be selected
     * @default null
     */
    minDate?: string | Date | null
    /**
     * maximum date allowed to be selected
     * @default null
     */
    maxDate?: string | Date | null
    /**
     * Show the week numbers on the left side of the calendar
     */
    showWeekNumbers?: boolean
    /**
     * Only show a single calendar, with or without ranges.
     *
     * Set true or 'single' for a single calendar with no ranges, single dates only.
     * Set 'range' for a single calendar WITH ranges.
     * Set false for a double calendar with ranges.
     */
    singleDatePicker?: boolean | string
    /**
     * Show the dropdowns for month and year selection above the calendars
     */
    showDropdowns?: boolean
    /**
     * Show the dropdowns for time (hour/minute) selection below the calendars
     */
    timePicker?: boolean
    /**
     * Determines the increment of minutes in the minute dropdown
     */
    timePickerIncrement?: number
    /**
     * Use 24h format for the time
     */
    timePicker24Hour?: boolean
    /**
     * Allows you to select seconds except hour/minute
     */
    timePickerSeconds?: boolean
    /**
     * Auto apply selected range. If false you need to click an apply button
     */
    autoApply?: boolean
    /**
     * Object containing locale data used by the picker. See example below the table
     *
     * @default *see below
     */
    localeData?: object
    /**
     * You can set this to false in order to hide the ranges selection. Otherwise it is an object with key/value. See below
     * @default *see below
     */
    ranges?: object | boolean
    /**
     * which way the picker opens - "center", "left", "right" or "inline"
     */
    opens?: "center" | "left" | "right" | "inline"
    /**
     function(classes, date) - special prop type function which accepts 2 params:
      "classes" - the classes that the component's logic has defined,
      "date" - tha date currently processed.
      You should return Vue class object which should be applied to the date rendered.
      */
    // eslint-disable-next-line vue/require-default-prop
    dateFormat?: AppDateRangPicker_DateFormatFnCallback
    /**
     * If set to false and one of the predefined ranges is selected then calendars are hidden.
     * If no range is selected or you have clicked the "Custom ranges" then the calendars are shown.
     */
    alwaysShowCalendars?: boolean
    /**
     * Disabled state. If true picker do not popup on click.
     */
    disabled?: boolean
    /**
     * Class of html picker control container
     */
    controlContainerClass?: object | string
    /**
     * Append the dropdown element to the end of the body
     * and size/position it dynamically. Use it if you have
     * overflow or z-index issues.
     * @type {Boolean}
     */
    appendToBody?: boolean
    /**
     * Whether to close the dropdown on "esc"
     */
    closeOnEsc?: boolean
    /**
     * Makes the picker readonly. No button in footer. No ranges. Cannot change.
     */
    readonly?: boolean
    /**
     * This is the v-model prop which the component uses. This should be an object containing startDate and endDate props.
     * Each of the props should be a string which can be parsed by Date, or preferably a Date Object.
     * @default {
     * startDate: null,
     * endDate: null
     * }
     */
    dateRange?: object
  }>(),
  {
    minDate: null,
    maxDate: null,
    showWeekNumbers: false,
    singleDatePicker: false,
    showDropdowns: false,
    timePicker: false,
    timePickerIncrement: 5,
    timePicker24Hour: true,
    timePickerSeconds: false,
    autoApply: false,
    localeData(props) {
      return {}
    },
    ranges(props) {
      let today = new Date()
      today.setHours(0, 0, 0, 0)
      let todayEnd = new Date()
      todayEnd.setHours(11, 59, 59, 999)

      let yesterdayStart = new Date()
      yesterdayStart.setDate(today.getDate() - 1)
      yesterdayStart.setHours(0, 0, 0, 0)

      let yesterdayEnd = new Date()
      yesterdayEnd.setDate(today.getDate() - 1)
      yesterdayEnd.setHours(11, 59, 59, 999)

      let thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1)
      let thisMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0, 11, 59, 59, 999)

      return {
        Today: [today, todayEnd],
        Yesterday: [yesterdayStart, yesterdayEnd],
        "This month": [thisMonthStart, thisMonthEnd],
        "This year": [new Date(today.getFullYear(), 0, 1), new Date(today.getFullYear(), 11, 31, 11, 59, 59, 999)],
        "Last month": [
          new Date(today.getFullYear(), today.getMonth() - 1, 1),
          new Date(today.getFullYear(), today.getMonth(), 0, 11, 59, 59, 999),
        ],
      }
    },
    opens: "center",
    alwaysShowCalendars: true,
    disabled: false,
    controlContainerClass: "form-control reportrange-text",
    appendToBody: false,
    closeOnEsc: true,
    dateRange(props) {
      return {
        startDate: null,
        endDate: null,
      }
    },
  }
)

const [modelValueModel, modelValueModelModifiers] = defineModel<AppDateRangPickerValueModelType>("modelValue", {
  default: {
    startDate: null,
    endDate: null,
  },
})

const [startDateModel, startDateModelModifiers] = defineModel<Date>("startDate", {})

const [endDateModel, endDateModelModifiers] = defineModel<Date>("endDate", {})

watch(
  modelValueModel,
  (value) => {
    startDateModel.value = value?.startDate
    endDateModel.value = value?.endDate
  },
  {
    immediate: true,
    deep: true,
  }
)

const emit = defineEmits<{
  /**
   * Emits whenever the picker opens/closes
   */
  toggle: [open: boolean],
  /**
   * Emits when the user selects a range from the picker and clicks "apply" (if autoApply is true).
   * @param value - json object containing the dates: {startDate, endDate}
   */
  update: [value: AppDateRangPickerValueModelType], // Thằng vue3-daterange-picker trong code của nó thực tế ko có dù trong tài liệu có ghi https://innologica.github.io/vue2-daterange-picker/#events. -> tự tạo =@update:modelValue
  /**
   * Emits when the user selects a range from the picker.
   * @param value - json object containing the dates: {startDate, endDate}
   */
  select: [value: AppDateRangPickerValueModelType],
  /**
   * Emits when the user click on apply button
   * @param value - json object containing the dates: {startDate, endDate}
   */
  clickApply: [value: AppDateRangPickerValueModelType],
  /**
   * Emits when the user click on cancel button
   */
  clickCancel: []
}>()

function onToggle(open: boolean) {
  //console.log('AppDateRangPicker.onToggle', open)
  emit("toggle", open)
}

function onUpdate(value: AppDateRangPickerValueModelType) {
  //console.log('AppDateRangPicker.onUpdate', value)
  emit("update", value)
}

function onSelect(value: AppDateRangPickerValueModelType) {
  //console.log('AppDateRangPicker.onSelect', value)
  emit("select", value)
}

function onClickApply() {
  //console.log('AppDateRangPicker.onClickApply', modelValueModel.value)
  emit("clickApply", modelValueModel.value)
}

function onClickCancel() {
  //console.log('AppDateRangPicker.onClickCancel', modelValueModel.value)
  emit("clickCancel")
}
</script>

<template>
  <DateRangePicker
    v-bind="{ ...$attrs, ...$props }"
    v-model="modelValueModel"
    class="vue-daterange-picker-tms"
    opens="inline"
    :time-picker="true"
    :time-picker24-hour="false"
    :date-range="{ startDate: null, endDate: null }"
    :ranges="false"
    :always-show-calendars="true"
    @toggle="onToggle"
    @select="onSelect"
    @update:model-value="onUpdate"
  >
    <!-- <template v-slot:input="picker" style="min-width: 350px">
      {{ picker.startDate | date }} - {{ picker.endDate | date }}
    </template> -->

    <template #footer="slotProps">
      <hr v-if="!slotProps.autoApply" class="vue-daterange-picker-footer-separator" />
      <div v-if="!slotProps.autoApply" class="vue-daterange-picker-footer">
        <div class="group-daterange-text">
          {{ slotProps.rangeText }}
        </div>
        <div class="group-daterange-buttons">
          <Button
            severity="secondary"
            label="Cancel"
            @click="
              slotProps.clickCancel()
              onClickCancel()
            "
          ></Button>
          <Button
            severity="primary"
            label="Apply"
            @click="
              slotProps.clickApply()
              onClickApply()
            "
          ></Button>
        </div>
      </div>
    </template>

    <!-- <div slot="footer" slot-scope="data" class="slot">
      <div>
        <b class="text-black">Calendar footer</b> {{ data.rangeText }}
      </div>
      <div style="margin-left: auto">
        <a @click="data.clickApply" v-if="!data.in_selection" class="btn btn-primary btn-sm">Choose current</a>
      </div>
    </div> -->
  </DateRangePicker>
</template>

<style lang="scss">
.vue-daterange-picker {
  .daterangepicker .drp-calendar.left {
    padding: 24px 0 24px 24px;
  }

  .daterangepicker .drp-calendar.right {
    padding: 24px;
  }

  .daterangepicker .calendar-table th,
  .daterangepicker .calendar-table td {
    font-size: var(--Typeface-size-md, 14px);
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */

    min-width: 32px;
    width: 32px;
    height: 32px;
  }

  .daterangepicker th.month {
    font-size: var(--Typeface-size-md, 14px);
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 142.857% */
  }

  .daterangepicker td.in-range {
    background-color: #edf3ff;
  }

  .daterangepicker td.active,
  .daterangepicker td.active:hover {
    background-color: #2e6be5;
  }

  .vue-daterange-picker-footer-separator {
    margin: 0 24px;
    border-top: 1px solid #ebebeb;
  }
  .vue-daterange-picker-footer {
    display: flex;
    align-items: center;
    padding: 16px 24px;

    .group-daterange-text {
      flex-grow: 1;
    }

    .group-daterange-buttons {
      display: flex;
      gap: 1rem;
    }
  }
}
</style>
