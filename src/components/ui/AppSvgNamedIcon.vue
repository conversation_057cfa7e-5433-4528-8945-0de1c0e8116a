<template>
  <Suspense>
    <SvgIcon :src="src" v-bind="{...$attrs}" />
  </Suspense>
</template>

<script lang="ts" setup>
  import { type SvgIconProps } from "vite-awesome-svg-loader/vue-integration"
  import { ref, watch } from "vue"

  defineOptions({
    inheritAttrs: false,
  });

  // Use same props as SvgIcon, but replace src with name
  interface Props extends Omit<SvgIconProps, "src"> {
    name?: string,
    area?: string,
  }

  const props = defineProps<Props>()

  // See: https://vitejs.dev/guide/features#glob-import
  // I wasn't able to use dynamic imports for this
  // const icons: any = import.meta.glob(["/src/assets/icons/*.svg", "/src/assets/icons/locales/*.svg"], {
  //   // Put URL here or setup your imports via vite-awesome-svg-loader configuration
  //   as: "preserve-line-width&set-current-color",
  // });

  const src = ref("") // SVG source code

  // Make it reactive

  const onNameChange = async (name: string | undefined) => {
    if(!name) {
      src.value = (await import("../../assets/icons/undefined.svg")).default
      return
    }
    // Fetch SVG source code.
    // This may throw an error because icon might not be found, or user has been disconnected.
    // You may need to handle this case, for example, don't show an image.
    // Also, while loading, you may want to use some kind of animation.
    // const code = (await icons[`/src/assets/import-demo/icons/${name}.svg`]()).default;
    // Note that variables only represent file names one level deep. If file is 'foo/bar', the import would fail.
    // For more advanced usage, you can use the glob import feature.
    // And must read https://github.com/rollup/plugins/tree/master/packages/dynamic-import-vars#limitations
    const area = props.area
    const code = area
    ? (await import(`../../assets/icons/${area}/${name}.svg`)).default 
    : (await import(`../../assets/icons/${name}.svg`)).default

    // Verify that name hasn't changed. If it did, set its source code. Otherwise other onNameChange()
    // call will handle the changes.
    if (name === props.name) {
      src.value = code
    }
  }

  // Fetch new icon whenever name changes
  watch(
    () => props.name,
    () => onNameChange(props.name)
  )

  // Fetch initial icon.
  // If you'll provide a loading fallback, you may replace this with watch(..., { immediate: true })
  await onNameChange(props.name)
</script>
