<!-- <script lang="ts">
export interface AppDataTableProps<TData> extends /* @vue-ignore */ DataTableProps {
  /**
   * An array of objects to display.
   */
  value?: TData[] | undefined | null
  /**
   * Name of the field that uniquely identifies the a record in the data.
   */
  dataKey?: string | ((item: TData) => string) | undefined
  // Bổ sung props có trong DefaultProps nhưng ko có trong mô tả.
  responsiveLayout?: string | undefined
  /**
   * Selected row in single mode or an array of values in multiple mode.
   */
  selection?: TData[] | TData | undefined;
}

export interface AppDataTableProps extends /* @vue-ignore */ DataTableProps {
  // Bổ sung props có trong DefaultProps nhưng ko có trong mô tả.
  responsiveLayout?: string | undefined
}

export interface AppDataTableProps extends /* @vue-ignore */ DataTableProps {
  /**
   * An array of objects to display.
   */
  value?: TData[] | undefined | null;
  /**
   * Name of the field that uniquely identifies the a record in the data.
   */
  dataKey?: string | ((item: TData) => string) | undefined;
  /**
   * Selected row in single mode or an array of values in multiple mode.
   */
  selection?: TData[] | TData | undefined;
}
</script> -->
<script setup lang="ts" generic="TData">
import APPCONFIG from "@/appConfig"
import { IPagedRequest } from "@/types/base"
import primevueUtil from "@/utils/primevueUtil"
import DataNotFoundIcon from "@assets/icons/data-not-found.svg"
import type {
  DataTableFilterEvent,
  DataTablePageEvent,
  DataTableProps,
  DataTableRowClickEvent,
  DataTableRowDoubleClickEvent,
  DataTableSlots,
  DataTableSortEvent,
} from "primevue/datatable"
import DataTable from "primevue/datatable"
import { computed } from "vue"
import { useI18n } from "vue-i18n"
//import { HintedString } from "primevue/ts-helpers";

const { t } = useI18n()

defineOptions({
  inheritAttrs: false,
})

// Dùng cái này thì do dùng withDefaults thì nó lại reset hết các prop ko mô tả về undefined -> hơi bất ổn do mất mặc định
// const props = withDefaults(defineProps<DataTableProps>(), {
//   paginator: true
// })

type AppDataTableProps = DataTableProps & {
  /**
   * An array of objects to display.
   */
  value?: TData[] | undefined | null
  /**
   * Name of the field that uniquely identifies the a record in the data.
   */
  dataKey?: string | ((item: TData) => string) | undefined
  /**
   * Selected row in single mode or an array of values in multiple mode.
   */
  selection?: TData[] | TData | undefined
  // Bổ sung props có trong DefaultProps nhưng ko có trong mô tả.
  responsiveLayout?: string | undefined
}

const props = withDefaults(defineProps<AppDataTableProps>(), {
  ...primevueUtil.getDefaultProps(DataTable),
  // Extend or change defaults value of props here:
  ...{
    totalRecords: 0,
    paginator: true,
    loading: false,
    scrollable: false,
  },
})

// const props = withDefaults(
//   defineProps<{
//     /**
//      * An array of objects to display.
//      */
//     value?: TData[] | undefined | null
//     /**
//      * Name of the field that uniquely identifies the a record in the data.
//      */
//     dataKey?: string | ((item: TData) => string) | undefined
//     /**
//      * Number of total records, defaults to length of value when not defined.
//      * @defaultValue 0
//      */
//     totalRecords?: number | undefined
//     /**
//      * When specified as true, enables the pagination.
//      * @defaultValue true
//      */
//     paginator?: boolean | undefined
//     /**
//      * Displays a loader to indicate data load is in progress.
//      * @defaultValue false
//      */
//     loading?: boolean | undefined
//     /**
//      * When specified, enables horizontal and/or vertical scrolling.
//      * @defaultValue false
//      */
//     scrollable?: boolean | undefined
//     /**
//      * Height of the scroll viewport in fixed pixels or the 'flex' keyword for a dynamic size.
//      */
//     //scrollHeight?: HintedString<"flex"> | undefined
//     scrollHeight?: string | undefined
//     // query
//     //start?: number | undefined
//     //length?: number | undefined
//   }>(),
//   {
//     value: undefined,
//     dataKey: undefined,
//     totalRecords: 0,
//     paginator: true,
//     loading: false,
//     scrollable: false,
//     scrollHeight: undefined,
//   }
// )

//console.log(primevueUtil.getDefaultProps(DataTable))

// Không nên dùng defineModel vì primevue vẫn có thể update vào biến này
// trong khi ở ngoài ko cho phép update -> nên dùng defineEmits và defineProps
//const selectionModel = defineModel<TData | TData[]>("selection")

const [queryModel, queryModelModifiers] = defineModel<IPagedRequest>("query", {
  default: {
    start: 0,
    length: APPCONFIG.DEFAULT_TABLE_ITEM_PER_PAGE,
  } as IPagedRequest,
})

const emit = defineEmits<{
  // <eventName>: <expected arguments>
  queryChange: [value: IPagedRequest] // named tuple syntax
  rowClick: [value: DataTableRowClickEvent]
  rowDbclick: [value: DataTableRowDoubleClickEvent]
  selection: [value: TData | TData[] | undefined]
}>()

const slots = defineSlots<DataTableSlots>()

// const fetchData = (input?: IPagedRequest) => {
//   if (input) {
//     queryModel.value = input
//   }

//   emit("queryChange", queryModel.value)
// }

// defineExpose({
//   fetchData,
// })

const onPage = (event: DataTablePageEvent) => {
  //lazyParams.value = event;
  // lazyParams.value.filters = filters.value;
  //loadLazyData();

  //console.log('onPage', event, queryModel.value)

  // queryModel.value = {
  //   start: event.first,
  //   length: event.rows,
  // }
  queryModel.value.start = event.first
  queryModel.value.length = event.rows

  emit("queryChange", queryModel.value)
}
const onSort = (event: DataTableSortEvent) => {
  //lazyParams.value = event;
  //loadLazyData();

  //console.log('onSort', event, queryModel.value)

  // queryModel.value = {
  //   start: event.first,
  //   length: event.rows,
  // }
  queryModel.value.start = event.first
  queryModel.value.length = event.rows

  emit("queryChange", queryModel.value)
}
const onFilter = (event: DataTableFilterEvent) => {
  // lazyParams.value.filters = filters.value;
  //Reset pagination first
  // lazyParams.value.originalEvent = {first: 0, page: 0}
  // onPage(lazyParams.value);
  // loadLazyData();

  //console.log('onFilter', event, queryModel.value)

  // queryModel.value = {
  //   start: event.first,
  //   length: event.rows,
  // }
  queryModel.value.start = event.first
  queryModel.value.length = event.rows

  emit("queryChange", queryModel.value)
}

interface AppDataTableRowClickEvent<T> {
  /**
   * Browser event.
   */
  originalEvent: Event
  /**
   * Selected row data.
   */
  data: T
  /**
   * Row index.
   */
  index: number
}

const innerCurrentPageReportTemplate = computed(() => t("common.data-table.current-page-report-template"))
const innerPaginatorTemplate = computed(() =>
  !props.totalRecords
    ? "CurrentPageReport RowsPerPageDropdown"
    : "CurrentPageReport RowsPerPageDropdown FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink"
)
// export type {
//   DataTableRowClickEvent,
//   DataTableRowDoubleClickEvent,
//   DataTableRowSelectAllEvent,
//   DataTableRowSelectEvent,
//   DataTableRowUnselectAllEvent,
//   DataTableRowUnselectEvent,
// } from "primevue/datatable"
</script>

<template>
  <DataTable
    v-bind="{ ...$attrs, ...$props }"
    v-model:first="queryModel.start"
    v-model:rows="queryModel.length"
    :selection="props.selection"
    class="app-data-table"
    :paginator="props.paginator"
    paginator-position="top"
    :rows-per-page-options="[20, 50, 100, 200]"
    :current-page-report-template="innerCurrentPageReportTemplate"
    :paginator-template="innerPaginatorTemplate"
    :lazy="true"
    @update:selection="(value) => emit('selection', value)"
    @page="onPage"
    @sort="onSort"
    @filter="onFilter"
    @row-click="(evt) => emit('rowClick', evt as AppDataTableRowClickEvent<TData>)"
    @row-dblclick="(evt) => emit('rowDbclick', evt)"
  >
    <template #empty>
      <slot name="empty">
        <div class="panel-no-data-found">
          <SvgIcon :src="DataNotFoundIcon" />
          <!-- <h1 class="panel-no-data-found-title">No data available in table!</h1> -->
          <h1 class="panel-no-data-found-title">{{ $t("component-app-data-table.dont-have-data") }}</h1>
          <h2 class="panel-no-data-found-subtitle">{{ $t("component-app-data-table.check-filer") }}</h2>
        </div>
      </slot>
    </template>

    <template #loading>
      <slot name="loading">
        <AppProgressSpinner></AppProgressSpinner>
      </slot>
    </template>

    <template v-if="!!slots.header" #header>
      <slot name="header"></slot>
    </template>

    <template v-if="!!slots.footer" #footer>
      <slot name="footer"></slot>
    </template>

    <!-- Pass on all named slots -->
    <!-- <slot v-for="slot in Object.keys($slots)" :name="slot" :slot="slot"></slot> -->

    <!-- Pass on all scoped slots -->
    <!-- <template v-for="slot in Object.keys($scopedSlots)" :slot="slot" slot-scope="scope"><slot :name="slot" v-bind="scope"/></template> -->

    <!-- <template v-for="(_, scopedSlot) of $slots" v-slot:[scopedSlot]="slotProps">
      <slot :name="scopedSlot" v-bind="slotProps"></slot>
    </template> -->

    <!-- <template v-for="scopedSlot of Object.keys($slots)" v-slot:[scopedSlot]="{slotProps}">
      <slot :name="scopedSlot" v-bind="slotProps"></slot>
    </template> -->

    <!-- Đoạn này lỗi  -->
    <!-- <template v-for="(_, name) in $slots" v-slot:[name]="slotData"><slot :name="name" v-bind="slotData" /></template> -->

    <!-- <template v-for="(_, name) in $slots" #[name]="slotData">
      <slot :name="name" v-bind="slotData ?? {}"></slot>
    </template> -->

    <slot></slot>
  </DataTable>
</template>

<style lang="scss">
.app-data-table {
  &.p-datatable .p-datatable-tbody > tr.p-datatable-emptymessage > td {
    padding: 32px 0;
  }

  .panel-no-data-found {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .panel-no-data-found-title {
      color: var(--text-headings, #1c1c1c);

      font-size: var(--Typeface-size-xl, 20px);
      font-style: normal;
      font-weight: 600;
      line-height: 28px; /* 140% */
    }

    .panel-no-data-found-subtitle {
      color: var(--text-tertiary, #6c6c6c);

      font-size: var(--Typeface-size-md, 14px);
      font-style: normal;
      font-weight: 400;
      line-height: 20px; /* 142.857% */
    }
  }
}
</style>
