<script setup lang="ts">
  import ToastCopyIcon from "@assets/icons/toast-copy.svg"
  import ToastSuccessIcon from "@assets/icons/toast-success.svg"
  import ToastErrorIcon from "@assets/icons/toast-error.svg"
  import ToastWarnIcon from "@assets/icons/toast-warn.svg"
  import UndefinedIcon from "@assets/icons/undefined.svg"

  defineOptions({
    inheritAttrs: false,
  })
</script>

<template>
  <Toast 
    v-bind="{...$attrs}"
    position="bottom-center"
    :pt="{
      icon: ({ props: toastProps}) => ({
        class: [
          {
            'p-toast-message-icon-success': toastProps.message?.severity === 'success',
            'p-toast-message-icon-error': toastProps.message?.severity === 'error',
            'p-toast-message-icon-info': toastProps.message?.severity === 'info',
            'p-toast-message-icon-warn': toastProps.message?.severity === 'warn',
          },
        ],
      }),
    }"
  >
    <template #icon="{ class: className }">
      <SvgIcon v-if="className.includes('p-toast-message-icon-success')" :src="ToastSuccessIcon" :class="className" />
      <SvgIcon v-else-if="className.includes('p-toast-message-icon-error')" :src="ToastErrorIcon" :class="className" />
      <SvgIcon v-else-if="className.includes('p-toast-message-icon-warn')" :src="ToastWarnIcon" :class="className" />
      <SvgIcon v-else :src="UndefinedIcon" :class="className" />

      <!-- <AppSvgNamedIcon :name="`toast-${}`" /> -->
    </template>
  </Toast>

  <Toast
    v-bind="{...$attrs}"
    position="center"
    class="p-toast-group-quickAlert"
    group="groupToastQuickAlert"
    :pt="{
      icon: ({ props: toastProps}) => ({
        class: [
          {
            'p-toast-message-icon-success': toastProps.message?.severity === 'success',
            'p-toast-message-icon-error': toastProps.message?.severity === 'error',
            'p-toast-message-icon-info': toastProps.message?.severity === 'info',
            'p-toast-message-icon-warn': toastProps.message?.severity === 'warn',
            'p-toast-message-icon-copytoclipboard': toastProps.message?.styleClass.includes('p-toast-message-copytoclipboard'),
          },
        ],
      }),
    }"
  >
    <template #icon="{ class: className }">
      <SvgIcon v-if="className.includes('p-toast-message-icon-copytoclipboard')" :src="ToastCopyIcon" :class="className" />
      <SvgIcon v-else :src="UndefinedIcon" :class="className" />

      <!-- <AppSvgNamedIcon :name="`toast-${}`" /> -->
    </template>
  </Toast>
</template>
