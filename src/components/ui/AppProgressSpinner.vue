<script lang="ts" setup>
//import ProgressSpinner, { ProgressSpinnerProps } from "primevue/progressspinner"
//const props = withDefaults(defineProps<ProgressSpinnerProps>(), {})
//defineProps<ProgressSpinnerProps>()

import ProgressSpinner from "../ui/ProgressSpinner.vue"

defineOptions({
  inheritAttrs: false,
})
</script>

<template>
  <ProgressSpinner v-bind="{ ...$attrs }" />
</template>

<!-- <style lang="scss"></style> -->
