<script setup lang="ts">
/**
 * https://www.npmjs.com/package/maska
 * https://www.npmjs.com/package/vue-cleave-component
 *
 * npm i maska
 * https://github.com/beholdr/maska
 * https://beholdr.github.io/maska/v3/#/vue
 *
 * npm i cleave-zen
 * https://github.com/nosir/cleave-zen
 *
 * npm i inputmask
 * https://github.com/RobinHerbots/Inputmask
 * https://robinherbots.github.io/Inputmask/#/demo
 *
 * TODO: hỗ trợ focus
 * TODO: bấm vào prefix|suffix thì đưa con trỏ về nunber
 */
import APPCONFIG from "@/app.config"
import { truncNumber } from "@/utils/numberUtil"
import primevueUtil from "@/utils/primevueUtil"
import type { InputTextProps } from "primevue/inputtext"
import InputText from "primevue/inputtext"
import { ComponentPublicInstance, computed, nextTick, ref, watch } from "vue"
//import { formatNumeral, unformatNumeral, type FormatNumeralOptions } from "cleave-zen"
//import Cleave from 'vue-cleave-component'
import { useFocus } from "@vueuse/core"
import { Mask, MaskaDetail, MaskInputOptions } from "maska"
import { vMaska } from "maska/vue"

const logDebug = (message: any, ...optionalParams: any[]) => {
  const enable = false

  enable && console.log.apply(console, [`InputAmount.${props.name}`, ...[message, ...optionalParams]])
}

defineOptions({
  inheritAttrs: false,
})

export interface InputAmountProps extends /* @vue-ignore */ Omit<InputTextProps, "mode" | "modelValue"> {
  /**
   * Cách thức xử lý hiển thị với giá trị amount.
   * Nếu là float thì: 1234.5 -> 1,234.5
   * Nếu là integer thì: 1234.5 -> 1,234
   * Nếu là currency thì: 1234.5 -> 1,234.50
   * @default "float"
   */
  mode?: "float" | "integer" | "currency"
  minFractionDigits?: number | undefined
  maxFractionDigits?: number | undefined
  min?: number | undefined
  max?: number | undefined
  name?: string | undefined
  suffix?: string | undefined
}

const props = withDefaults(defineProps<InputAmountProps>(), {
  ...primevueUtil.getDefaultProps(InputText),
  // Extend or change defaults value of props here:
  ...{
    mode: "float",
  },
})

const [amountValueModel, amountValueModelModifiers] = defineModel<number | null | undefined>()
const [currencyValueModel, currencyValueModelModifiers] = defineModel<Nullable<string>>("currency")

const emit = defineEmits(["input", "blur", "focus"])

const isEditing = ref<boolean>(false)
const displayValue = ref<Nullable<string>>()
const innerMinFractionDigits = ref<number>()
const innerMaxFractionDigits = ref<number>()
const innerMinAmount = ref<number>()
const innerMaxAmount = ref<number>()

//#region minFractionDigits
const defaultMinFractionDigits = computed(() => {
  switch (props.mode) {
    case "currency":
      return currencyValueModel.value?.toUpperCase() == "VND" ? 0 : 2
    case "integer":
    case "float":
    default:
      return 0
  }
})
watch(
  [() => props.minFractionDigits, defaultMinFractionDigits],
  ([minFractionDigits, defaultMinFractionDigits]) => {
    innerMinFractionDigits.value = minFractionDigits != undefined ? minFractionDigits : defaultMinFractionDigits
  },
  {
    immediate: true,
  }
)
//#endregion

//#region maxFractionDigits
const defaultMaxFractionDigits = computed(() => {
  switch (props.mode) {
    case "currency":
      return currencyValueModel.value?.toUpperCase() == "VND" ? 0 : 2
    case "integer":
      return 0
    case "float":
    default:
      return 2
  }
})
watch(
  [() => props.maxFractionDigits, defaultMaxFractionDigits],
  ([maxFractionDigits, defaultMaxFractionDigits]) => {
    innerMaxFractionDigits.value = maxFractionDigits != undefined ? maxFractionDigits : defaultMaxFractionDigits
  },
  {
    immediate: true,
  }
)
//#endregion

//#region min
const defaultMin = computed(() => {
  return 0
})
watch(
  [() => props.min, defaultMin],
  ([min, defaultMin]) => {
    innerMinAmount.value = min != undefined ? min : defaultMin
  },
  {
    immediate: true,
  }
)
//#endregion

//#region max
const defaultMax = computed(() => {
  switch (props.mode) {
    case "currency":
      return currencyValueModel.value?.toUpperCase() == "VND"
        ? APPCONFIG.MAX_PAYMENTAMOUNT_VND
        : APPCONFIG.MAX_PAYMENTAMOUNT
    case "integer":
      return APPCONFIG.MAX_NUMBER_INPUT_INT
    default:
      return APPCONFIG.MAX_NUMBER_INPUT_DEC
  }
})
watch(
  [() => props.max, defaultMax],
  ([max, defaultMax]) => {
    innerMaxAmount.value = max != undefined ? max : defaultMax
  },
  {
    immediate: true,
  }
)
//#endregion

//#region options
// const cleaveOptions = computed<FormatNumeralOptions>(() => {
//   return {
//     numeralPositiveOnly: true,
//     numeralDecimalScale: innerMaxFractionDigits.value,
//   }
// })
const maskaOptions = computed<MaskInputOptions>(() => {
  return {
    number: {
      unsigned: true, // positive numbers only
      fraction: innerMaxFractionDigits.value,
      locale: "en-US",
    },
    postProcess: (val) => (val && props.suffix ? `${val} ${props.suffix}` : val),
    eager: true,
  }
})
//#endregion options

const correctAmountValue = (value: Nullable<number>): Nullable<number> => {
  if (!value || Number.isNaN(value)) return value

  const minFractionDigits = innerMinFractionDigits.value
  const maxFractionDigits = innerMaxFractionDigits.value
  const min = innerMinAmount.value
  const max = innerMaxAmount.value

  if (maxFractionDigits != undefined) {
    const newValue = truncNumber(value, maxFractionDigits)
    if (value != newValue) {
      logDebug(`Has correct value (maxFractionDigits:${maxFractionDigits})`, value, newValue)
      value = newValue
    }
  }
  if (min && value < min) {
    logDebug("Has correct value (min)", value, min)
    value = min
  }
  if (max && value > max) {
    logDebug("Has correct value (max)", value, max)
    value = max
  }

  return value
}

const updateDisplayValue = (
  input: Nullable<number | string>,
  updateOptions: {
    displayType: "FocusIn" | "FocusOut"
  }
) => {
  // CHÚ Ý:
  // - String(null) == 'null'
  // - String(undefined) == 'undefined'
  // - Number(0) == 0
  // - Number("") == 0
  // - Number("0") == 0
  // - Number(null) == 0
  // - Number(undefined) = NaN
  // - formatNumeral(String(null)) = ''
  // - formatNumeral(String(undefined)) = ''
  // - formatNumeral(String('')) = ''
  // - formatNumeral(String('0')) = '0'
  // - formatNumeral(String('0.00')) = '0.00'

  //let formatedValue: Nullable<string> = formatNumeral(String(input), cleaveOptions.value)
  let formatedValue: Nullable<string> = new Mask(maskaOptions.value).masked(String(input))

  logDebug(`updateDisplayValue(${updateOptions.displayType})`, input, formatedValue)

  if (updateOptions.displayType == "FocusIn") {
    if (Number(formatedValue) == 0) {
      // Xử lý cho các trường hợp formatedValue = "0"|"0.00"|""
      formatedValue = ""
      displayValue.value = formatedValue
      return
    }
  }
  if (updateOptions.displayType == "FocusOut") {
    // if (formatedValue == "0" || formatedValue == "" || formatedValue == null || formatedValue == undefined) {
    //   formatedValue = "0"
    // }

    // Check khi formatedValue có giá trị != '' ("0" vẫn được xử lý) -> Chỉ khi input != null && input != undefined && input != ''
    // thì mới xử lý bổ sung đuôi phần thập phân .00
    if (formatedValue) {
      // Thực hiện bổ sung để hiển thị đầy đủ x,xxx.xx
      const minFractionDigits = innerMinFractionDigits.value
      // const maxFractionDigits = innerMaxFractionDigits.value
      // const min = innerMinAmount.value
      // const max = innerMaxAmount.value

      if (minFractionDigits && minFractionDigits > 0) {
        const decimalFractions = formatedValue.split(".")
        const integerPart = decimalFractions[0] // input -12,342.50999974435 -> return: '-12,342'
        const fractionalPart: string | undefined = decimalFractions[1] // input -12,342.50999974435 -> return: '50999974435'
        const padZeroFractionalPart = String(fractionalPart || "").padEnd(minFractionDigits, "0") // input ('50999974435, 2) -> return '50'; input (', 2) -> return '00'
        formatedValue = `${integerPart}.${padZeroFractionalPart}`
      }
    }
  }

  displayValue.value = formatedValue
}

watch(
  [
    amountValueModel,
    innerMinFractionDigits,
    innerMaxFractionDigits,
    innerMinAmount,
    innerMaxAmount,
    maskaOptions,
  ],
  () => {
    if (isEditing.value == false) {
      // Khi đang input (Thay đổi value-model) thì cần bỏ qua việc cập nhật valueModel vào innerValue
      // -> cho phép tránh xử lý vụ xóa 123.01 ->123.0 sẽ bị xóa mất phần thập phân .0
      // Lý do: valueModel (123) cập nhật giá trị vào innerValue và innerValue cập nhật vào input (123.0)
      updateDisplayValue(amountValueModel.value, {
        displayType: "FocusOut",
      })
      
      // Check giá trị để cập nhật lại
      const amount = amountValueModel.value
      const correctedAmount = correctAmountValue(amount)
      if (correctedAmount != amount) {
        amountValueModel.value = correctedAmount
      }
    }
  },
  {
    immediate: true,
  }
)

//#region Handle events
const refInputText = ref<ComponentPublicInstance<InstanceType<typeof InputText>> | null>(null)

const { focused } = useFocus(refInputText)

export type InputAmountEvent = {
  name: string
  displayValue: string
  value: Nullable<string | number>
}

function onInput(event: Event) {
  logDebug("onInput", displayValue.value, amountValueModel.value)

  // Raise events
  emit("input", {
    name: props.name,
    displayValue: displayValue.value,
    value: amountValueModel.value,
  } as InputAmountEvent)
}

function onFocus(event: Event) {
  logDebug("onFocus", displayValue.value)

  // Cập nhật mới lại toàn bộ trước khi focusIn sau đó mới chuyển trạng thái isEditing=true (ngăn update model-value)
  const amount = correctAmountValue(amountValueModel.value)
  updateDisplayValue(amount, {
    displayType: "FocusIn", // simple input format
  })
  isEditing.value = true

  // Raise events
  emit("focus", {
    name: props.name,
    displayValue: displayValue.value,
    value: amountValueModel.value,
  } as InputAmountEvent)
}

async function onBlur(event: Event) {
  logDebug("onBlur", displayValue.value)

  // Chuyển trạng thái isEditing=false (cho phép update model-value) trước để cập nhật mới lại toàn bộ sau khi focusOut
  isEditing.value = false
  const amount = correctAmountValue(amountValueModel.value)
  updateDisplayValue(amount, {
    displayType: "FocusOut", // simple input format
  })

  // Raise events
  emit("blur", {
    name: props.name,
    displayValue: displayValue.value,
    value: amountValueModel.value,
  } as InputAmountEvent)
}
//#endregion

const onMaska = (event: CustomEvent<MaskaDetail>) => {
  logDebug({
    masked: event.detail.masked,
    unmasked: event.detail.unmasked,
    completed: event.detail.completed,
  })

  const amountValue = event.detail.unmasked ? Number(event.detail.unmasked) : undefined
  const correctedAmountValue = correctAmountValue(amountValue)
  amountValueModel.value = correctedAmountValue

  // Trường hợp có cập nhật correct thì cập nhật ngược lại input
  if (amountValue != correctedAmountValue) {
    nextTick(() => (displayValue.value = String(correctedAmountValue)))
  }
}

defineExpose({
  amountValueModel, // make sure you expose the bound variable
  focus: (state: boolean) => focused.value = state
})
</script>

<template>
  <!-- <Cleave
    ref="refInputText"
    v-bind="{ ...$attrs }"
    v-model="innerValue"
    :options="options"
    :disabled="props.disabled"
    class="p-inputtext p-component"
    :class="{
      'p-invalid': props.invalid,
    }"
    @focus="onFocus"
    @blur="onBlur"
  ></Cleave> -->
  <!-- <InputText
    ref="refInputText"
    v-bind="{ ...$attrs }"
    v-model.lazy="innerValue"
    @input="onInput"
    @focus="onFocus"
    @blur="onBlur"
  ></InputText> -->
  <InputText
    ref="refInputText"
    v-bind="{ ...$attrs, ...$props }"
    v-model.lazy="displayValue"
    v-maska="maskaOptions"
    @maska="onMaska"
    @input="onInput"
    @focus="onFocus"
    @blur="onBlur"
  ></InputText>
</template>

<style lang="scss"></style>
