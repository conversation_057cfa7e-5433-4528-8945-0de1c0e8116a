<script setup lang="ts">
import { computed, InputHTMLAttributes, nextTick, ref } from "vue"
import type { InputNumberProps, InputNumberInputEvent, InputNumberBlurEvent } from "primevue/inputnumber"
import InputNumber from "primevue/inputnumber/InputNumber.vue"
import APPCONFIG from "@/appConfig";

defineOptions({
  inheritAttrs: false,
})

const props = withDefaults(
  defineProps<{
    isInteger?: boolean
    isPercent?: boolean
    /**
     * When present, it specifies that the component should have invalid state style.
     * @defaultValue false
     */
    invalid?: boolean
    disabled?: boolean
    readonly?: boolean
    prefix?: string
    suffix?: string
    allowEmpty?: boolean
    inputId?: string
    inputClass?: string | object
    inputStyle?: object
    inputProps?: InputHTMLAttributes
    placeholder?: string
    minFractionDigits?: number
    maxFractionDigits?: number
    max?: number
  }>(),
  {
    isInteger: true,
    invalid: false,
    allowEmpty: true,
    isPercent: false
  }
)

const [amountValueModel, amountValueModelModifiers] = defineModel<Nullable<number>>()

const fractionDigits = computed(() => {
  return props.isInteger ? 0 : 2
})
const minFractionDigits = ref(props.isInteger ? 0 : props.minFractionDigits ?? fractionDigits.value)
const maxFractionDigits = ref(props.isInteger ? 0 : props.maxFractionDigits ?? fractionDigits.value)

const processCustomMaxAmount = computed(() => !props.max)

/** Hàm chuẩn để trả về số đầu vào với số thập phân mong muốn mà ko bị làm tròn giá trị */
// function toFixed(num: number, fixed: number): number {
//   // https://stackoverflow.com/questions/4187146/truncate-number-to-two-decimal-places-without-rounding
//   const toFixedRegex = new RegExp("^-?\\d+(?:\\.\\d{0," + (fixed || -1) + "})?") // Ex: /^-?\d+(?:\.\d{0,0})?/
//   return Number(num.toString().match(toFixedRegex)?.firstOrDefault())
// }

//const maxAmount = computed(() => !processMaxAmount ? undefined : Number(formatCurrency(appSettings.maxPaymentAmount, currencyValueModel.value?.toString().toUpperCase())))
//const maxAmount = computed(() => !processMaxAmount ? undefined : numberUtil.toFixed(appSettings.maxPaymentAmount, fractionDigits.value))
const maxAmount = computed(() =>
  !processCustomMaxAmount.value
    ? props.max
    : props.isInteger
      ? APPCONFIG.MAX_NUMBER_INPUT_INT
      : APPCONFIG.MAX_NUMBER_INPUT_DEC
)

const refInputNumber = ref<InstanceType<typeof InputNumber> | null>(null)

function onInput($event: InputNumberInputEvent) {
  try {
    //https://stackoverflow.com/questions/14667713/how-to-convert-a-string-to-number-in-typescript
    let value = Number($event.value)
    if (props.isInteger)  {
      value = Math.trunc(value)
    }
    if (processCustomMaxAmount.value) {
      if (value < 0) {
        amountValueModel.value = 0
      } else if (value > (maxAmount.value ?? 0)) {
        amountValueModel.value = maxAmount.value
      } else {
        amountValueModel.value = value
      }
    } else if (props.max && maxAmount.value) {
      if (value > maxAmount.value) {
        amountValueModel.value = maxAmount.value
      } else {
        amountValueModel.value = value
      }
    } else {
      amountValueModel.value = value
    }

    //https://github.com/primefaces/primevue/issues/506#issuecomment-2085265777
    //const target = $event.originalEvent.target as HTMLElement;
    //target.blur();
    //target.focus();
  } catch (ex) {
    console.error(ex)
  }
}

const onFocus = () => {
  minFractionDigits.value = props.isPercent ? 2 : fractionDigits.value;
  maxFractionDigits.value = props.isPercent ? 2 : fractionDigits.value;
}

const onBlur = (event: InputNumberBlurEvent) => {
  if (Number.isInteger(Number(event.value.replace(/,/g, ''))) && props.isPercent) {
    minFractionDigits.value = 0;
    maxFractionDigits.value = 0;
  } else {
    minFractionDigits.value = fractionDigits.value;
    maxFractionDigits.value = fractionDigits.value;
  }
}
</script>

<template>
  <InputNumber
    ref="refInputNumber"
    :model-value="amountValueModel"
    locale="en-US"
    :min-fraction-digits="minFractionDigits"
    :max-fraction-digits="maxFractionDigits"
    :min="0"
    :max="maxAmount"
    :invalid="invalid"
    :suffix="suffix"
    v-bind="{ ...$attrs }"
    @input="onInput"
    @focus="onFocus"
    @blur="onBlur($event)"
  />
</template>

<style lang="scss">
</style>
