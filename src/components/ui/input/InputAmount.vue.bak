<script setup lang="ts">
import APPCONFIG from "@/app.config"
import { toFixed } from "@/utils/numberUtil"
import primevueUtil from "@/utils/primevueUtil"
import type { InputTextProps } from "primevue/inputtext"
import InputText from "primevue/inputtext"
import type { CleaveOptions } from "cleave.js/options"
import { computed, nextTick, ref, watch } from "vue"
import type Cleave from "cleave.js"

defineOptions({
  inheritAttrs: false,
})

export interface InputAmountProps extends /* @vue-ignore */ Omit<InputTextProps, "mode" | "modelValue"> {
  /**
   * <PERSON><PERSON>ch thức xử lý với giá trị amount
   * @default "float"
   */
  mode?: "float" | "integer" | "currency"
  minFractionDigits?: number | undefined
  maxFractionDigits?: number | undefined
  min?: number | undefined
  max?: number | undefined
}

const props = withDefaults(defineProps<InputAmountProps>(), {
  ...primevueUtil.getDefaultProps(InputText),
  // Extend or change defaults value of props here:
  ...{
    mode: "float",
  },
})

const [amountValueModel, amountValueModelModifiers] = defineModel<number | null | undefined>()
const [currencyValueModel, currencyValueModelModifiers] = defineModel<Nullable<string>>("currency")
const displayValueModel = defineModel<string>("displayValue")

const emit = defineEmits(["input", "blur", "focus"])

const isEditing = ref<boolean>(false)

//#region modelValue
const innerValue = ref<Nullable<string | number>>()
//const innerValue = ref<Nullable<number>>()
//const innerValue = ref<number | string>(0)
// Đồng bộ (2 chiều) 2 giá trị với nhau. (Cách viết ngắn của 2 thằng watch)
// syncRef(amountValueModel, innerValue, {
//   immediate: true
// })
watch(
  amountValueModel,
  (amountValueModel) => {
    if (isEditing.value == false) {
      // Khi đang input thì cần bỏ qua việc cập nhật valueModel vào innerValue
      // -> cho phép tránh xử lý vụ xóa 123.01 ->123.0 sẽ bị xóa mất phần thập phân .0
      // Lý do: valueModel (123) cập nhật giá trị vào innerValue và innerValue cập nhật vào input (123.0)
      innerValue.value = String(amountValueModel)
    }
  },
  {
    immediate: true,
  }
)
// watch(
//   innerValue,
//   (innerValue) => {
//     amountValueModel.value = Number(innerValue)
//   },
//   {
//     immediate: true,
//   }
// )
//#endregion

//#region minFractionDigits
const innerMinFractionDigits = ref<number>()
const defaultMinFractionDigits = () => {
  switch (props.mode) {
    case "currency":
      return currencyValueModel.value?.toUpperCase() == "VND" ? 0 : 2
    default:
      return 0
  }
}
watch(
  [() => props.minFractionDigits, currencyValueModel],
  ([minFractionDigits]) => {
    innerMinFractionDigits.value = minFractionDigits != undefined ? minFractionDigits : defaultMinFractionDigits()
  },
  {
    immediate: true,
  }
)
//#endregion

//#region maxFractionDigits
const innerMaxFractionDigits = ref<number>()
const defaultMaxFractionDigits = () => {
  switch (props.mode) {
    case "currency":
      return currencyValueModel.value?.toUpperCase() == "VND" ? 0 : 2
    case "integer":
      return 0
    default:
      return 2
  }
}
watch(
  [() => props.maxFractionDigits, currencyValueModel],
  ([maxFractionDigits]) => {
    innerMaxFractionDigits.value = maxFractionDigits != undefined ? maxFractionDigits : defaultMaxFractionDigits()
  },
  {
    immediate: true,
  }
)
//#endregion

// Cho phép nhập tự do phần thập phân khi đang edit
const innerMinFractionDigitsWhenEdit = computed(() => {
  if (props.mode == "currency") {
    return isEditing.value ? 0 : innerMinFractionDigits.value
  }

  return innerMaxFractionDigits.value
})

//#region min
const innerMinAmount = ref<number>()
const defaultMin = () => {
  return 0
}
watch(
  [() => props.min],
  ([min]) => {
    innerMinAmount.value = min != undefined ? min : defaultMin()
  },
  {
    immediate: true,
  }
)
//#endregion

//#region max
const innerMaxAmount = ref<number>()
const defaultMax = () => {
  switch (props.mode) {
    case "currency":
      return currencyValueModel.value?.toUpperCase() == "VND"
        ? APPCONFIG.MAX_PAYMENTAMOUNT_VND
        : APPCONFIG.MAX_PAYMENTAMOUNT
    case "integer":
      return APPCONFIG.MAX_NUMBER_INPUT_INT
    default:
      return APPCONFIG.MAX_NUMBER_INPUT_DEC
  }
}
watch(
  [() => props.max, () => props.mode, currencyValueModel],
  ([max, numberType, currencyValueModel]) => {
    innerMaxAmount.value = max != undefined ? max : defaultMax()
  },
  {
    immediate: true,
  }
)
//#endregion

const correctValue = (value: Nullable<number>): Nullable<number> => {
  if (!value || Number.isNaN(value)) return value

  const minFractionDigits = innerMinFractionDigits.value
  const maxFractionDigits = innerMaxFractionDigits.value
  const min = innerMinAmount.value
  const max = innerMaxAmount.value

  if (maxFractionDigits != undefined) {
    value = toFixed(value, maxFractionDigits)
  }
  if (min && value < min) {
    value = min
  }
  if (max && value > max) {
    value = max
  }

  return value
}

watch(
  [innerMinFractionDigits, innerMaxFractionDigits, innerMinAmount, innerMaxAmount],
  ([innerMinFractionDigits, innerMaxFractionDigits, innerMinAmount, innerMaxAmount]) => {
      const curVal = Number(innerValue.value)
      const newVal = correctValue(curVal)
      if (curVal != newVal) {
        innerValue.value = newVal
      }

      // update display

      // console.log(refInputCleave.value)

      // if (refInputCleave.value) {
      //   refInputCleave.value.setRawValue("0.00")
      // }
      
       if (cleaveRawValue.value) {

        //cleaveRawValue.value.setr
      
      //   console.log(cleaveRawValue.value)
      //   cleaveRawValue.value.value = "xx"

      //   $(cleaveRawValue.value).val(".00")
       }
  }
)

//#region Handle events
//const refInputText = ref<InstanceType<typeof InputText> | null>(null)
const refInputText = ref(null)
const refInputCleave = computed(() => (refInputText.value as any)?.cleave as Cleave)

const cleaveRawValue = computed<HTMLInputElement | undefined>(() => (refInputText.value as any)?.cleave?.element)

// function onInput($event: InputTextInputEvent) {
//   // Cập nhật ngược trực tiếp giá trị input vào valueModel
//   // (NOTE: ko cập nhật vào innerValue để ngăn xử lý xóa mất .0 như mô tả ở trên)
//   const value = Number($event.value)
//   const valueCorrected = correctValue(value)
//   // Chỉ chặn xử lý tiếp khi mà không thỏa mãn giá trị
//   if (valueCorrected != value) {
//     innerValue.value = valueCorrected
//   }

//   // update giá trị chính xác vào valueModel
//   amountValueModel.value = valueCorrected

//   // Raise events
//   emit("input", $event)
// }

export type InputAmountEvent = {
  name: string,
  displayValue: string,
  value: Nullable<string | number>
}

function onFocus($event: Event) {
  //console.log("onFocus", innerValue.value)
  isEditing.value = true

  // Nếu giá trị là 0 thì -> Hiển thị input là ""
  if (innerValue.value == 0) {
    innerValue.value = ""
  }

  // Raise events
  emit("focus", {
    name: props.name,
    displayValue: displayValueModel.value,
    value: amountValueModel.value
  } as InputAmountEvent)
}

async function onBlur($event: Event) {
  //console.log("onBlur", innerValue.value)
  isEditing.value = false

  // Nếu giá trị là "" thì -> Hiển thị input là 0
  if (innerValue.value == "" || innerValue.value == null || innerValue.value == undefined) {
    innerValue.value = 0
  }

  // Raise events
  emit("blur", {
    name: props.name,
    displayValue: displayValueModel.value,
    value: amountValueModel.value
  } as InputAmountEvent)
}
//#endregion

/**
 * https://github.com/nosir/cleave.js/blob/master/doc/options.md
 */
const options = computed<CleaveOptions>(() => {
  return {
    numeral: true,
    numeralPositiveOnly: true,
    numeralDecimalScale: innerMaxFractionDigits.value,
    blocks: [3, 2],
    noImmediatePrefix: true,
    rawValueTrimPrefix: true,
    onValueChanged: function (e) {
      console.log('onValueChanged', e)
      // e.target = { value: '5100-1234', rawValue: '51001234' }
      displayValueModel.value = e.value

      

      if (isEditing.value) {
        const curVal = Number(innerValue.value)
        const newVal = correctValue(curVal)
        if (curVal != newVal) {
          innerValue.value = newVal
        }
        amountValueModel.value = Number(innerValue.value)
      } else {
if (refInputCleave.value) {
        refInputCleave.value.properties.delimiterLazyShow setRawValue("0.00")
      }
      }
    },
  }
})
</script>

<template>
  <cleave
    ref="refInputText"
    v-bind="{ ...$attrs }"
    v-model="innerValue"
    :options="options"
    :disabled="props.disabled"
    class="p-inputtext p-component"
    :class="{
      'p-invalid': props.invalid,
    }"
    @focus="onFocus"
    @blur="onBlur"
  ></cleave>
  <!-- <InputText
    ref="refInputText"
    v-cleave="options"
  ></InputText> -->
</template>

<style lang="scss"></style>
