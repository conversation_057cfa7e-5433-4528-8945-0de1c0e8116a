<script setup lang="ts">
import APPCONFIG from "@/app.config"
import { toFixed } from "@/utils/numberUtil"
import primevueUtil from "@/utils/primevueUtil"
import type { InputTextProps } from "primevue/inputtext"
import InputText from "primevue/inputtext"
import { computed, nextTick, ref, watch } from "vue"
import { formatNumeral, unformatNumeral, type FormatNumeralOptions } from "cleave-zen"

defineOptions({
  inheritAttrs: false,
})

export interface InputAmountProps extends /* @vue-ignore */ Omit<InputTextProps, "mode" | "modelValue"> {
  /**
   * <PERSON><PERSON><PERSON> thức xử lý hiển thị với giá trị amount.
   * Nếu là float thì: 1234.5 -> 1,234.5
   * Nếu là integer thì: 1234.5 -> 1,234
   * Nếu là currency thì: 1234.5 -> 1,234.50
   * @default "float"
   */
  mode?: "float" | "integer" | "currency"
  minFractionDigits?: number | undefined
  maxFractionDigits?: number | undefined
  min?: number | undefined
  max?: number | undefined
}

const props = withDefaults(defineProps<InputAmountProps>(), {
  ...primevueUtil.getDefaultProps(InputText),
  // Extend or change defaults value of props here:
  ...{
    mode: "float",
  },
})

const [amountValueModel, amountValueModelModifiers] = defineModel<number | null | undefined>()
const [currencyValueModel, currencyValueModelModifiers] = defineModel<Nullable<string>>("currency")

const emit = defineEmits(["input", "blur", "focus"])

const isEditing = ref<boolean>(false)
const innerValue = ref<Nullable<string>>()
const innerMinFractionDigits = ref<number>()
const innerMaxFractionDigits = ref<number>()
const innerMinAmount = ref<number>()
const innerMaxAmount = ref<number>()
const debugComponent = true

//#region minFractionDigits
const defaultMinFractionDigits = computed(() => {
  switch (props.mode) {
    case "currency":
      return currencyValueModel.value?.toUpperCase() == "VND" ? 0 : 2
    case "integer":
    case "float":
    default:
      return 0
  }
})
watch(
  [() => props.minFractionDigits, defaultMinFractionDigits],
  ([minFractionDigits, defaultMinFractionDigits]) => {
    innerMinFractionDigits.value = minFractionDigits != undefined ? minFractionDigits : defaultMinFractionDigits
  },
  {
    immediate: true,
  }
)
//#endregion

//#region maxFractionDigits
const defaultMaxFractionDigits = computed(() => {
  switch (props.mode) {
    case "currency":
      return currencyValueModel.value?.toUpperCase() == "VND" ? 0 : 2
    case "integer":
      return 0
    case "float":
    default:
      return 2
  }
})
watch(
  [() => props.maxFractionDigits, defaultMaxFractionDigits],
  ([maxFractionDigits, defaultMaxFractionDigits]) => {
    innerMaxFractionDigits.value = maxFractionDigits != undefined ? maxFractionDigits : defaultMaxFractionDigits
  },
  {
    immediate: true,
  }
)
//#endregion

//#region min
const defaultMin = computed(() => {
  return 0
})
watch(
  [() => props.min, defaultMin],
  ([min, defaultMin]) => {
    innerMinAmount.value = min != undefined ? min : defaultMin
  },
  {
    immediate: true,
  }
)
//#endregion

//#region max
const defaultMax = computed(() => {
  switch (props.mode) {
    case "currency":
      return currencyValueModel.value?.toUpperCase() == "VND"
        ? APPCONFIG.MAX_PAYMENTAMOUNT_VND
        : APPCONFIG.MAX_PAYMENTAMOUNT
    case "integer":
      return APPCONFIG.MAX_NUMBER_INPUT_INT
    default:
      return APPCONFIG.MAX_NUMBER_INPUT_DEC
  }
})
watch(
  [() => props.max, defaultMax],
  ([max, defaultMax]) => {
    innerMaxAmount.value = max != undefined ? max : defaultMax
  },
  {
    immediate: true,
  }
)
//#endregion

//#region Cleave.js options
const options = computed<FormatNumeralOptions>(() => {
  //https://github.com/nosir/cleave.js/blob/master/doc/options.md
  return {
    numeral: true,
    numeralPositiveOnly: true,
    numeralDecimalScale: innerMaxFractionDigits.value,
    noImmediatePrefix: true,
    rawValueTrimPrefix: true,
  }
})
//#endregion

const correctAmountValue = (value: Nullable<number>): Nullable<number> => {
  if (!value || Number.isNaN(value)) return value

  const minFractionDigits = innerMinFractionDigits.value
  const maxFractionDigits = innerMaxFractionDigits.value
  const min = innerMinAmount.value
  const max = innerMaxAmount.value

  if (maxFractionDigits != undefined) {
    const newValue = toFixed(value, maxFractionDigits)
    if (value != newValue) {
      debugComponent && console.log(`Has correct value (maxFractionDigits:${maxFractionDigits})`, value, newValue)
      value = newValue
    }
  }
  if (min && value < min) {
    debugComponent && console.log("Has correct value (min)", value, min)
    value = min
  }
  if (max && value > max) {
    debugComponent && console.log("Has correct value (max)", value, max)
    value = max
  }

  return value
}

const updateInnerValue = (
  input: Nullable<number | string>,
  updateOptions: {
    displayType: "FocusIn" | "FocusOut"
  }
) => {
  let formatedValue: Nullable<string> = formatNumeral(String(input), options.value)

  debugComponent && console.log("updateInnerValue", input, updateOptions, formatedValue)

  if (updateOptions.displayType == "FocusIn") {
    if (formatedValue == "0") {
      formatedValue = ""
    }
  }
  if (updateOptions.displayType == "FocusOut") {
    if (formatedValue == "" || formatedValue == null || formatedValue == undefined) {
      formatedValue = "0"
    }

    // Thực hiện bổ sung để hiển thị đầy đủ x,xxx.xx
    const minFractionDigits = innerMinFractionDigits.value
    // const maxFractionDigits = innerMaxFractionDigits.value
    // const min = innerMinAmount.value
    // const max = innerMaxAmount.value

    if (minFractionDigits && minFractionDigits > 0) {
      const decimalFractions = formatedValue.split(".")
      const integerPart = decimalFractions[0] // input -12,342.50999974435 -> return: '-12,342'
      const fractionalPart: string | undefined = decimalFractions[1] // input -12,342.50999974435 -> return: '50999974435'
      const padZeroFractionalPart = String(fractionalPart || "").padEnd(minFractionDigits, "0") // input '50999974435, 2 -> return '50'
      formatedValue = `${integerPart}.${padZeroFractionalPart}`
    }
  }

  innerValue.value = formatedValue
}

//#region modelValue
watch(
  [innerValue, options],
  ([innerValue, options], [oldInnerValue]) => {
    debugComponent && console.log('watch update amountValueModel')

    const value = Number(unformatNumeral(String(innerValue), options))
    const correctValue = correctAmountValue(value)
    amountValueModel.value = correctValue

    // trường hợp có thay đổi correct giá trị thì phải update ngược lại innerValue
    if (correctValue != value) {
      debugComponent && console.log("Has correct value", correctValue, value)
      nextTick(() => {
        updateInnerValue(correctValue, {
          displayType: "FocusIn", // simple input format
        })
      })
      return
    }
    // Các trường hợp không thỏa mãn khác
    if (innerValue && innerMaxFractionDigits.value == 0 && innerValue.split(".").length > 1) {
      debugComponent && console.log("Has revert value (not allow '.')", innerValue)
      nextTick(() => {
        updateInnerValue(oldInnerValue, {
          displayType: "FocusIn", // simple input format
        })
      })
      return
    }
    if (innerValue && innerValue.split(".").length > 2) {
      debugComponent && console.log("Has revert value (enter '..')", innerValue)
      nextTick(() => {
        updateInnerValue(oldInnerValue, {
          displayType: "FocusIn", // simple input format
        })
      })
      return
    }
  },
  {
    immediate: true,
  }
)
//#endregion

//#region innerValue
watch(
  [amountValueModel, innerMinFractionDigits, currencyValueModel, options],
  ([amountValueModel, innerMinFractionDigits, currencyValueModel, options]) => {
    debugComponent && console.log('watch update innerValue')

    if (isEditing.value == false) {
      // Khi đang input thì cần bỏ qua việc cập nhật valueModel vào innerValue
      // -> cho phép tránh xử lý vụ xóa 123.01 ->123.0 sẽ bị xóa mất phần thập phân .0
      // Lý do: valueModel (123) cập nhật giá trị vào innerValue và innerValue cập nhật vào input (123.0)
      updateInnerValue(amountValueModel, {
        displayType: "FocusOut", // full display format
      })
    }
  },
  {
    immediate: true,
  }
)
//#endregion

//#region Handle events
const refInputText = ref<InstanceType<typeof InputText> | null>(null)
//const refInputText = ref<HTMLInputElement | null>(null)

export type InputAmountEvent = {
  name: string
  displayValue: string
  value: Nullable<string | number>
}

function onInput($event: Event) {
  debugComponent && console.log("onInput", innerValue.value)

  // Raise events
  emit("input", {
    name: props.name,
    displayValue: innerValue.value,
    value: amountValueModel.value,
  } as InputAmountEvent)
}

function onFocus($event: Event) {
  debugComponent && console.log("onFocus", innerValue.value)

  // Cập nhật mới lại toàn bộ trước khi focusIn sau đó mới chuyển trạng thái isEditing=true (ngăn update model-value)
  const amount = correctAmountValue(amountValueModel.value)
  updateInnerValue(amount, {
    displayType: "FocusIn", // simple input format
  })
  isEditing.value = true

  // Raise events
  emit("focus", {
    name: props.name,
    displayValue: innerValue.value,
    value: amountValueModel.value,
  } as InputAmountEvent)
}

async function onBlur($event: Event) {
  debugComponent && console.log("onBlur", innerValue.value)

  // Chuyển trạng thái isEditing=false (cho phép update model-value) trước để cập nhật mới lại toàn bộ sau khi focusOut
  isEditing.value = false
  const amount = correctAmountValue(amountValueModel.value)
  updateInnerValue(amount, {
    displayType: "FocusOut", // simple input format
  })

  // Raise events
  emit("blur", {
    name: props.name,
    displayValue: innerValue.value,
    value: amountValueModel.value,
  } as InputAmountEvent)
}
//#endregion
</script>

<template>
  <!-- <cleave
    ref="refInputText"
    v-bind="{ ...$attrs }"
    v-model="innerValue"
    :options="options"
    :disabled="props.disabled"
    class="p-inputtext p-component"
    :class="{
      'p-invalid': props.invalid,
    }"
    @focus="onFocus"
    @blur="onBlur"
  ></cleave> -->
  <InputText
    ref="refInputText"
    v-bind="{ ...$attrs }"
    :model-value="innerValue"
    @update:model-value="(value) => updateInnerValue(value, { displayType: 'FocusIn' })"
    @input="onInput"
    @focus="onFocus"
    @blur="onBlur"
  ></InputText>
</template>

<style lang="scss"></style>
