<script setup lang="ts">
import { computed, InputHTMLAttributes, nextTick, ref, watch } from "vue"
import type { InputNumberProps, InputNumberInputEvent, InputNumberBlurEvent } from "primevue/inputnumber"
import InputNumber from "primevue/inputnumber"
import APPCONFIG from "@/app.config"
import { toFixed } from "@/utils/numberUtil"
import primevueUtil from "@/utils/primevueUtil"
import { MaybeComputedElementRef, MaybeElement, unrefElement } from "@vueuse/core"

defineOptions({
  inheritAttrs: false,
})

export interface InputAmountProps extends /* @vue-ignore */ Omit<InputNumberProps, "mode"> {
  /**
   * <PERSON><PERSON>ch thức xử lý với giá trị amount
   * @default "float"
   */
  mode?: "float" | "integer" | "currency"
}

const props = withDefaults(defineProps<InputAmountProps>(), {
  ...primevueUtil.getDefaultProps(InputNumber),
  // Extend or change defaults value of props here:
  ...{
     mode: "float",
  },
})

// const props = withDefaults(
//   defineProps<{
//     /**
//      * Cách thức xử lý với giá trị amount
//      * @default "float"
//      */
//     mode?: "float" | "integer" | "currency"

//     invalid?: boolean | undefined
//     disabled?: boolean | undefined
//     readonly?: boolean | undefined
//     allowEmpty?: boolean | undefined

//     inputId?: string | undefined
//     inputClass?: string | object | undefined
//     inputStyle?: object | undefined
//     inputProps?: InputHTMLAttributes | undefined
//     placeholder?: string | undefined

//     minFractionDigits?: number | undefined
//     maxFractionDigits?: number | undefined
//     max?: number | undefined
//     min?: number | undefined
//   }>(),
//   {
//     mode: "float",
//     allowEmpty: true
//   }
// )

const [amountValueModel, amountValueModelModifiers] = defineModel<Nullable<number>>()
const [currencyValueModel, currencyValueModelModifiers] = defineModel<Nullable<string>>("currency")

const emit = defineEmits(["input", "blur", "focus"])

const isEditing = ref<boolean>(false)

//#region modelValue
const innerValue = ref<Nullable<number>>()
// Đồng bộ (2 chiều) 2 giá trị với nhau. (Cách viết ngắn của 2 thằng watch)
// syncRef(amountValueModel, innerValue, {
//   immediate: true
// })
watch(amountValueModel, (amountValueModel) => {
  if (isEditing.value == false) {
    // Khi đang input thì cần bỏ qua việc cập nhật valueModel vào innerValue
    // -> cho phép tránh xử lý vụ xóa 123.01 ->123.0 sẽ bị xóa mất phần thập phân .0
    // Lý do: valueModel (123) cập nhật giá trị vào innerValue và innerValue cập nhật vào input (123.0)
    innerValue.value = amountValueModel
  }
}, {
  immediate: true
})
watch(innerValue, (innerValue) => {
  // v-model:model-value="innerValue"
  // Đoạn này chỉ cập nhật giá trị mới vào innerValue khi:
  // 1. Bấm enter/home/<USER>
  // 2. on Blur
  if (isEditing.value == false) {
    amountValueModel.value = innerValue
  }
}, {
  immediate: true
})

//#endregion

//#region minFractionDigits
const innerMinFractionDigits = ref<number>()
const defaultMinFractionDigits = () => {
  switch (props.mode) {
    case "currency":
      return currencyValueModel.value?.toUpperCase() == "VND" ? 0 : 2
    default:
      return 0
  }
}
watch(
  [() => props.minFractionDigits, currencyValueModel],
  ([minFractionDigits]) => {
    innerMinFractionDigits.value = minFractionDigits != undefined ? minFractionDigits : defaultMinFractionDigits()
  },
  {
    immediate: true,
  }
)
//#endregion

//#region maxFractionDigits
const innerMaxFractionDigits = ref<number>()
const defaultMaxFractionDigits = () => {
  switch (props.mode) {
    case "currency":
      return currencyValueModel.value?.toUpperCase() == "VND" ? 0 : 2
    case "integer":
      return 0
    default:
      return 2
  }
}
watch(
  [() => props.maxFractionDigits, currencyValueModel],
  ([maxFractionDigits]) => {
    innerMaxFractionDigits.value = maxFractionDigits != undefined ? maxFractionDigits : defaultMaxFractionDigits()
  },
  {
    immediate: true,
  }
)
//#endregion

// Cho phép nhập tự do phần thập phân khi đang edit
const innerMinFractionDigitsWhenEdit = computed(() => {
  if (props.mode == "currency") {
    return isEditing.value ? 0 : innerMinFractionDigits.value
  }

  return innerMaxFractionDigits.value
})

//#region min
const innerMinAmount = ref<number>()
const defaultMin = () => {
  return 0
}
watch(
  [() => props.min],
  ([min]) => {
    innerMinAmount.value = min != undefined ? min : defaultMin()
  },
  {
    immediate: true,
  }
)
//#endregion

//#region max
const innerMaxAmount = ref<number>()
const defaultMax = () => {
  switch (props.mode) {
    case "currency":
      return currencyValueModel.value?.toUpperCase() == "VND"
        ? APPCONFIG.MAX_PAYMENTAMOUNT_VND
        : APPCONFIG.MAX_PAYMENTAMOUNT
    case "integer":
      return APPCONFIG.MAX_NUMBER_INPUT_INT
    default:
      return APPCONFIG.MAX_NUMBER_INPUT_DEC
  }
}
watch(
  [() => props.max, () => props.mode, currencyValueModel],
  ([max, numberType, currencyValueModel]) => {
    innerMaxAmount.value = max != undefined ? max : defaultMax()
  },
  {
    immediate: true,
  }
)
//#endregion

const correctValue = (value: Nullable<number>): Nullable<number> => {
  if (!value || Number.isNaN(value)) return value

  const minFractionDigits = innerMinFractionDigits.value
  const maxFractionDigits = innerMaxFractionDigits.value
  const min = innerMinAmount.value
  const max = innerMaxAmount.value

  if (maxFractionDigits != undefined) {
    value = toFixed(value, maxFractionDigits)
  }
  if (min != undefined && value < min) {
    value = min
  }
  if (max != undefined && value > max) {
    value = max
  }

  return value
}

//#region Handle events
const refInputNumber = ref<InstanceType<typeof InputNumber> | null>(null)

function onInput($event: InputNumberInputEvent) {
  // Cập nhật ngược trực tiếp giá trị input vào valueModel
  // (NOTE: ko cập nhật vào innerValue để ngăn xử lý xóa mất .0 như mô tả ở trên)
  const value = Number($event.value)
  const valueCorrected = correctValue(value)
  // Chỉ chặn xử lý tiếp khi mà không thỏa mãn giá trị
  if (valueCorrected != value) {
    innerValue.value = valueCorrected
  }

  // update giá trị chính xác vào valueModel
  amountValueModel.value = valueCorrected

  // Raise events
  emit("input", $event)
}

function onFocus($event: Event) {
  isEditing.value = true
  if (innerValue.value == 0) {
    innerValue.value = undefined
  }

  // Raise events
  emit("focus", $event)
  
  //setCursor()
}

function setCursor() {
  const $elm = $('input', unrefElement(refInputNumber.value as any))
  const elm = $elm.get(0) as HTMLInputElement
  
  console.log(refInputNumber.value, elm)
  nextTick(() => elm?.setSelectionRange(1,1)) // elm?.setSelectionRange(1,1) không hoạt động But elm?.setSelectionRange(1,2) worked ??

  //console.log( as HTMLInputElement)
  //(unrefElement(refInputNumber.value as any) as HTMLInputElement).setSelectionRange(0,0, "backward")
}

async function onBlur($event: InputNumberBlurEvent) {
  const value = $event.value
  if (!value) {
    // nextTick to update new value of innerValue after change it
    await nextTick()
    innerValue.value = 0
  }
  isEditing.value = false

  // Raise events
  emit("blur", $event)
}
//#endregion
</script>

<template>
  <InputNumber
    ref="refInputNumber"
    v-model:model-value="innerValue"
    locale="en-US"
    v-bind="{ ...$attrs }"
    :invalid="props.invalid"
    :disabled="props.disabled"
    :allow-empty="props.allowEmpty"
    :input-id="props.inputId"
    :input-class="props.inputClass"
    :input-props="props.inputProps"
    :input-style="props.inputStyle"
    :min-fraction-digits="isEditing ? undefined : innerMinFractionDigits"
    :max-fraction-digits="innerMaxFractionDigits"
    :min="innerMinAmount"
    :max="innerMaxAmount"
    @input="onInput"
    @focus="onFocus"
    @blur="onBlur"
  />
</template>

<style lang="scss"></style>
