<script setup lang="ts">
/**
 * npm i maska
 * npm i -D @types/maska
 * https://github.com/beholdr/maska
 * https://beholdr.github.io/maska/v3/#/vue
 */
import APPCONFIG from "@/appConfig"
import { truncNumber } from "@/utils/numberUtil"
import primevueUtil from "@/utils/primevueUtil"
import { useFocus } from "@vueuse/core"
import { Mask, MaskInputOptions } from "maska"
import { vMaska } from "maska/vue"
import type { InputTextProps } from "primevue/inputtext"
import InputText from "primevue/inputtext"
import { ComponentPublicInstance, computed, nextTick, ref, watch } from "vue"

// CHÚ Ý:
// - String(null) == 'null'
// - String(undefined) == 'undefined'
// - Number(0) == 0
// - Number("") == 0
// - Number("0") == 0
// - Number(null) == 0
// - Number(undefined) = NaN
// - formatNumeral(String(null)) = ''
// - formatNumeral(String(undefined)) = ''
// - formatNumeral(String('')) = ''
// - formatNumeral(String('0')) = '0'
// - formatNumeral(String('0.00')) = '0.00'

// TODO:
// Chưa hỗ trợ prefix

// Các chức năng
// - format ###,###.##
// - Khi focus out thì hiển thị đầy đủ
// - Khi bấm vào prefix/suffix thì tự động di chuyển con trỏ về đúng vị trí
// - Hỗ trợ bàn phím trên mobile
// - Cho phép gõ dấu "," thay cho dấu "."

const logDebug = (message: any, ...optionalParams: any[]) => {
  const enable = APPCONFIG.MODE_DEV && false

  enable && console.log.apply(console, [`InputAmount.${props.name}`, ...[message, ...optionalParams]])
}

defineOptions({
  inheritAttrs: false,
})

export interface InputAmountProps extends /* @vue-ignore */ Omit<InputTextProps, "mode" | "modelValue"> {
  /**
   * Cách thức xử lý hiển thị với giá trị amount.
   * Nếu là float thì: 1234.5 -> 1,234.5
   * Nếu là integer thì: 1234.5 -> 1,234
   * Nếu là currency thì: 1234.5 -> 1,234.50
   * @default "float"
   */
  mode?: "float" | "integer" | "currency"
  minFractionDigits?: number | undefined
  maxFractionDigits?: number | undefined
  min?: number | undefined
  max?: number | undefined
  name?: string | undefined
  suffix?: string | undefined
}

const props = withDefaults(defineProps<InputAmountProps>(), {
  ...primevueUtil.getDefaultProps(InputText),
  // Extend or change defaults value of props here:
  ...{
    mode: "float",
  },
})

const [_amountModel] = defineModel<number | null | undefined>()
const [_currencyModel] = defineModel<Nullable<string>>("currency")

const emit = defineEmits(["input", "blur", "focus", "click", "keydown"])

const _isEditing = ref<boolean>(false)
const _textValue = ref<Nullable<string>>()

const innerFractionDigitsMin = ref<number>()
const innerFractionDigitsMax = ref<number>()

const innerMinAmount = ref<number>()
const innerMaxAmount = ref<number>()

const innerSuffix = computed<string | undefined>(() => (props.suffix ? ` ${props.suffix}` : undefined))

//#region minFractionDigits
const defaultMinFractionDigits = computed(() => {
  const isVnd = _currencyModel.value?.toUpperCase() == "VND"
  const isEditing = _isEditing.value
  switch (props.mode) {
    case "currency":
      return isVnd || isEditing ? 0 : 2
    case "integer":
    case "float":
    default:
      return 0
  }
})
watch(
  [() => props.minFractionDigits, defaultMinFractionDigits],
  ([minFractionDigits, defaultMinFractionDigits]) => {
    innerFractionDigitsMin.value = minFractionDigits != undefined ? minFractionDigits : defaultMinFractionDigits
  },
  {
    immediate: true,
  }
)
//#endregion

//#region maxFractionDigits
const defaultMaxFractionDigits = computed(() => {
  switch (props.mode) {
    case "currency":
      return _currencyModel.value?.toUpperCase() == "VND" ? 0 : 2
    case "integer":
      return 0
    case "float":
    default:
      return 2
  }
})
watch(
  [() => props.maxFractionDigits, defaultMaxFractionDigits],
  ([maxFractionDigits, defaultMaxFractionDigits]) => {
    innerFractionDigitsMax.value = maxFractionDigits != undefined ? maxFractionDigits : defaultMaxFractionDigits
  },
  {
    immediate: true,
  }
)
//#endregion

//#region min
const defaultMin = computed(() => {
  switch (props.mode) {
    case "currency":
      return 0
    default:
      return undefined
  }
})
watch(
  [() => props.min, defaultMin],
  ([min, defaultMin]) => {
    innerMinAmount.value = min != undefined ? min : defaultMin
  },
  {
    immediate: true,
  }
)
//#endregion

//#region max
const defaultMax = computed(() => {
  switch (props.mode) {
    case "currency":
      return _currencyModel.value?.toUpperCase() == "VND"
        ? APPCONFIG.MAX_PAYMENTAMOUNT_VND
        : APPCONFIG.MAX_PAYMENTAMOUNT_USD
    case "integer":
      return APPCONFIG.MAX_NUMBER_INPUT_INT
    default:
      return APPCONFIG.MAX_NUMBER_INPUT_DEC
  }
})
watch(
  [() => props.max, defaultMax],
  ([max, defaultMax]) => {
    innerMaxAmount.value = max != undefined ? max : defaultMax
  },
  {
    immediate: true,
  }
)
//#endregion

//#region options
const maskaOptions = computed<MaskInputOptions>(() => {
  const min = innerMinAmount.value ?? 0
  //const max = innerMaxAmount.value ?? 0
  //const maxFractionDigits = innerFractionDigitsMax.value
  const allowMinus = min < 0
  const fraction = innerFractionDigitsMax.value
  const isEditing = _isEditing.value
  let preStartWithDot = false

  return {
    number: {
      unsigned: !allowMinus, // positive numbers only
      fraction: fraction,
      locale: "en-US",
    },
    preProcess: (val) => {
      let newVal = val
      newVal = processAllowCommaInsteadOfDot(newVal)
      preStartWithDot = newVal.startsWith(".")
      logDebug("preProcess", val, "->", newVal)
      return newVal
    },
    postProcess: (val) => {
      let newVal = val
      // Trường hợp đang edit thì bỏ dấu , để ngăn bug không thể xóa dấu , xảy ra.
      //newVal = newVal && isEditing ? newVal.replace(/[,]/g, "") : newVal
      // Trường hợp đang xóa (bsp/del) đến dấu . vd: 0.12 xóa 0 thì bỏ luôn số 0 ở đầu để cho phép hiển thị .12
      newVal = newVal && preStartWithDot ? newVal.replace(/^0\./, ".") : newVal
      // Xử lý hiển thị bổ sung suffix
      newVal = newVal && props.suffix ? `${newVal}${innerSuffix.value}` : newVal

      logDebug("postProcess", val, "->", newVal)
      return newVal
    },
  }
})
//#endregion options

const correctAmountValue = (value: Nullable<number>): Nullable<number> => {
  if (!value || Number.isNaN(value)) return value

  let correctedValue = value

  const minFractionDigits = innerFractionDigitsMin.value
  const maxFractionDigits = innerFractionDigitsMax.value
  const min = innerMinAmount.value
  const max = innerMaxAmount.value

  if (maxFractionDigits != undefined) {
    const truncValue = truncNumber(value, maxFractionDigits)
    if (value != truncValue) {
      logDebug(`Has correct value (maxFractionDigits:${maxFractionDigits})`, correctedValue, truncValue)
      correctedValue = truncValue
    }
  }
  if (min != undefined && value < min) {
    logDebug("Has correct value (min)", correctedValue, min)
    correctedValue = min
  }
  if (max != undefined && value > max) {
    logDebug("Has correct value (max)", correctedValue, max)
    correctedValue = max
  }

  return correctedValue
}

function isNullable<T>(value: Nullable<T>): value is null | undefined {
  return value == null
}

const formatValue = (value: Nullable<number>): Nullable<string> => {
  if (isNullable(value)) return undefined

  let formatedValue = new Mask(maskaOptions.value).masked(String(value))

  return formatedValue
}

const formatFullValue = (value: Nullable<number>): Nullable<string> => {
  if (isNullable(value)) value = 0

  let formatedValue = new Mask(maskaOptions.value).masked(String(value))

  if (formatedValue) {
    // Thực hiện bổ sung để hiển thị đầy đủ x,xxx.xx
    const minFractionDigits = innerFractionDigitsMin.value
    // const maxFractionDigits = innerMaxFractionDigits.value
    // const min = innerMinAmount.value
    // const max = innerMaxAmount.value

    if (minFractionDigits != undefined && minFractionDigits > 0) {
      const decimalFractions = formatedValue.split(".")
      const integerPart = decimalFractions[0] // input -12,342.50999974435 -> return: '-12,342'
      const fractionalPart: string | undefined = decimalFractions[1] // input -12,342.50999974435 -> return: '50999974435'
      const padZeroFractionalPart = String(fractionalPart || "").padEnd(minFractionDigits, "0") // input ('50999974435, 2) -> return '50'; input (', 2) -> return '00'
      formatedValue = `${integerPart}.${padZeroFractionalPart}`
    }
  }

  //logDebug("formatFullValue", value, "->", formatedValue, maskaOptions.value)

  return formatedValue
}

const unformatValue = (value: Nullable<string>): Nullable<number> => {
  if (isNullable(value)) return undefined

  const unformatValue = new Mask(maskaOptions.value).unmasked(value ?? "")

  //logDebug("unformatValue", value, "->", unformatValue, maskaOptions.value)

  return Number(unformatValue)
}

// update amount -> text (when lostfocus)
watch(_amountModel, (amountValue) => {
  // Chỉ tiếp tục xử lý khi NOT TYPING -> Đang edit thì bỏ qua
  if (_isEditing.value == true) return

  // update amount -> text
  const formatedValue = formatFullValue(amountValue)

  // update textValue
  _textValue.value = formatedValue

  // check correct
  const correctedValue = correctAmountValue(amountValue)
  if (correctedValue != amountValue) {
    nextTick(() => (_amountModel.value = correctedValue))
  }
})

// update text -> amount (when editing)
watch(_textValue, (textValue) => {
  // Chỉ tiếp tục xử lý khi TYPING -> Đang edit thì thực hiện
  if (_isEditing.value != true) return

  // update text -> amount
  const amountValue = unformatValue(textValue)

  // update modelValue
  _amountModel.value = amountValue

  // check correct
  const correctedValue = correctAmountValue(amountValue)
  if (correctedValue != amountValue) {
    nextTick(() => (_textValue.value = formatValue(correctedValue)))
  }
})

// update amount -> amount + amount -> text (when lostfocus)
watch(
  [_currencyModel, innerFractionDigitsMin, innerFractionDigitsMax, innerMinAmount, innerMaxAmount],
  () => {
    // Chỉ tiếp tục xử lý khi NOT TYPING -> Đang edit thì bỏ qua
    if (_isEditing.value == true) return

    // update amount -> amount + amount -> text if has change
    const amountValue = correctAmountValue(_amountModel.value)
    const formatedValue = formatFullValue(amountValue)

    // update modelValue
    _amountModel.value = amountValue

    // update textValue
    _textValue.value = formatedValue
  },
  {
    immediate: true,
  }
)

//#region Handle events
const refInputText = ref<ComponentPublicInstance<InstanceType<typeof InputText>> | null>(null)

const { focused } = useFocus(refInputText)

export type InputAmountEvent = {
  name: string
  displayValue: string
  value: Nullable<string | number>
}

function onInput(event: Event) {
  processMouseClickOnSuffix()

  logDebug("onInput", _textValue.value, _amountModel.value)

  // Raise events
  emit("input", event, {
    name: props.name,
    displayValue: _textValue.value,
    value: _amountModel.value,
  } as InputAmountEvent)
}

function onFocus(event: Event) {
  logDebug("onFocus", {
    formatedValue: _textValue.value,
    amountValue: _amountModel.value,
  })

  // Cập nhật mới lại toàn bộ trước khi focusIn sau đó mới chuyển trạng thái isEditing=true (ngăn update model-value)
  if (!_amountModel.value) {
    _textValue.value = ""
  } else {
    _textValue.value = formatValue(_amountModel.value)
  }
  _isEditing.value = true

  // Raise events
  emit("focus", event, {
    name: props.name,
    displayValue: _textValue.value,
    value: _amountModel.value,
  } as InputAmountEvent)
}

function onBlur(event: Event) {
  logDebug("onBlur", {
    formatedValue: _textValue.value,
    amountValue: _amountModel.value,
  })

  // Chuyển trạng thái isEditing=false (cho phép update model-value) trước để cập nhật mới lại toàn bộ sau khi focusOut
  _isEditing.value = false
  _textValue.value = formatFullValue(_amountModel.value)

  // Raise events
  emit("blur", event, {
    name: props.name,
    displayValue: _textValue.value,
    value: _amountModel.value,
  } as InputAmountEvent)
}

function onClick(event: Event) {
  processMouseClickOnSuffix()

  // Raise events
  emit("click", event, {
    name: props.name,
    displayValue: _textValue.value,
    value: _amountModel.value,
  } as InputAmountEvent)
}

function onKeydown(event: KeyboardEvent) {
  processTypingBackspaceDelete(event)

  // Raise events
  emit("keydown", event, {
    name: props.name,
    displayValue: _textValue.value,
    value: _amountModel.value,
  } as InputAmountEvent)
}
//#endregion

function processMouseClickOnSuffix() {
  const htmlInputElement = refInputText.value?.$el as HTMLInputElement
  if (isNullable(htmlInputElement)) return
  const formatedValue = _textValue.value
  if (isNullable(formatedValue)) return
  const caretPosition = htmlInputElement.selectionStart // caret = cursor
  if (isNullable(caretPosition)) return
  const suffix = innerSuffix.value
  if (isNullable(suffix)) return
  const suffixIndex = formatedValue.length - suffix.length
  const isUpdateCaretPosition = caretPosition && suffixIndex && caretPosition > suffixIndex

  logDebug("processUpdateCaretPosition", {
    caretPosition,
    formatedValue,
    suffix,
    suffixIndex,
    isUpdateCaretPosition,
  })

  if (isUpdateCaretPosition) {
    // đổi vị trí con trỏ lên trước suffix để thực hiện input
    htmlInputElement.setSelectionRange(suffixIndex, suffixIndex)
  }
}

function processTypingBackspaceDelete(event: KeyboardEvent) {
  const inputKey = event.key
  if (inputKey != "Backspace" && inputKey != "Delete") return
  const htmlInputElement = refInputText.value?.$el as HTMLInputElement
  if (isNullable(htmlInputElement)) return
  const formatedValue = _textValue.value
  if (isNullable(formatedValue)) return
  const caretPosition = htmlInputElement.selectionStart // caret = cursor
  if (isNullable(caretPosition)) return

  const firstTextIndex = 0
  const lastTextIndex = formatedValue.length - 1
  if (inputKey == "Backspace") {
    if (caretPosition <= firstTextIndex) return
    const backCaretPosition = caretPosition - 1

    if (formatedValue[backCaretPosition] == ",") {
      // đổi vị trí con trỏ lên trước suffix để thực hiện input
      htmlInputElement.setSelectionRange(backCaretPosition, backCaretPosition)
    }
    return
  }
  if (inputKey == "Delete") {
    if (caretPosition >= lastTextIndex) return
    const nextCaretPosition = caretPosition + 1

    if (formatedValue[caretPosition] == ",") {
      // đổi vị trí con trỏ lên trước suffix để thực hiện input
      htmlInputElement.setSelectionRange(nextCaretPosition, nextCaretPosition)
    }
    return
  }
}

function processAllowCommaInsteadOfDot(value: string) {
  const COMMA = ","
  const DOT = "."
  let newVal = value
  const htmlInputElement = refInputText.value?.$el as HTMLInputElement
  if (isNullable(htmlInputElement)) return newVal
  const formatedValue = _textValue.value
  if (isNullable(formatedValue)) return newVal
  const caretPosition = htmlInputElement.selectionStart // caret = cursor
  if (isNullable(caretPosition)) return newVal
  const backCaretPosition = caretPosition - 1
  logDebug("preProcess", "caretPosition", newVal[backCaretPosition])
  if (backCaretPosition > -1 && newVal[backCaretPosition] == COMMA) 
  {
    // user is typing "," -> replace to .
    newVal = newVal.substring(0, backCaretPosition) + DOT + newVal.substring(backCaretPosition + 1);
  }
  return newVal
}

defineExpose({
  amountValueModel: _amountModel, // make sure you expose the bound variable
  focus: (state: boolean) => (focused.value = state),
})
</script>

<template>
  <InputText
    ref="refInputText"
    v-bind="{ ...$attrs, ...$props }"
    v-model.lazy="_textValue"
    v-maska="maskaOptions"
    type="text"
    inputmode="decimal"
    @input="onInput"
    @focus="onFocus"
    @blur="onBlur"
    @click="onClick"
    @keydown="onKeydown"
  ></InputText>
</template>

<style lang="scss"></style>
