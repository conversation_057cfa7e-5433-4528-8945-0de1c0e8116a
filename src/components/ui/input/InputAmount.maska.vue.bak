<script setup lang="ts">
/**
 * npm i maska
 * npm i -D @types/maska
 * https://github.com/beholdr/maska
 * https://beholdr.github.io/maska/v3/#/vue
 *
 * TODO: hỗ trợ focus
 * TODO: bấm vào prefix|suffix thì đưa con trỏ về nunber
 */
import APPCONFIG from "@/app.config"
import { truncNumber } from "@/utils/numberUtil"
import primevueUtil from "@/utils/primevueUtil"
import type { InputTextProps } from "primevue/inputtext"
import InputText from "primevue/inputtext"
import { ComponentPublicInstance, computed, nextTick, ref, watch } from "vue"
//import { formatNumeral, unformatNumeral, type FormatNumeralOptions } from "cleave-zen"
//import Cleave from 'vue-cleave-component'
import { useFocus } from "@vueuse/core"
import { Mask, MaskaDetail, MaskInputOptions } from "maska"
import { vMaska } from "maska/vue"

// CHÚ Ý:
// - String(null) == 'null'
// - String(undefined) == 'undefined'
// - Number(0) == 0
// - Number("") == 0
// - Number("0") == 0
// - Number(null) == 0
// - Number(undefined) = NaN
// - formatNumeral(String(null)) = ''
// - formatNumeral(String(undefined)) = ''
// - formatNumeral(String('')) = ''
// - formatNumeral(String('0')) = '0'
// - formatNumeral(String('0.00')) = '0.00'

const logDebug = (message: any, ...optionalParams: any[]) => {
  const enable = true

  enable && console.log.apply(console, [`InputAmount.${props.name}`, ...[message, ...optionalParams]])
}

defineOptions({
  inheritAttrs: false,
})

export interface InputAmountProps extends /* @vue-ignore */ Omit<InputTextProps, "mode" | "modelValue"> {
  /**
   * Cách thức xử lý hiển thị với giá trị amount.
   * Nếu là float thì: 1234.5 -> 1,234.5
   * Nếu là integer thì: 1234.5 -> 1,234
   * Nếu là currency thì: 1234.5 -> 1,234.50
   * @default "float"
   */
  mode?: "float" | "integer" | "currency"
  minFractionDigits?: number | undefined
  maxFractionDigits?: number | undefined
  min?: number | undefined
  max?: number | undefined
  name?: string | undefined
  suffix?: string | undefined
}

const props = withDefaults(defineProps<InputAmountProps>(), {
  ...primevueUtil.getDefaultProps(InputText),
  // Extend or change defaults value of props here:
  ...{
    mode: "float",
  },
})

const [_amountModel] = defineModel<number | null | undefined>()
const [_currencyModel] = defineModel<Nullable<string>>("currency")

const emit = defineEmits(["input", "blur", "focus"])

const _isEditing = ref<boolean>(false)
const _textValue = ref<Nullable<string>>()

const innerFractionDigitsMin = ref<number>()
const innerFractionDigitsMax = ref<number>()

const innerMinAmount = ref<number>()
const innerMaxAmount = ref<number>()

//#region minFractionDigits
const defaultMinFractionDigits = computed(() => {
  switch (props.mode) {
    case "currency":
      return _currencyModel.value?.toUpperCase() == "VND" ? 0 : 2
    case "integer":
    case "float":
    default:
      return 0
  }
})
watch(
  [() => props.minFractionDigits, defaultMinFractionDigits],
  ([minFractionDigits, defaultMinFractionDigits]) => {
    innerFractionDigitsMin.value = minFractionDigits != undefined ? minFractionDigits : defaultMinFractionDigits
  },
  {
    immediate: true,
  }
)
//#endregion

//#region maxFractionDigits
const defaultMaxFractionDigits = computed(() => {
  switch (props.mode) {
    case "currency":
      return _currencyModel.value?.toUpperCase() == "VND" ? 0 : 2
    case "integer":
      return 0
    case "float":
    default:
      return 2
  }
})
watch(
  [() => props.maxFractionDigits, defaultMaxFractionDigits],
  ([maxFractionDigits, defaultMaxFractionDigits]) => {
    innerFractionDigitsMax.value = maxFractionDigits != undefined ? maxFractionDigits : defaultMaxFractionDigits
  },
  {
    immediate: true,
  }
)
//#endregion

//#region min
const defaultMin = computed(() => {
  switch (props.mode) {
    case "currency":
      return 0
    default:
      return undefined
  }
})
watch(
  [() => props.min, defaultMin],
  ([min, defaultMin]) => {
    innerMinAmount.value = min != undefined ? min : defaultMin
  },
  {
    immediate: true,
  }
)
//#endregion

//#region max
const defaultMax = computed(() => {
  switch (props.mode) {
    case "currency":
      return _currencyModel.value?.toUpperCase() == "VND"
        ? APPCONFIG.MAX_PAYMENTAMOUNT_VND
        : APPCONFIG.MAX_PAYMENTAMOUNT
    case "integer":
      return APPCONFIG.MAX_NUMBER_INPUT_INT
    default:
      return APPCONFIG.MAX_NUMBER_INPUT_DEC
  }
})
watch(
  [() => props.max, defaultMax],
  ([max, defaultMax]) => {
    innerMaxAmount.value = max != undefined ? max : defaultMax
  },
  {
    immediate: true,
  }
)
//#endregion

//#region options
const maskaOptions = computed<MaskInputOptions>(() => {
  const min = innerMinAmount.value ?? 0
  //const max = innerMaxAmount.value ?? 0
  //const maxFractionDigits = innerFractionDigitsMax.value
  const allowMinus = min < 0

  return {
    number: {
      unsigned: !allowMinus, // positive numbers only
      fraction: innerFractionDigitsMax.value,
      locale: "en-US",
    },
    postProcess: (val) => (val && props.suffix ? `${val} ${props.suffix}` : val),
    //eager: false,
    //reversed: true
  }
})
//#endregion options

const correctAmountValue = (value: Nullable<number>): Nullable<number> => {
  if (!value || Number.isNaN(value)) return value

  let correctedValue = value

  const minFractionDigits = innerFractionDigitsMin.value
  const maxFractionDigits = innerFractionDigitsMax.value
  const min = innerMinAmount.value
  const max = innerMaxAmount.value

  if (maxFractionDigits != undefined) {
    const truncValue = truncNumber(value, maxFractionDigits)
    if (value != truncValue) {
      logDebug(`Has correct value (maxFractionDigits:${maxFractionDigits})`, correctedValue, truncValue)
      correctedValue = truncValue
    }
  }
  if (min != undefined && value < min) {
    logDebug("Has correct value (min)", correctedValue, min)
    correctedValue = min
  }
  if (max != undefined && value > max) {
    logDebug("Has correct value (max)", correctedValue, max)
    correctedValue = max
  }

  return correctedValue
}

function isNullable<T>(value: Nullable<T>): value is null | undefined {
  return value == null
}

const formatValue = (value: Nullable<number>): Nullable<string> => {
  if (isNullable(value)) return undefined

  let formatedValue = new Mask(maskaOptions.value).masked(String(value))

  return formatedValue
}

const formatFullValue = (value: Nullable<number>): Nullable<string> => {
  let formatedValue = new Mask(maskaOptions.value).masked(String(value ?? 0))

  if (formatedValue) {
    // Thực hiện bổ sung để hiển thị đầy đủ x,xxx.xx
    const minFractionDigits = innerFractionDigitsMin.value
    // const maxFractionDigits = innerMaxFractionDigits.value
    // const min = innerMinAmount.value
    // const max = innerMaxAmount.value

    if (minFractionDigits != undefined && minFractionDigits > 0) {
      const decimalFractions = formatedValue.split(".")
      const integerPart = decimalFractions[0] // input -12,342.50999974435 -> return: '-12,342'
      const fractionalPart: string | undefined = decimalFractions[1] // input -12,342.50999974435 -> return: '50999974435'
      const padZeroFractionalPart = String(fractionalPart || "").padEnd(minFractionDigits, "0") // input ('50999974435, 2) -> return '50'; input (', 2) -> return '00'
      formatedValue = `${integerPart}.${padZeroFractionalPart}`
    }
  }

  return formatedValue
}

const unformatValue = (value: Nullable<string>): Nullable<number> => {
  if (isNullable(value)) return undefined

  const unformatValue = new Mask(maskaOptions.value).unmasked(value ?? "")

  return Number(unformatValue)
}

// update amount -> text (when lostfocus)
watch(_amountModel, (amountValue) => {
  // Chỉ tiếp tục xử lý khi NOT TYPING
  //if (_isEditing.value != true) return

  // update amount -> text
  //const formatedValue = _isEditing.value == true ? formatValue(amountValue) : formatFullValue(amountValue)
  const formatedValue = formatValue(amountValue)

  // update textValue
  _textValue.value = formatedValue

  // check correct
  const correctedValue = correctAmountValue(amountValue)
  if (correctedValue != amountValue) {
    nextTick(() => (_amountModel.value = correctedValue))
  }
})

// update text -> amount (when editing)
watch(_textValue, (textValue) => {
  // Chỉ tiếp tục xử lý khi TYPING
  //if (_isEditing.value == true) return

  // update text -> amount
  const amountValue = unformatValue(textValue)

  // update modelValue
  _amountModel.value = amountValue
})

// update amount -> amount + amount -> text (when lostfocus)
watch([innerFractionDigitsMin, innerFractionDigitsMax, innerMinAmount, innerMaxAmount], () => {
  // Chỉ tiếp tục xử lý khi NOT TYPING
  //if (_isEditing.value != true) return

  // update amount -> amount + amount -> text
  const amountValue = _amountModel.value
  if (isNullable(amountValue)) return

  // update modelValue
  const correctedValue = correctAmountValue(amountValue)
  _amountModel.value = correctedValue
  // update textValue (Đoạn này thừa do đã được update nhờ watch modelValue)
  const formatedValue = formatFullValue(correctedValue)
  _textValue.value = formatedValue
})

//#region Handle events
const refInputText = ref<ComponentPublicInstance<InstanceType<typeof InputText>> | null>(null)

const { focused } = useFocus(refInputText)

export type InputAmountEvent = {
  name: string
  displayValue: string
  value: Nullable<string | number>
}

function onInput(event: Event) {
  logDebug("onInput", _textValue.value, _amountModel.value)

  // Raise events
  emit("input", {
    name: props.name,
    displayValue: _textValue.value,
    value: _amountModel.value,
  } as InputAmountEvent)
}

function onFocus(event: Event) {
  logDebug("onFocus", {
    formatedValue: _textValue.value,
    amountValue: _amountModel.value,
  })

  // Cập nhật mới lại toàn bộ trước khi focusIn sau đó mới chuyển trạng thái isEditing=true (ngăn update model-value)
  if (!_amountModel.value) {
    _textValue.value = ""
  } else {
    _textValue.value = formatValue(_amountModel.value)
  }
  _isEditing.value = true

  // Raise events
  emit("focus", {
    name: props.name,
    displayValue: _textValue.value,
    value: _amountModel.value,
  } as InputAmountEvent)
}

async function onBlur(event: Event) {
  logDebug("onBlur", {
    formatedValue: _textValue.value,
    amountValue: _amountModel.value,
  })

  // Chuyển trạng thái isEditing=false (cho phép update model-value) trước để cập nhật mới lại toàn bộ sau khi focusOut
  _isEditing.value = false
  _textValue.value = formatFullValue(_amountModel.value)

  // Raise events
  emit("blur", {
    name: props.name,
    displayValue: _textValue.value,
    value: _amountModel.value,
  } as InputAmountEvent)
}
//#endregion

defineExpose({
  amountValueModel: _amountModel, // make sure you expose the bound variable
  focus: (state: boolean) => focused.value = state
})
</script>

<template>
  <InputText
    ref="refInputText"
    v-bind="{ ...$attrs, ...$props }"
    v-model.lazy="_textValue"
    v-maska="maskaOptions"
    @input="onInput"
    @focus="onFocus"
    @blur="onBlur"
  ></InputText>
</template>

<style lang="scss"></style>
