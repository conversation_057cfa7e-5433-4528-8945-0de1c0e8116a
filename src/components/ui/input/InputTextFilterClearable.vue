<!-- eslint-disable vue/require-default-prop -->
<script lang="ts" setup>
import ClearInputIcon from "@/assets/icons/clear-input.svg"
import SearchIcon from "@assets/icons/search.svg"

import primevueUtil from "@/utils/primevueUtil"
import { useFocus, watchDebounced } from "@vueuse/core"
import type { InputTextProps } from "primevue/inputtext"
import InputText from "primevue/inputtext"
import { ref, watch } from "vue"

defineOptions({
  inheritAttrs: false,
})

type CssClass = string | Record<string, boolean> | (string | Record<string, boolean>)[]

export interface InputTextFilterClearableProps extends /* @vue-ignore */ InputTextProps {
  delay?: number
  wrapperClass?: CssClass
  inputClass?: CssClass
  iconClass?: CssClass
  iconClearClass?: CssClass
}

const props = withDefaults(defineProps<InputTextFilterClearableProps>(), {
  ...primevueUtil.getDefaultProps(InputText),
  // Extend or change defaults value of props here:
  ...{
    // Mặc định luôn dùng delay
    //delay: APPCONFIG.DEBOUNCED_DELAY_INPUT,
    delay: 0,
  },
})

const [modelValueModel] = defineModel<Nullable<string>>("modelValue")

const emit = defineEmits(["input", "clear"])
// const emit = defineEmits<{
//   /**
//    * Emitted when the value changes.
//    * @param {string} value - New value.
//    */
//   "update:modelValue": [event: string | undefined] // named tuple syntax
// }>()

// Mặc định hỗ trợ debounce cho input text
const innerValue = ref<Nullable<string>>()
watch(
  modelValueModel,
  (newValue) => {
    if (newValue != innerValue.value) {
      innerValue.value = newValue
    }
  },
  {
    immediate: true,
  }
)
watchDebounced(
  innerValue,
  (newValue) => {
    modelValueModel.value = newValue
  },
  {
    debounce: props.delay,
  }
)

// Hỗ trợ re-focus sau khi bấm button clear
const refInputText = ref<HTMLInputElement>()
const { focused } = useFocus(refInputText)

function onClickClear() {
  innerValue.value = ""

  emit("input", { target: { value: "" } })
  emit("clear")
  focusInput(true)
}

function onInput($event: Event) {
  emit("input", $event)
}

const focusInput = (isFocus: boolean) => {
  focused.value = isFocus
}

defineExpose({
  focusInput: focusInput,
})
</script>

<template>
  <IconField class="inputtext-filterclearable p-icon-field-left p-icon-field-right" :class="props.wrapperClass">
    <InputIcon>
      <!-- <SearchIcon /> -->
      <SvgIcon :src="SearchIcon" :class="props.iconClass" />
    </InputIcon>
    <InputText
      ref="refInputText"
      v-bind="{ ...$attrs, ...$props, ...$emit }"
      v-model:model-value="innerValue"
      :class="props.inputClass"
      @input="onInput"
      @focusout="() => (innerValue = innerValue?.trim())"
    />
    <InputIcon v-if="innerValue?.length" class="user-clickable" @click="onClickClear">
      <!-- <ClearInputIcon /> -->
      <!-- <SvgIcon name="svgicon-xcircle" /> -->
      <SvgIcon :src="ClearInputIcon" :class="props.iconClearClass" />
    </InputIcon>
  </IconField>
</template>

<style lang="scss">
.inputtext-filterclearable {
  &.p-icon-field > .p-input-icon {
    top: 0;
    right: 0;
    bottom: 0;
    padding: 0 0.5rem;
    margin-top: 0;
    display: flex;
    justify-content: center;
    align-items: center;

    &:first-child {
      left: 0;
      right: unset;
      padding-left: 0.75rem;
    }
    &:last-child {
      padding-right: 0.75rem;
    }
  }

  &.p-inputwrapper > .p-inputtext:focus + .p-input-icon {
    z-index: 1;
  }

  .p-inputtext {
    padding-right: 2.75rem; //52px = calc(1.25rem + 1rem + 0.5)
    &:placeholder-shown {
      padding-right: 1rem; // revert khi input không có value
    }
  }

  .p-input-icon svg {
    width: 1.25rem;
    height: 1.25rem;
  }

  .p-inputgroup > & {
    display: flex;

    &:not(:first-child) > .p-inputtext {
      border-left: none;
    }
  }
}
</style>
