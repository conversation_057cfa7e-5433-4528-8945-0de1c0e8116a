<script setup lang="ts">
  import { defineAsyncComponent } from "vue"

  //import ConfirmDialog from 'primevue/confirmdialog';
  const ConfirmDialog = defineAsyncComponent(() => import("primevue/confirmdialog"))

  defineOptions({
    inheritAttrs: false,
  })
</script>

<template>
  <ConfirmDialog group="y"></ConfirmDialog>
  <ConfirmDialog
    v-bind="{ ...$attrs }"
    :draggable="false"
    class="app-confirm-dialog"
  >
    <template #container="{ message, acceptCallback, rejectCallback }">
      <div class="p-dialog-header">
        <span class="p-dialog-title">{{ message.header }}</span>
      </div>
      <div class="p-dialog-content">
        <span class="p-confirm-dialog-message">{{ message.message }}</span>
      </div>
      <div class="p-dialog-footer">
        <Button
          :severity="message.rejectSeverity ?? 'cancel'"
          class="p-confirm-dialog-reject"
          :label="message.rejectLabel"
          :icon="message.rejectIcon"
          :class="message.rejectClass"
          @click="rejectCallback"
        ></Button>
        <Button
          :severity="message.acceptSeverity ?? 'primary'"
          class="p-confirm-dialog-accept"
          :label="message.acceptLabel"
          :icon="message.acceptIcon"
          :class="message.acceptClass"
          @click="acceptCallback"
        ></Button>
      </div>
    </template>

    <!-- <template #icon="{ class: className }">
      <SvgIcon v-if="className.includes('p-toast-message-icon-success')" :src="ToastSuccessIcon" :class="className" />
      <SvgIcon v-else-if="className.includes('p-toast-message-icon-error')" :src="ToastErrorIcon" :class="className" />
      <SvgIcon v-else :src="UndefinedIcon" :class="className" />

      !-- <AppSvgNamedIcon :name="`toast-${}`" /> --
    </template> -->
  </ConfirmDialog>
</template>

<style lang="scss">
.app-confirm-dialog {
  width: 30rem;
  
  .p-dialog-header {
    padding: 1.5rem 1.5rem .75rem;
  }
  .p-dialog-content {
    padding: 0 1.5rem 2rem;
  }
  .p-dialog-footer {
    padding: 0 1.5rem 1.5rem;
    justify-content: space-around;
    gap: 1rem;

    >.p-button {
      width: 100%;
    }
  }
}
</style>