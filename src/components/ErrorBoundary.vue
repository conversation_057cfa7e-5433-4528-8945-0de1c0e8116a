<script setup lang="ts">
import { useToastService } from '@/services/toastService';
import { onErrorCaptured } from 'vue';
import { useI18n } from 'vue-i18n';

const toastService = useToastService()
const { t } = useI18n()

onErrorCaptured((error) => {
  console.error("ErrorBoundary", error)

  // Show a generic error message
  // alert("An error occurred. Please try again later.")
  toastService.error({
    summary: t("common.toast.error-summary"),
  })

  return false // Prevents the error from propagating further
})
</script>

<template>
  <!-- <div v-if="!!error">Whoops! {{ error }}</div> -->
  <!-- <template v-else><slot></slot></template> -->
  <slot></slot>
</template>

<style scoped>
.error-boundary {
  height: 100%;
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
}
</style>
