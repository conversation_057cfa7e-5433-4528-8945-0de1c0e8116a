<script setup lang="ts">
import Requied from '@/assets/icons/tms-requied.svg';

import { EnumPaymentLinkType, InfoFieldOptions } from '@/app.const';
import { useAuthService } from '@/services/authService';
import { useDashBoardService } from '@/services/dashBoardService';
import { InstallmentSettings, OnePayCards, OneTimeLinkProp, PaymentLinkRequest, PresetAmountJson } from '@/types/apis';
import { useToggle } from '@vueuse/core';
import { computed, ref } from 'vue';
import { GetByIdPaymentLinkOutput, usePaymentLinkService } from '@/services/paymentLinkService';
import { useToastService } from '@/services/toastService';
import APPCONFIG from '@/appConfig';
import { useAsyncTask } from 'vue-concurrency';
import { storeToRefs } from 'pinia';
import { makeRandom } from "@/utils/uuidUtil";

const [visible, toggleVisible] = useToggle();
const { form, formValidator, clearFormValidator } = useDashBoardService();
const { authMerchantProfile } = useAuthService();
const paymentLinkService = usePaymentLinkService()
const {
  stringToSlug,
  checkExitUriTask,
  getInstallmentsTask,
  getCurrencyConfigTask,
  addNewPaymentLinkTask,
  getByIdPaymentLinkTask,
} = paymentLinkService;
const { form: paymentLinkForm } = storeToRefs(paymentLinkService)

const presetAmount: PresetAmountJson =
{
  amount: null,
  enableDiscount: false,
  discount: null,
  discountIsPercent: false,
  enableTax: false,
  tax: null,
  enableOtherFee: false,
  otherFeeLabel: '',
  otherFee: null,
  otherFeeIsPercent: false
}

const request = ref<PaymentLinkRequest>({
  routerUri: '',
  name: '',
  description: '',
  dueDate: null,
  paymentMethods: authMerchantProfile ? authMerchantProfile.cfgPaymentMethods : 0,
  installmentSettings: '',
  hasPresetAmount: false,
  presetAmountJson: '',
  totalAmount: 0,
  currency: authMerchantProfile ? authMerchantProfile.defaultCurrency : '',
  exchangeRate: 0,
  paymentAmount: 0,
  paymentCurrency: authMerchantProfile ? authMerchantProfile.defaultCurrency : '',
  hasLimitPayment: true,
  maxPaymentQty: 1,
  totalPaymentQty: 0,
  infoFieldOptions: (InfoFieldOptions.enableName | InfoFieldOptions.requireName),
  customerNoteLckey: [],
  fileId: null,
  fileName: null,
  enable: true,
  linkType: EnumPaymentLinkType.OneTime
} as PaymentLinkRequest);

const toastService = useToastService();

const errorFields = computed(() => formValidator.errorFields);

const getPaymentById = ref<GetByIdPaymentLinkOutput>();

const showDialogSuccess = ref(false);

const oneTimeProp = ref<OneTimeLinkProp>({});

const [isShowAddNewDialog, toggleShowAddNewPopup] = useToggle();

const onCloseAddNewPPopup = () => {
  toggleShowAddNewPopup(false)
}

const nameToRouterUri = (linkName: string) => {
  return stringToSlug(linkName).trim().split(' ').join('-').replaceAll('--', '-');
}

const createRouterUri = async (linkName?: string) => {
  let numberOfExecutions = 0;
  let routerUri = nameToRouterUri(linkName ?? request.value.name);

  while ((await checkExitUriTask.perform({ routerUri: routerUri, orderId: null })).exist) {
    numberOfExecutions++
    routerUri = autoBuidNewUri(routerUri, numberOfExecutions);
  }

  return routerUri;
}

const autoBuidNewUri = (uri: string, numberOfExecutions: number) => {
  let routerUri = nameToRouterUri(form.name)

  switch (numberOfExecutions) {
    case 0:
      return uri;
    default:
      if (routerUri.length >= 193) {
        routerUri = routerUri.substring(0, 193)
      }

      return `${routerUri}-${makeRandom(6)}`;
  }
}

const getAllTime = (cards: OnePayCards[]) => {
  const result: number[] = [];
  cards.forEach(element => {
    element.times.forEach(el => {
      if (result.indexOf(el.time) < 0)
        result.push(el.time);
    })
  });

  return result
}

const getInstallmentSetting = async () => {
  const installment: InstallmentSettings[] = []

  if (authMerchantProfile && authMerchantProfile.cfgPaymentMethods && (authMerchantProfile.cfgPaymentMethods & APPCONFIG.INSTALLMENT) > 0) {
    const data = await getInstallmentsTask.perform({merchantId: authMerchantProfile.id});

    data.installments.forEach(element => {
      installment.push({
        bankId: element.bank_id,
        times: getAllTime(element.cards)
      })
    });
  }

  return JSON.stringify(installment);
}

const getExchangeRate = async () => {
  const data = (await getCurrencyConfigTask.perform(authMerchantProfile?.id)).find(item => item.currency === authMerchantProfile?.defaultCurrency);

  return data ? data.exchangeRate : null;
}

const submitTask = useAsyncTask(async (signal, input: void) => {
  const formName = form.name.trim();
  form.routerUri = await createRouterUri(formName);

  await formValidator.execute();
  const isValid = formValidator.isFinished && formValidator.pass
  if (!isValid) return;

  request.value.name = formName
  request.value.routerUri = form.routerUri
  if (form.amount) {
    presetAmount.amount = form.amount;
    request.value.hasPresetAmount = true;
    request.value.totalAmount = form.amount;
    request.value.paymentAmount = form.amount;
  } else {
    presetAmount.amount = null;
    request.value.hasPresetAmount = false;
    request.value.totalAmount = null;
    request.value.paymentAmount = null;
  }

  paymentLinkForm.value.name = request.value.name;
  paymentLinkForm.value.routerUri = request.value.routerUri;

  request.value.installmentSettings = await getInstallmentSetting();
  request.value.exchangeRate = await getExchangeRate();
  request.value.presetAmountJson = JSON.stringify(presetAmount);

  const data = await addNewPaymentLinkTask.perform(request.value);

  if (data?.id) {
    getPaymentById.value = await getByIdPaymentLinkTask.perform(data.id);
    if (getPaymentById.value) {
      showDialogSuccess.value = true;
    }
    toastService.success({})
  } else {
    toastService.error({}); // TODO: để tạm
  }

  toggleVisible(false);
})
const submit = () => submitTask.perform()

const onShow = async () => {
  form.name = '';
  form.amount = undefined;
  if (authMerchantProfile && authMerchantProfile.defaultCurrency) {
    form.currency = authMerchantProfile.defaultCurrency;
  }

  request.value.presetAmountJson = '';
  request.value.totalAmount = 0;
  request.value.paymentAmount = 0;
  request.value.routerUri = '';
  request.value.name = '';
  request.value.description = '';
  request.value.hasPresetAmount = false;
  request.value.installmentSettings = '';
  request.value.exchangeRate = null;

  await formValidator.execute()
  clearFormValidator()
}

const openAddnew = () => {
  oneTimeProp.value =
  {
    linkName: request.value.name,
    routerUri: request.value.routerUri,
    description: request.value.description,
    amount: form.amount,
    hasPresetAmount: request.value.hasPresetAmount,
    hasLimitPayment: request.value.hasLimitPayment,
    maxPaymentQty: request.value.maxPaymentQty,
    totalPaymentQty: request.value.totalPaymentQty,
    totalAmount: request.value.totalAmount,
    paymentAmount: request.value.paymentAmount,
    currency: request.value.currency,
    paymentCurrency: request.value.paymentCurrency,
    linkType: request.value.linkType
  }

  toggleShowAddNewPopup(true)
}

const closeSuccess = () => {
  showDialogSuccess.value = false
}

const show = () => {
  toggleVisible(true)
}
const hide = () => {
  toggleVisible(false)
}

defineExpose({
  show,
  hide,
})
</script>

<template>
  <Dialog
    v-model:visible="visible"
    modal
    :header="$t('component-one-time-link.header')"
    :draggable="false"
    class="dialog-one-time-link"
    @show="onShow"
  >
    <div class="mb-3">
      <div class="group-label mb-1">
        <label for="name" class="mr-1">{{ $t("component-one-time-link.content.link-name") }}</label>
        <div class="icon-requied">
          <SvgIcon :src="Requied" />
        </div>
      </div>

      <InputTextClearable
        v-model="form.name"
        maxlength="200"
        :invalid="errorFields?.name?.any() ?? false"
        class="one-time-link-input one-time-link-name"
      />

      <div v-if="errorFields?.name?.length" class="text-error">
        {{ errorFields.name[0].message }}
      </div>
    </div>

    <div class="mb-3">
      <div class="group-label mb-1">
        <label for="name" class="mr-1">{{ $t("component-one-time-link.content.amount") }}</label>
      </div>

      <!-- <InputNumberNew
        v-if="authMerchantProfile && authMerchantProfile.defaultCurrency === 'USD'"
        v-model="form.amount"
        locale="en-US"
        :min-fraction-digits="2"
        :max-fraction-digits="2"
        :suffix="` ${authMerchantProfile?.defaultCurrency}`"
        :invalid="errorFields?.amount?.any() ?? false"
        :is-integer="false"
        :is-percent="false"
        class="one-time-link-input one-time-link-amount w-100"
      /> -->

      <!-- <InputNumberNew
        v-else-if="authMerchantProfile && authMerchantProfile.defaultCurrency === 'VND'"
        v-model="form.amount"
        locale="en-US"
        :suffix="` ${authMerchantProfile?.defaultCurrency}`"
        :invalid="errorFields?.amount?.any() ?? false"
        :is-integer="false"
        :is-percent="false"
        class="one-time-link-input one-time-link-amount w-100"
      /> -->
      <InputAmount
        v-model:model-value="form.amount"
        name="onetimeLink-amount"
        class="one-time-link-input one-time-link-amount w-100"
        mode="currency"
        :currency="authMerchantProfile?.defaultCurrency"
        :suffix="`${authMerchantProfile?.defaultCurrency}`"
        :invalid="errorFields?.amount?.any() ?? false"
      />

      <div v-if="errorFields?.amount?.length" class="text-error">
        {{ errorFields.amount[0].message }}
      </div>
    </div>

    <div class="mb-3">
      <div class="d-flex mb-1 justify-content-between">
        <label for="description" class="tms-label-add-edit">{{ $t("component-one-time-link.content.description") }}</label>
        <span class="text-count">{{ `${request.description?.length ?? 0}/500` }}</span>
      </div>
      <Textarea v-model="request.description" :maxlength="500" class="tms-InputText tms-InputText-description w-100" style="height: 76px;"></Textarea>
    </div>

    <div class="">
      <div class="detail-explanation">
        <div class="detail-one-time-link">
          {{ $t("component-one-time-link.content.note.one-time-link") }}
        </div>

        <div class="detail-one-time-link more-advanced">
          {{ $t("component-one-time-link.content.note.more-advanced") }}
          <a href="javascript:void(0)" class="action" @click="openAddnew">{{ $t("component-one-time-link.content.note.action") }}</a>
        </div>
      </div>
    </div>

    <template #footer>
      <Button
        class="btn-crete"
        :label="$t('component-one-time-link.footer')"
        :disabled="submitTask.isRunning || form.name.trim().length <= 0"
        @click="submit"
      />
    </template>

    <Teleport to="body">
      <div class="create-one-time-link-loading">
        <AppLoading v-if="addNewPaymentLinkTask.isRunning">
        </AppLoading>
      </div>
    </Teleport>
  </Dialog>

  <Lazy>
    <AddNew
      v-if="isShowAddNewDialog"
      :visible="isShowAddNewDialog"
      :one-time-prop="oneTimeProp"
      @close-popup-add="onCloseAddNewPPopup"
    />

    <AddSuccessPayment
      v-if="showDialogSuccess"
      v-model="getPaymentById"
      :visible="showDialogSuccess"
      @close-popup-success="closeSuccess"
    />
  </Lazy>
</template>

<style lang="scss">
.dialog-one-time-link {
  max-width: 540px;
  width: 100%;
  border-radius: 8px;

  .p-dialog-header {
    padding: 16px 24px;
    margin-bottom: 12px;
  }

  .p-dialog-content {
    padding: 0 24px;

    .group-label {
      display: flex;

      .icon-requied {
        width: 8px;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        margin-top: 4px;
      }
    }

    .one-time-link-input {
      height: 52px;
    }

    .detail-explanation {
      background-color: #F5F5F5;
      padding: 8px 12px;
      border-radius: 4px;
      height: 48px;

      .detail-one-time-link {
        font-size: 12px;
        line-height: 16px;
        font-weight: 400;
        color: #404040;

        .action {
          font-weight: 500;
          text-decoration: none;
          color: #2E6BE5;
        }
      }
    }

    .text-error {
      font-weight: 400;
      line-height: 16px;
      color: #D82039;
      margin-top: 4px;
    }
  }

  .p-dialog-footer {
    padding: 24px;

    .btn-crete {
      width: 100%;
      padding: 12px 24px;
      height: 48px;
    }
  }
}

.create-one-time-link-loading {
  .component-loading {
    position: fixed;
    top: 0;
    z-index: 9999;
    background-color: black;
    opacity: 0.75;
  }
}
</style>
