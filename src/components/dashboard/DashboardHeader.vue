<script setup lang="ts">

import APPCONFIG from "@/appConfig"
import { useAuthService } from "@/services/authService"
import { useToastService } from "@/services/toastService"
import { getImgSrc } from "@/utils/appUtil"
import { useClipboard } from "@vueuse/core"
import { useQRCode } from "@vueuse/integrations/useQRCode"
import { computed, ref, watch } from "vue"

import TemplateQrCodeDownload from "../TemplateQrCodeDownload.vue"
import LockIcon from "@assets/lock.svg"
import { createPayment } from "@/apis/paymentApi"

const { authMerchantProfile, authUserMerchantsList } = useAuthService()
const { copy } = useClipboard()
const toastService = useToastService()
const logo = getImgSrc(authMerchantProfile?.logoFileId)
const qrCodeDownload = ref<InstanceType<typeof TemplateQrCodeDownload> | null>(null)

const amount = ref<string>("")
const note = ref<string>("")
const merchantId = ref<string>("")
const merchantName = ref<string>("")
const currencyCode = ref<string>("")


if (authUserMerchantsList && authUserMerchantsList.length > 0) {
  merchantId.value = authUserMerchantsList[0].merchant_id || ""
  merchantName.value = authUserMerchantsList[0].merchant_name
  currencyCode.value = authUserMerchantsList[0].currency_code || ""
}

const formatAmount = (value: string) => {
  // Remove all non-digit characters except decimal point for USD
  const numericValue = currencyCode.value === 'USD' 
    ? value.replace(/[^\d.]/g, "")
    : value.replace(/\D/g, "");

  // For USD, handle decimal places
  if (currencyCode.value === 'USD') {
    // Split by decimal point
    const parts = numericValue.split('.');
    // Format the whole number part with commas
    const wholePart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    
    // If there's a decimal part, limit to 2 decimal places
    if (parts.length > 1) {
      const decimalPart = parts[1].slice(0, 2);
      return `${wholePart}.${decimalPart}`;
    }
    return wholePart;
  }
  
  // For non-USD, just add commas as thousand separators
  return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

// Watch for changes in the amount input
watch(amount, (newValue) => {
  if (currencyCode.value === 'USD') {
    // For USD, allow decimal point and up to 2 decimal places
    if (/^\d*\.?\d{0,2}$/.test(newValue.replace(/,/g, ""))) {
      amount.value = formatAmount(newValue);
    } else {
      amount.value = formatAmount(amount.value);
    }
  } else {
    // For non-USD, only allow whole numbers
    if (/^\d*$/.test(newValue.replace(/,/g, ""))) {
      amount.value = formatAmount(newValue);
    } else {
      amount.value = formatAmount(amount.value);
    }
  }
});

// Computed property to check if the amount is valid
const isAmountValid = computed(() => {
  const numericValue = amount.value.replace(/,/g, "");
  if (currencyCode.value === 'USD') {
    // For USD, validate decimal format
    return numericValue.length > 0 && /^\d+\.?\d{0,2}$/.test(numericValue);
  }
  // For non-USD, validate whole number
  return numericValue.length > 0 && /^\d+$/.test(numericValue);
});

const myLink = ref<string>(`${APPCONFIG.APP_ML_HOST}/${authMerchantProfile?.mylinkRouterUri}`)
const qrcode = useQRCode(myLink, {
  errorCorrectionLevel: "Q",
  type: "image/jpeg",
  margin: 0,
})

async function copyLinkUri(input?: string) {
  const link = input ?? myLink.value
  await copy(link)

  toastService.copySuccess({})
}

const downloadQr = () => {
  const refQrCodeDownload = qrCodeDownload.value
  if (!refQrCodeDownload) return

  refQrCodeDownload.downloadQr()
}

async function handlePayment() {
  const paymentAmount = parseFloat(amount.value.replace(/,/g, ''));
  const paymentNote = note.value;

  try {
    const res = await createPayment({
      merchant_id: merchantId.value,
      payment_amount: paymentAmount,
      note: paymentNote
    });
    if (res.status === 200 && res.data?.payment_url) {
      // Redirect to the payment URL
      window.location.href = String(res.data.payment_url);
    } else {
      console.error('Unexpected response format or status:', res);
    }
    console.log('Payment API response:', res);
  } catch (err) {
    console.error('Payment API error:', err);
  }
}

</script>

<template>
  <div class="page-dashboard-header dashboard-header">

    <div class="dashboard-header-content">
      <div class="dashboard-header-content-info">
        <div class="merchant-name"> {{ merchantName }} </div>
      </div>
      <div class="dashboard-header-content-info2">
        <h2>{{ $t("component-create-payment.label") }}</h2>
        <form @submit.prevent="handlePayment">
          <div class="form-row">
            <div class="form-group">
              <label for="amount">{{ $t("component-create-payment.amount-label") }} <span
                  style="color: red;">*</span></label>
              <input type="text" id="amount" v-model="amount" placeholder="20,000,000 VND" required />
            </div>
            <div class="form-group">
              <label for="note">{{ $t("component-create-payment.note-label") }}</label>
              <input type="text" id="note" v-model="note" placeholder="Booking number, invoice number..." />
            </div>
          </div>
          <div class="button-container">
            <button type="submit" :disabled="!isAmountValid">
              <SvgIcon v-if="!isAmountValid" :src="LockIcon" />
              {{ $t("component-create-payment.pay") }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>


</template>

<style lang="scss">
.dashboard-header.page-dashboard-header {
  min-height: 275px;
  padding: 20px 24px 10px 24px;
  background-color: #f5f8ff;
  border-radius: var(--layout-radius-radius-xs, 8px);
  background: var(--OP-Gradient, linear-gradient(105deg, #00A7D7 0.56%, #0F7EBB 49.78%, #1D559F 99.01%));

  .dashboard-header-title {
    font-size: 28px;
    font-weight: 600;
    line-height: 30px;
  }

  .dashboard-header-content {
    margin-top: 15px;
    // display: flex;
    // flex: 1 1;

    .dashboard-header-content-qr-code {
      display: flex;
      width: 800px;
      padding: var(--layout-spacing-spacing-xl, 20px);
      flex-direction: column;
      align-items: flex-start;
      gap: var(--layout-spacing-spacing-xl, 20px);
      border-radius: var(--layout-radius-radius-xs, 8px);
      background: #FFF;
      box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.20);

      
    }

    .dashboard-header-content-info2 {
      margin-bottom: 30px;
      width: 800px;
      height: 200px;
      padding: 20px var(--layout-spacing-spacing-xl, 20px);
      align-items: center;
      gap: var(--layout-spacing-spacing-xxs, 4px);
      border-radius: var(--layout-radius-radius-xs, 8px);
      background: var(--surface-ghost, #FFF);
      box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.20);
      margin: 10px auto;
      .form-row {
          display: flex;
          gap: 15px;
          /* Khoảng cách giữa các ô nhập liệu */
          margin-bottom: 15px;
        }
      
        .form-group {
          flex: 1;
          /* Chia đều không gian cho các ô nhập liệu */
        }
      
        label {
          display: block;
          margin-bottom: 5px;
          font-weight: bold;
        }
      
        input {
          width: 100%;
          padding: 8px;
          box-sizing: border-box;
          border: 1px solid #ccc;
          border-radius: 4px;
        }

        input:focus {
          outline: none;
          border: 1px solid #2E6BE5;
        }
      
        .button-container {
          display: flex;
          justify-content: flex-end;
        }
      
        button {
          display: flex;
          width: 120px;
          padding: var(--layout-spacing-spacing-sm, 10px) var(--layout-spacing-spacing-xl, 20px);
          justify-content: center;
          align-items: center;
          gap: var(--layout-spacing-spacing-xs, 8px);
          padding: 10px 20px;
          border-radius: var(--layout-radius-radius-xxs, 4px);
          background: var(--surface-action, #2E6BE5);
          color: white;
          border: none;
          cursor: pointer;

          &:disabled {
              background-color: #ccc;
              color: var(--text-disabled2, #BABABA);
              text-align: center;
              /* Inter/B2/14_Semibold */
              font-family: var(--Typeface-family-Inter, Inter);
              font-size: var(--Typeface-size-md, 14px);
              font-style: normal;
              font-weight: 600;
              line-height: 20px;
              /* Màu xám khi nút bị disabled */
              cursor: not-allowed;
              width: fit-content;
          }
        }
      
      .left {
        float: left;
        flex: 1;
        justify-content: flex-start;
        width: 50%;

        span {
          width: 98% !important;
        }
      }

      .right {
        float: left;
        flex: 1;
        justify-content: flex-start;
        width: 50%;
        
        span {
          width: 98% !important;
        }
      }
    }

    .dashboard-header-content-info {
      display: flex;
      width: 800px;
      height: 60px;
      padding: 0px var(--layout-spacing-spacing-xl, 20px);
      align-items: center;
      gap: var(--layout-spacing-spacing-xxs, 4px);
      border-radius: var(--layout-radius-radius-xs, 8px);
      background: var(--surface-ghost, #FFF);
      box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.20);
      margin: auto;

      .merchant-name {
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        @include global.text_truncate(1);
      }

      .text-normal {
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        padding-bottom: 8px;
      }

      .my-link {
        height: 36px;
        color: #00bf5f;
        border: 1px solid #8ae2b5;
        background-color: #e6f9ef;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        padding: 8px 16px;
        border-radius: 4px;
        min-width: 416px;

        &:focus {
          outline: unset;
        }
      }

      .action-group {
        display: flex;
        align-items: center;

        .btn-download {
          height: 40px;
          min-width: 131px;
          border-radius: 4px;
          background-color: #2e6be5;
          border: none;
          color: #ffffff;
          font-size: 14px;
          font-weight: 600;
          line-height: 20px;
          padding: 10px 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-right: 16px;
        }

        span {
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
        }

        .one-time-link {
          text-decoration: none;
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          color: #2e6be5;
          cursor: pointer;
        }
      }
    }
  }
}

.copy-position {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .btn-copy {
    background-color: unset;
  }
}

.tooltip-merchantname {
  max-width: 400px;
}
</style>
