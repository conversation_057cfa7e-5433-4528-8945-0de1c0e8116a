<script setup lang="ts">
// import svg
import DashboardTotalRefund from "@/assets/icons/dashboard-total-refund.svg"
import DashboardTotalRevenue from "@/assets/icons/dashboard-total-revenue.svg"
import DropdownArrow from "@/assets/icons/dropdown-arrow.svg"
import AddPlush from "@/assets/icons/icon-add-one-time-link.svg"
import Number from "@/assets/icons/transaction-number-icon-chart.svg"
import Volume from "@/assets/icons/transaction-volume-icon-chart.svg"

import { DashBoardResponse } from "@/apis/dashBoardApis"
import { EnumReportTimeInterval } from "@/apis/reportApi"
import { PermissionEnum } from "@/enums/permissions"
import { useAuthService } from "@/services/authService"
import { useDashBoardService } from "@/services/dashBoardService"
import { useToastService } from "@/services/toastService"
import { formatCurrency, formatNumber } from "@/utils/formatUtil"
import moment from "moment"
import ChartPanel from "primevue/chart"
import { onMounted, ref, watch } from "vue"
import { useI18n } from "vue-i18n"

import { useLayoutService } from "@/services/layoutService"
import { useLocaleService } from "@/services/localeService"
import { isInteger } from "lodash-es"
import { storeToRefs } from "pinia"

const selectedType = ref({ label: "Monthly", value: "month" })
const types = ref([
  { label: "Daily", value: "day" },
  { label: "Weekly", value: "week" },
  { label: "Monthly", value: "month" },
])
const totalRevenue = ref(0)
const totalRefund = ref(0)
const revenueAmount = ref(0)
const refundAmount = ref(0)
const localeService = useLocaleService()

const refChart = ref<InstanceType<typeof ChartPanel> | null>(null)
//const refChart = ref()
const chartData = ref()
const chartOptions = ref()

const monthPicker = ref(true)
const weekPicker = ref(false)

const { t } = useI18n()
const toastService = useToastService()
const { hasPermission } = useAuthService()
const layoutService = useLayoutService()
const { leftMenuCollapsed } = storeToRefs(layoutService)
const { dashBoardTask } = useDashBoardService()
const resultDashBoard = ref<DashBoardResponse>({
  transactionMetrics: [],
  totalPurchaseCount: 0,
  totalAuthorizeCount: 0,
  totalCaptureCount: 0,
  totalVoidRefundCount: 0,
  totalRevenueAmount: 0,
  totalRefundAmount: 0,
  currency: "",
})

watch(selectedType, () => {
  if (selectedType.value.value === "week") {
    weekPicker.value = true
    monthPicker.value = false

    const startDate = moment().startOf("week").toDate()
    const endDate = moment().endOf("week").toDate()

    fetchData(startDate, endDate, EnumReportTimeInterval.Weekly)
  }

  if (selectedType.value.value === "month") {
    monthPicker.value = true
    weekPicker.value = false

    const startDate = moment().startOf("month").toDate()
    const endDate = moment().endOf("month").toDate()

    fetchData(startDate, endDate, EnumReportTimeInterval.Monthly)
  }

  if (selectedType.value.value === "day") {
    weekPicker.value = false
    monthPicker.value = false

    const startDate = moment().startOf("day").toDate()
    const endDate = moment().endOf("day").toDate()

    fetchData(startDate, endDate, EnumReportTimeInterval.Daily)
  }
})

onMounted(() => {
  fetchData()
})

const fetchData = async (startDate?: datetime, endDate?: datetime, timeIntervalSelected?: EnumReportTimeInterval) => {
  const documentStyle = getComputedStyle(document.documentElement)
  const textColorSecondary = documentStyle.getPropertyValue("--text-color-secondary")
  const surfaceBorder = documentStyle.getPropertyValue("--surface-border")

  const res = await dashBoardTask.perform({
    dateFrom: startDate ? startDate : moment().startOf("month").toDate(),
    dateTo: endDate ? endDate : moment().endOf("month").toDate(),
    timeInterval: timeIntervalSelected ? timeIntervalSelected : EnumReportTimeInterval.Monthly,
  })

  resultDashBoard.value = res
  refundAmount.value = res.transactionMetrics[0].Metrics.TotalRefund
  revenueAmount.value = res.transactionMetrics[0].Metrics.TotalRevenue
  totalRevenue.value =
    res.transactionMetrics[0].Metrics.NoOfPurchaseTrans +
    res.transactionMetrics[0].Metrics.NoOfAuthorizeTrans +
    res.transactionMetrics[0].Metrics.NoOfCaptureTrans
  totalRefund.value = res.transactionMetrics[0].Metrics.NoOfRefundVoidTrans

  const labels: string[] = resultDashBoard.value.transactionMetrics
    .map((item) => {
      switch (timeIntervalSelected) {
        case EnumReportTimeInterval.Monthly:
          return moment(item.GroupLabel, "MM-YYYY").format("MMM YY")
        case EnumReportTimeInterval.Weekly: {
          const rangeLabel = item.GroupLabel.split(" - ")
          const weekNum = moment(rangeLabel[0], "DD-MM-YYYY").format("WW")
          return `W${weekNum}`
        }
        case EnumReportTimeInterval.Daily:
          return moment(item.GroupLabel, "DD-MM-YYYY").format("MMM DD")
        default:
          return moment(item.GroupLabel, "MM-YYYY").format("MMM YY")
      }
    })
    .reverse()

  const transactionVolume: number[] = resultDashBoard.value.transactionMetrics
    .map((item) => {
      return item.Metrics.TotalRevenue
    })
    .reverse()

  const transactionNumber: number[] = resultDashBoard.value.transactionMetrics
    .map((item) => {
      return item.Metrics.NoOfPurchaseTrans + item.Metrics.NoOfAuthorizeTrans + item.Metrics.NoOfCaptureTrans
    })
    .reverse()

  chartData.value = {
    labels: labels,
    datasets: [
      {
        type: "line",
        data: transactionNumber,
        backgroundColor: "#F38713",
        borderColor: "#F38713",
        borderWidth: 1,
        fill: false,
        yAxisID: "y1",
      },
      {
        type: "bar",
        backgroundColor: "#DCDCDC",
        data: transactionVolume,
        borderColor: "white",
        borderWidth: 1,
        yAxisID: "y",
        barPercentage: 0.7,
        hoverBackgroundColor: "#BED1F7",
        hoverBorderColor: "#BED1F7",
      },
    ],
  }

  const minNumber = getMinvalueNumber(transactionVolume, transactionNumber)
  const maxNumber = Math.max(...transactionNumber.map((item) => item))
  const stepSizeVolumne = getStepSizeVolume(transactionVolume)
  const stepSizeNumber = getStepSizeNumber(transactionNumber, minNumber)

  chartOptions.value = {
    responsive: true,
    aspectRatio: 3,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            let label = context.dataset.label || ""

            if (label) {
              label += ": "
            }
            if (context.parsed.y !== null) {
              if (context.dataset.yAxisID === "y") {
                label += formatCurrency(context.parsed.y, resultDashBoard.value.currency)
              } else {
                label += formatNumber(context.parsed.y, 0)
              }
            }
            return label
          },
        },
      },
    },
    scales: {
      x: {
        ticks: {
          color: textColorSecondary,
          autoSkip: false,
          maxRotation: 0,
          minRotation: 0,
        },
        grid: {
          drawOnChartArea: false, // only want the grid lines for one axis to show up
          color: surfaceBorder,
        },
        display: true,
        title: {
          display: true,
        },
        border: {
          display: false,
        },
      },
      y: {
        type: "linear",
        ticks: {
          maxTicksLimit: 10,
          stepSize: stepSizeVolumne,
          callback: (value: number) => {
            return value == 0 ? 0 : fomatLableChartVolume(value)
          },
        },
        grid: {
          color: surfaceBorder,
        },
        position: "left",
        beginAtZero: true,
        border: {
          display: false,
          dash: [5],
        },
      },
      y1: {
        type: "linear",
        ticks: {
          maxTicksLimit: 10,
          stepSize: stepSizeNumber,
          callback: (value: number) => {
            if (maxNumber <= 10) return value == 0 ? 0 : isInteger(value) ? Math.ceil(value) : ""
            return value == 0 ? 0 : Math.ceil(value)
          },
        },
        grid: {
          drawOnChartArea: false, // only want the grid lines for one axis to show up
          color: surfaceBorder,
        },
        position: "right",
        beginAtZero: true,
        suggestedMin: minNumber,
        border: {
          display: false,
        },
      },
    },
  }

  //refChart.value?.refresh()
}

const fomatLableChartVolume = (value: number) => {
  const defaultNumber = 10
  const result = Math.ceil(value < 0 ? -value : value)

  if (Math.ceil(result / defaultNumber ** 3).toString().length <= 3) {
    return value < 0 ? `-${Math.ceil(result / defaultNumber ** 3)}K` : `${Math.ceil(result / defaultNumber ** 3)}K`
  } else if (Math.ceil(result / defaultNumber ** 6).toString().length <= 3) {
    return value < 0 ? `-${Math.ceil(result / defaultNumber ** 6)}M` : `${Math.ceil(result / defaultNumber ** 6)}M`
  } else if (Math.ceil(result / defaultNumber ** 9).toString().length <= 3) {
    return value < 0 ? `-${Math.ceil(result / defaultNumber ** 9)}B` : `${Math.ceil(result / defaultNumber ** 9)}B`
  } else if (Math.ceil(result / defaultNumber ** 12).toString().length <= 3) {
    return value < 0 ? `-${Math.ceil(result / defaultNumber ** 12)}T` : `${Math.ceil(result / defaultNumber ** 12)}T`
  }
}

const getStepSizeVolume = (arrayVolume: number[]): number => {
  const checkNegativeValue = arrayVolume.some((value) => {
    return 10
  })

  if (checkNegativeValue) {
    var minValue = Math.min(...arrayVolume.map((item) => item))
    const maxValue = Math.max(...arrayVolume.map((item) => item))

    if (minValue > 0) minValue = 0
    var total = maxValue + Math.abs(minValue)

    var stepSize = total / 10
    return stepSize
  } else {
    return 10
  }
}

const getMinvalueNumber = (arrayVolume: number[], arrayNumber: number[]): number => {
  const checkNegativeValue = arrayVolume.some((value) => {
    return value < 0
  })
  const minVol = Math.min(...arrayVolume.map((item) => item))
  const maxVol = Math.max(...arrayVolume.map((item) => item))

  if (checkNegativeValue && minVol < 0) {
    var maxVal = Math.max(...arrayNumber.map((item) => item))
    return (minVol / maxVol) * maxVal
  } else {
    return 0
  }
}
const getStepSizeNumber = (arrayNumber: number[], minNumber: number): number => {
  const checkNegativeValue = arrayNumber.some((value) => {
    return 10
  })

  if (checkNegativeValue) {
    const maxValue = Math.max(...arrayNumber.map((item) => item))
    var total = maxValue + Math.abs(minNumber)
    var stepSize = total / 10
    return stepSize
  } else {
    return 10
  }
}

const emitMonth = (date: number) => {
  const selectedMonth = moment().month(date).format("MMM")
  const startDate = moment(selectedMonth, "MMM").startOf("month").toDate()
  const endDate = moment(selectedMonth, "MMM").endOf("month").toDate()

  fetchData(startDate, endDate, EnumReportTimeInterval.Monthly)
}

const emitWeek = (date: any) => {
  const weekNumber = moment(date).week()
  const year = moment(date).year()

  const startDate = moment().day("Sunday").year(year).week(weekNumber).toDate()
  const endDate = moment(startDate).add(6, "days").toDate()

  fetchData(startDate, endDate, EnumReportTimeInterval.Weekly)
}

const emitDay = (date: any) => {
  const startDate = moment(date).startOf("day").toDate()
  const endDate = moment(date).endOf("day").toDate()

  fetchData(startDate, endDate, EnumReportTimeInterval.Daily)
}

const refDialogOneTime = ref()
const creatOneTimeLink = () => {
  if (!hasPermission(PermissionEnum.CreateLink)) {
    toastService.error({
      summary: t("common.toast.action-not-permission"),
    })
    return
  }

  refDialogOneTime.value.show()
}

watch(leftMenuCollapsed, () => {
  refChart.value?.reinit()
})
</script>

<template>
  <div class="tms-dashboard-body tms-page-dashboard-body">
    <div class="d-flex">
      <div class="border-end tms-page-dashboard-body-left">
        <div class="tms-inputgroup">
          <InputGroup>
            <Dropdown
              v-model="selectedType"
              :options="types"
              option-label="label"
              panel-class="dropdown-swich-type-date"
            >
              <template #dropdownicon>
                <!-- <DropdownArrow /> -->
                <SvgIcon :src="DropdownArrow" />
              </template>
            </Dropdown>

            <TmsMonthPicker v-if="monthPicker" @auto-apply="emitMonth" />

            <TmsWeekPicker v-else-if="weekPicker" @auto-apply="emitWeek" />

            <TmsDatePicker v-else @auto-apply="emitDay" />
          </InputGroup>
        </div>

        <div class="statistics">
          <div class="total-revenue">
            <div class="total-revenue-icon">
              <!-- <DashboardTotalRevenue /> -->
              <SvgIcon :src="DashboardTotalRevenue" />
            </div>

            <div class="total-revenue-info">
              <div class="title">
                {{ $t("page-Dashboard.total-revenue") }}
              </div>
              <div class="amount">
                {{ formatCurrency(revenueAmount, resultDashBoard.currency) }}
              </div>
              <div class="transactions">
                {{ `${totalRevenue} ${$t("page-Dashboard.transactions")}` }}
              </div>
            </div>
          </div>

          <div class="total-refund">
            <div class="total-refund-icon">
              <!-- <DashboardTotalRefund /> -->
              <SvgIcon :src="DashboardTotalRefund" />
            </div>

            <div class="total-refund-info">
              <div class="title">
                {{ $t("page-Dashboard.total-refund") }}
              </div>
              <div class="amount">
                {{ formatCurrency(refundAmount, resultDashBoard.currency) }}
              </div>
              <div class="transactions">
                {{ `${totalRefund} ${$t("page-Dashboard.transactions")}` }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex-fill">
        <div class="chart">
          <div class="title">
            {{ $t("page-Dashboard.revenue-trend") }}
          </div>

          <div class="custom-chart">
            <Chart ref="refChart" type="bar" :data="chartData" :options="chartOptions" class="w-100" />

            <div class="chart-legend">
              <Button class="btn-transaction transaction-volume">
                <SvgIcon :src="Volume"></SvgIcon>
                <span class="label">{{ $t("page-Dashboard.transaction-volume") }}</span>
              </Button>

              <Button class="btn-transaction transaction-number">
                <SvgIcon :src="Number"></SvgIcon>
                <span class="label">{{ $t("page-Dashboard.transaction-number") }}</span>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="group-chart"></div>
  </div>

  <div class="position-one-time-link">
    <Button
      v-tooltip.top="{
        value: !hasPermission(PermissionEnum.CreateLink) ? $t('common.tooltip.action-not-permission') : '',
      }"
      :class="{
        'no-permission': !hasPermission(PermissionEnum.DoRefund),
        'btn-create-one-time-link-en': localeService.locale === 'en',
        'btn-create-one-time-link-vi': localeService.locale === 'vi',
      }"
      :disabled="!hasPermission(PermissionEnum.DoRefund)"
      class="btn-create-one-time-link"
      @click="creatOneTimeLink"
    >
      <SvgIcon :src="AddPlush"></SvgIcon>
      <div class="last-wrapper text-truncate">
        {{ $t("page-Dashboard.add-link") }}
      </div>
    </Button>
  </div>

  <Teleport to="body">
    <div class="dashboard-loading">
      <AppLoading v-if="dashBoardTask.isRunning"></AppLoading>
    </div>
  </Teleport>

  <Lazy>
    <OneTimeLink ref="refDialogOneTime"></OneTimeLink>
  </Lazy>
</template>

<style lang="scss">
.tms-dashboard-body.tms-page-dashboard-body {
  padding: 16px 24px 0 24px;
  @media (min-width: 1441px) {
    padding-top: 24px;
  }

  .tms-page-dashboard-body-left {
    min-width: 355px; // TODO: ko biết nên xóa ko nên thôi để min
  }

  .tms-inputgroup {
    .p-inputgroup {
      width: 330px;
      height: 36px;

      .p-dropdown {
        width: 120px;
        border-radius: 4px 0 0 4px;
        border: 1px solid #dcdcdc;
        padding: 8px 16px;

        span.p-dropdown-label {
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          padding: 0;
        }

        .p-dropdown-trigger {
          width: 20px;
          height: 20px;
        }
      }

      .p-focus {
        outline: 1px solid #2e6be5;
      }

      .dp__main {
        min-width: 210px;

        .dp__input_wrap {
          height: 36px;

          .input-value {
            position: relative;

            input {
              border-radius: 0 4px 4px 0;
              border: 1px solid #dcdcdc;
              border-left: none;
              padding: 8px 16px;
              box-shadow: none;
              height: 36px;
              width: 100%;
              font-family: Inter;
              font-size: 14px;
              font-weight: 400;
              line-height: 20px;
              text-align: left;
              padding-left: 16px;

              &:focus {
                outline: none;
              }

              &::placeholder {
                color: #bababa;
                opacity: 1;
                /* Firefox */
              }

              &::-ms-input-placeholder {
                /* Edge 12-18 */
                color: #bababa;
              }
            }

            &-icon {
              height: 20px;
              cursor: pointer;
              position: absolute;
              top: 50%;
              right: 16px;
              transform: translateY(-50%);

              svg {
                vertical-align: unset;
              }
            }
          }
        }
      }
    }
  }

  .statistics {
    min-width: 335px;

    .total-revenue {
      display: flex;
      min-height: 120px;
      padding: 15px 0;
      padding-right: 15px;
      align-items: center;

      .total-revenue-icon {
        width: 64px;
        height: 64px;
        border-radius: 100px;
        padding: 16px;
        background-color: #f5f5f5;
      }

      .total-revenue-info {
        margin-left: 20px;

        .title {
          font-size: 14px;
          font-weight: 500;
          line-height: 20px;
        }

        .amount {
          font-size: 24px;
          font-weight: 400;
          line-height: 36px;

          white-space: nowrap;
        }

        .transactions {
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          color: #6c6c6c;
        }
      }
    }

    .total-refund {
      display: flex;
      min-height: 120px;
      padding: 15px 0;
      align-items: center;

      .total-refund-icon {
        width: 64px;
        height: 64px;
        border-radius: 100px;
        padding: 16px;
        background-color: #f5f5f5;
      }

      .total-refund-info {
        margin-left: 20px;

        .title {
          font-size: 14px;
          font-weight: 500;
          line-height: 20px;
        }

        .amount {
          font-size: 24px;
          font-weight: 400;
          line-height: 36px;
        }

        .transactions {
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          color: #6c6c6c;
        }
      }
    }
  }

  .chart {
    width: 100%;
    padding-left: 15px;
    @media (min-width: 1441px) {
      padding-left: 25px;
      padding-right: 30px;
    }
    .title {
      font-size: 16px;
      font-weight: 600;
      line-height: 20px;
    }

    .custom-chart {
      margin-top: 16px;

      .p-chart {
        width: 100%;
      }
    }
  }
  .group-chart {
    display: flex;
    flex: 1 1;
    margin-top: 20px;
  }
}

.dropdown-swich-type-date {
  margin-top: 1px !important;
  height: 140px;
  width: 120px;
  border-radius: 8px;
  box-shadow: 0px 4px 5px 0px #00000033;

  .p-dropdown-items-wrapper {
    overflow: hidden;

    ul.p-dropdown-items {
      padding: 0 24px;

      li.p-dropdown-item {
        padding: 20px 0 0 0;
        margin: 0;

        span.p-dropdown-item-label {
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
        }
      }

      .p-highlight {
        background-color: #ffffff;
        color: #404040;
      }

      .p-focus {
        background-color: #ffffff;
        color: #404040;
      }
    }
  }
}

.dashboard-loading {
  .component-loading {
    position: fixed;
    top: 0;
    z-index: 9999;
    background-color: black;
    opacity: 0.75;
  }
}

.position-one-time-link {
  position: absolute;
  right: 24px;
  bottom: 40px;
  height: 56px;
  padding: 4px;
  border-radius: 100px;

  &:hover {
    box-shadow: 0px 0px 24px 0px #2e6be566;
  }

  .last-wrapper {
    max-width: 0;
    font-weight: 600;
    font-size: 16px;
  }

  .btn-create-one-time-link {
    height: 3rem;
    width: 3rem;
    border-radius: 1.5rem; // 1/2 of height
    padding: 0.75rem; // 1/4 of height
    transition: width 0.5s;
    transition-timing-function: ease;

    //#region Button full style
    width: auto;
    justify-content: space-between;
    .last-wrapper {
      max-width: none;
      padding-left: 0.5rem;
    }
    padding: 0.625rem 1.25rem;
    //#endregion

    // &.btn-create-one-time-link-en {
    //   &:hover {
    //     width: 139px;
    //     padding: 12px 20px;
    //     justify-content: space-between;

    //     .last-wrapper {
    //       max-width: none;
    //     }
    //   }
    // }

    // &.btn-create-one-time-link-vi {
    //   &:hover {
    //     width: 145px;
    //     padding: 12px 20px;
    //     justify-content: space-between;

    //     .last-wrapper {
    //       max-width: none;
    //     }
    //   }
    // }
  }
}

.chart-legend {
  max-width: 420px;
  display: flex;
  align-items: center;

  .btn-transaction {
    background-color: #ffffff;
    color: #646464;
    font-size: 12px;
    padding: 0;
    cursor: unset;

    .label {
      margin-left: 4px;
    }
  }

  .transaction-volume {
    margin-right: 24px;
  }
}
</style>
