<script setup lang="ts">
import { useNavigatorService } from "@/services/navigatorService";
import { storeToRefs } from "pinia";

const props = withDefaults(
  defineProps<{
    name?: string
    max?: string | number | undefined
  }>(),
  {
    name: undefined,
    max: undefined
  }
)

var navigatorService = useNavigatorService()
const { getOrSetComponentKey } = navigatorService
const { listComponentInclude, listComponentExclude } = storeToRefs(navigatorService)

// Nếu sử dụng <component :key="key"></component> thì KeepAlive vẫn lưu cache các component trước đó của nó -> memory leak
</script>
<template>
  <RouterView v-slot="{ Component, route }">
    <div class="d-none"></div>
    <Suspense>
      <!-- START: main of page -->
      <template #default>
        <KeepAlive :max="props.max" :exclude="listComponentExclude">
          <component
            :is="Component"
            v-if="Component"
            :key="getOrSetComponentKey(Component, route, { routerViewName: props.name })"
          ></component>
        </KeepAlive>
      </template>
      <!-- END: main of page -->

      <!-- START: loading state -->
      <template #fallback>
        <div class="w-100 h-100 align-self-center d-flex justify-items-center">
          <!-- View loading -->
          <AppProgressSpinner></AppProgressSpinner>
        </div>
      </template>
      <!-- END: loading state -->
    </Suspense>
  </RouterView>
</template>
