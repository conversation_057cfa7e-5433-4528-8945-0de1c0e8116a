import PrimeVue, { PrimeVueConfiguration, PrimeVuePTOptions } from "primevue/config"
import <PERSON><PERSON><PERSON><PERSON> from "primevue/iconfield"
import InputIcon from "primevue/inputicon"
import { usePassThrough } from "primevue/passthrough"
import Tooltip from "primevue/tooltip"
import InputGroup from "primevue/inputgroup"
import Toast from "primevue/toast"
import ToastService from "primevue/toastservice"
import ConfirmPopup from "primevue/confirmpopup"
import ConfirmationService from "primevue/confirmationservice"
import { App, ObjectPlugin } from "vue"
import InputGroupAddon from "primevue/inputgroupaddon"
import ToggleSwitch from "primevue/inputswitch"
import FloatLabel from "primevue/floatlabel"


/**
 * Plugin thực hiện đăng ký vào app instance.
 * Called by `app.use(primeVue)`
 */
export const primeVue: ObjectPlugin<undefined[]> = {
  install(app: App, options?: undefined): void {


  const CustomPreset = usePassThrough(
    {
      panel: {
        root: {
          class: ["panel-tms"],
        },
      },
      inputtext: {
        root: {
          class: ["p-inputtext-tms"],
        },
      },
      button: {
        root: {
          class: ["p-button-tms"],
        },
      },
      iconfield: {
        root: {
          class: ["p-icon-field-tms"],
        },
      },
      datatable: {
        root: {
          class: ["p-datatable-tms"],
        },
      },
      // datatable: () => ({
      //   props: {
      //     rowsPerPageOptions: [5, 10, 20, 100] // Vẫn đang là kế hoạch https://github.com/primefaces/primevue/issues/4607
      //   }
      // }),
      paginator: {
        root: {
          class: ["p-paginator-tms"],
        },
      },
      dropdown: {
        root: {
          class: ["p-dropdown-tms"],
        },
      },
      toast: {
        root: {
          class: ["p-toast-tms"],
        },
      },
    } as PrimeVuePTOptions,
    {
      mergeSections: true,
      mergeProps: false,
    }
  )

  app
    .use(PrimeVue, {
      pt: CustomPreset,
    } as PrimeVueConfiguration)
    .use(ToastService)
    .use(ConfirmationService)
    .directive("tooltip", Tooltip)
    .component("IconField", IconField)
    .component("InputIcon", InputIcon)
    .component("InputGroup", InputGroup)
    .component("InputGroupAddon", InputGroupAddon)
    .component("Toast", Toast)
    .component("ConfirmPopup", ConfirmPopup)
    .component("ToggleSwitch", ToggleSwitch)
    .component("FloatLabel", FloatLabel)

    // app.use(PrimeVue, {
    //   unstyled: true, // True: ko áp dụng PrimeVue style,
    //   pt: {
    //     datatable: {
    //       wrapper: "table-responsive",
    //       table: "table table-bordered table-striped table-hover",
    //     },
    //     button: {
    //       root: {
    //         class: "bg-teal-500 hover:bg-teal-700 cursor-pointer text-white p-3 border-round border-none flex gap-2",
    //       },
    //       label: "text-white font-bold text-xl", // OR { class: 'text-white font-bold text-xl' }
    //       icon: "text-white text-2xl",
    //     },
    //     panel: {
    //       header: "bg-primary border-primary",
    //       content: "border-primary text-lg text-primary-700",
    //       title: "bg-primary text-xl",
    //       toggler: "bg-primary hover:bg-primary-reverse",
    //     },
    //   },
    // } as PrimeVueConfiguration)
  },
}
