import { App, DefineComponent, ObjectPlugin } from "vue"

export interface LayoutsOptions {}

/**
 * Plugin thực hiện đăng ký tự động tất cả layouts vào app instance.
 * Called by `app.use(layouts)`
 */
export const layouts: ObjectPlugin<LayoutsOptions> = {
  /**
   * Called by `app.use(layouts)`
   * @param app - Application that uses the router
   * @param options
   */
  install(app: App, options: LayoutsOptions) {
    // Quét và lấy tất cả layout trong /layouts/*.vue
    // Đọc https://vitejs.dev/guide/features#glob-import
    const layouts: Record<string, DefineComponent> = import.meta.glob(["./*.vue", "@layouts/*.vue"], {
      eager: true,
    })
    // console.log("Layouts is registed: ", layouts)

    Object.entries(layouts).forEach(([, layout]) => {
      // Đăng ký global component vào app instance.
      app.component(layout?.default?.name, layout?.default)
    })
  },
}
