import { IAppCookies, RemoveCookiesOptions, SetCookiesOptions } from "@/types/cookies"
import { App, ObjectPlugin } from "vue"
import VueCookies, { useCookies as useVueCookies } from "vue3-cookies"
import type { CookiesConfig } from "vue3-cookies/dist/interfaces"

export class AppCookies implements IAppCookies {
  private cookies: any // vue3-cookies

  constructor(cookies: any) {
    // Inject các giá trị từ các plugin khác nhau
    this.cookies = cookies
  }

  // constructor() {
  //   // Inject các giá trị từ các plugin khác nhau
  //   this.cookies = useCookies()
  // }

  get(keyName: string): string {
    return this.cookies.get(keyName)
  }

  set(keyName: string, value: string, options?: SetCookiesOptions): void {
    this.cookies.set(
      keyName,
      value,
      options?.expireTimes,
      options?.path,
      options?.domain,
      options?.secure,
      options?.sameSite
    )
  }

  remove(keyName: string, options?: RemoveCookiesOptions): boolean {
    return this.cookies.remove(keyName, options?.path, options?.domain)
  }

  hasKey(keyName: string): boolean {
    return this.cookies.isKey(keyName)
  }

  keys(): string[] {
    return this.cookies.keys()
  }
}

/**
 * Plugin thực hiện đăng ký tự động tất cả layouts vào app instance.
 * Called by `app.use(cookies)`
 */
export const cookies: ObjectPlugin<CookiesConfig[]> = {
  /**
   * Called by `app.use(cookies)`
   * @param app - Application that uses the router
   * @param options
   */
  install(app: App, options?: CookiesConfig): void {
    const pluginConfig: CookiesConfig = {
      expireTimes: options?.expireTimes ?? "31d",
      path: options?.path ?? "/",
      domain: options?.domain ?? "",
      secure: options?.secure ?? true,
      sameSite: options?.sameSite ?? "None",
    }
    VueCookies.install(app, pluginConfig)

    //console.log(app.config.globalProperties.$cookies) 2 thằng này same nhau
    //console.log(useVueCookies().cookies) 2 thằng này same nhau
  },
}

/**
 * Có thể sử dụng ngay lập tức ở global
 */
export function useGlobalCookies(): IAppCookies {
  return new AppCookies(useVueCookies().cookies)
}
