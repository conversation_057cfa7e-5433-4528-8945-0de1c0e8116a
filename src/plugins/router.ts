import { App, ObjectPlugin } from "vue"

import appRouter from "../routers/router.ts"

/**
 * Plugin thực hiện đăng ký tự động tất cả layouts vào app instance.
 * Called by `app.use(routers)`
 */
export const router: ObjectPlugin<unknown[]> = {
  /**
   * Called by `app.use(routers)`
   * @param app - Application that uses this plugin
   * @param options
   */
  install(app: App, options?: unknown): void {
    appRouter.install(app)
  },
}

export function useGlobalRouter() {
  return appRouter
}
