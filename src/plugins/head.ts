import APPCONFIG from "@/appConfig"
import { VueHeadMixin, createHead, useHead } from "@unhead/vue"
import { App, ObjectPlugin } from "vue"

/**
 * Plugin thực hiện đăng ký vào app instance.
 * Called by `app.use(htmlHead)`
 * 
 * ```
<script>
  export default {
    head() {
      return {
        title: 'Hello World'
      }
    }
  }
</script>
 * ``` 
 */
export const head: ObjectPlugin<undefined[]> = {
  install(app: App, options?: undefined): void {
    const head = createHead()

    app.use(head)
    
    app.mixin(VueHeadMixin)
  },
}

export { useHead }
