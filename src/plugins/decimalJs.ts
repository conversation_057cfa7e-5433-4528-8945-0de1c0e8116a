import Decimal from "decimal.js"
import type { App, ObjectPlugin } from "vue"

/**
 * Plugin thực hiện đăng ký sử dụng decimalJs vào app instance.
 * Called by `app.use(decimalJs)`
 */
export const decimalJs: ObjectPlugin<unknown[]> = {
  /**
   * Called by `app.use(jQuery)`
   * @param app - Application that uses this plugin
   * @param options
   */
  install(app: App, options?: unknown): void {
    Object.assign(window, { Decimal: Decimal })
  },
}