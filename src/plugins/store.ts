import { createPinia } from "pinia"
import { App, ObjectPlugin } from "vue"

export const pinia = createPinia()

/**
 * Plugin thực hiện đăng ký tự động tất cả layouts vào app instance.
 * Called by `app.use(locales)`
 */
export const stores: ObjectPlugin<unknown[]> = {
  /**
   * Called by `app.use(stores)`
   * @param app - Application that uses this plugin
   * @param options
   */
  install(app: App, options?: unknown): void {
    pinia.install(app)
  },
}
