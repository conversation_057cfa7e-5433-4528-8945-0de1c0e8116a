import jQueryPlugin from "jquery"
import type { App, ObjectPlugin } from "vue"

/**
 * Plugin thực hiện đăng ký sử dụng jQuery vào app instance.
 * Called by `app.use(jQuery)`
 */
export const jQuery: ObjectPlugin<unknown[]> = {
  /**
   * Called by `app.use(jQuery)`
   * @param app - Application that uses this plugin
   * @param options
   */
  install(app: App, options?: unknown): void {
    Object.assign(window, { $: jQueryPlugin, jQuery: jQueryPlugin })
    app.config.globalProperties.$jQuery = jQueryPlugin
  }
}

// https://vuejs.org/guide/typescript/options-api.html#augmenting-global-properties
declare module 'vue' {
  interface ComponentCustomProperties {
    $jQuery: JQueryStatic
  }
}
