import APPCONFIG from "@/appConfig"
import { App, ObjectPlugin } from "vue"
import type { LogEvent, LoggerHook } from "vue-logger-plugin"
import {
  StringifyObjectsHook,
  VueLogger,
  createLogger as createVueLogger,
  use<PERSON>og<PERSON> as useVueLogger,
} from "vue-logger-plugin"
//import axios from 'axios'

// https://github.com/dev-tavern/vue-logger-plugin
// https://dev.to/programequity/how-to-implement-a-full-stack-logging-solution-for-a-vuejs-and-nodejs-app-5bj3

const ServerLogHook: LoggerHook = {
  async run(event: LogEvent) {
    //await axios.post('/log', { severity: event.level, data: event.argumentArray })
  },
}

const LogLevel = APPCONFIG.MODE_PROD ? "warn" : !APPCONFIG.MODE_DEV ? "info" : "debug"

const _logger = createVueLogger({
  callerInfo: import.meta.env.DEV, // Whether information about the caller function should be included (Default: false)
  level: LogLevel, // The logging level: log <-- error <-- warn <-- info <-- debug (Default: debug)
  // ... (other options)
  beforeHooks: [StringifyObjectsHook],
  afterHooks: [ServerLogHook],
})

/**
 * Plugin thực hiện đăng ký sử dụng jQuery vào app instance.
 * Called by `app.use(jQuery)`
 */
export const logger: ObjectPlugin<unknown[]> = {
  /**
   * Called by `app.use(jQuery)`
   * @param app - Application that uses this plugin
   * @param options
   */
  install(app: App, options?: unknown): void {
    _logger.install(app)
  },
}

export function useGlobalLogger(): VueLogger {
  return _logger
}

export function useLogger(): VueLogger {
  return useVueLogger()
}
