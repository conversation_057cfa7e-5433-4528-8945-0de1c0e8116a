import APPCONFIG from "@/appConfig"
import { AppLocaleNames } from "@/enums/locales"
import en from "@locales/en.json"
import vi from "@locales/vi.json"
import { createI18n } from "vue-i18n"

export const LOCALES = [
  { value: AppLocaleNames.EN, caption: "English" },
  { value: AppLocaleNames.VI, caption: "Việt Nam" },
]

export const DefaultLocale = APPCONFIG.DETAUL_LOCALE || AppLocaleNames.EN

export const messages = {
  [AppLocaleNames.EN]: en,
  [AppLocaleNames.VI]: vi,
}

// Type-define 'en' as the master schema for the resource
export type MessageSchema = (typeof messages)[AppLocaleNames.EN]

// define number format schema
export type NumberSchema = {
  currency: {
    style: "currency"
    currencyDisplay: "symbol"
    currency: string
  }
}

// Global Scope
export const i18n = createI18n<[MessageSchema], "en" | "vi" | string, false>({
  locale: DefaultLocale, // set current locale
  fallbackLocale: DefaultLocale, // set fallback locale
  messages,
  legacy: false, // <--- Setting the legacy parameter = false is very important to ensuring that the i18n plugin properly works with Vue 3 Composition API.
  globalInjection: true, // <--- Set true to allow use global function ex: {{ $t("hello") }}
})

export type I18nGlobal = typeof i18n.global

// khác với useI18n() chỉ sử dụng ở setup, useI18nGlobal() có thể sử dụng ở mọi vị trí trong app (vd: service)
export function useGlobalI18n(): I18nGlobal {
  return i18n.global
}

// /**
//  * Plugin thực hiện đăng ký tự động tất cả layouts vào app instance.
//  * Called by `app.use(cookies)`
//  */
// export const locales: ObjectPlugin<undefined[]> = {
//   /**
//    * Called by `app.use(cookies)`
//    * @param app - Application that uses the router
//    * @param options
//    */
//   install(app: App, options?: undefined): void {

//     app.runWithContext(() => {
//       console.log("Vue app runWithContext", getCurrentInstance())
//       // i18n useI18n sử dụng getCurrentInstance do đó vẫn phải gọi trong setup
//     })
//   },
// }