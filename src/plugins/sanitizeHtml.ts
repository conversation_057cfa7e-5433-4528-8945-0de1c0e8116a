//import sanitizeHtml, { IOptions } from "sanitize-html"
import xss, {IFilterXSSOptions} from "xss"
import type { App, ObjectPlugin, Directive, FunctionDirective } from "vue"

/**
 * Sử dụng lib "sanitize-html" có vấn đề là nó quá to (https://github.com/vuejs/vue/issues/6333#issuecomment-596753899)
 */

/**
 * Plugin thực hiện đăng ký tự động tất cả layouts vào app instance.
 * Called by `app.use(plugin)`
 */
export const sanitizeHtmlPlugin: ObjectPlugin<IFilterXSSOptions[]> = {
  /**
   * Called by `app.use(stores)`
   * @param app - Application that uses this plugin
   * @param options
   */
  install(app: App, options?: IFilterXSSOptions): void {
    const defaultOptions = { ...options } as IFilterXSSOptions

    const sanitizeFn = (dirty: string, options?: IFilterXSSOptions): string => {
      return xss(dirty, { ...defaultOptions, ...options })
    }

    const sanitizeDirective: FunctionDirective<HTMLElement, string> = (el, binding) => {
      const sanitizeValue = sanitizeFn(binding.value)

      if (typeof el.innerHTML === "string") {
        // we're client-side and `el` is an HTMLElement
        el.innerHTML = sanitizeValue
      } else {
        // we're server-side and `el` is a VNode
        // see https://vuejs.org/v2/guide/security.html#Injecting-HTML
        throw Error("server-side is not supported currently.")
      }
    }

    app.directive("html-safe", sanitizeDirective)

    app.config.globalProperties.$sanitize = sanitizeFn
    app.config.globalProperties.$htmlSafe = sanitizeFn
  },
}

import { DefineComponent } from "vue"

// https://vuejs.org/guide/typescript/options-api.html#augmenting-global-properties
declare module "vue" {
//declare module "@vue/runtime-core" {
  interface ComponentCustomProperties {
    $sanitize: (dirty: string, options?: IFilterXSSOptions) => string
    $htmlSafe: (dirty: string, options?: IFilterXSSOptions) => string
    vHtmlSafe: Directive<HTMLElement, string>
  }
}