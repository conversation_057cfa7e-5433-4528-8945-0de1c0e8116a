// The simple version of lib https://www.npmjs.com/package/linq-to-typescript
// Only use for Array. Hi<PERSON><PERSON> suất cao cho các tập dữ liệu nhỏ đến trung bình.
// WARN: Nếu ko dùng lib này thì comment lại đoạn declare global để tránh thừa khai báo ghi đè kiểu dữ liệu

// TODO: XỬ LÝ execute luôn
initializeLinqExtensions()

declare global {
  interface Array<T> {
    all(predicate: (x: T) => boolean): boolean
    allAsync(predicate: (x: T) => Promise<boolean>): Promise<boolean>
    any(predicate?: (x: T) => boolean): boolean
    anyAsync(predicate?: (x: T) => Promise<boolean>): Promise<boolean>
    count(predicate?: (x: T) => boolean): number
    countAsync(predicate?: (x: T) => Promise<boolean>): Promise<number>
    isEmpty: () => boolean
    clear: () => void
    update: (data: T[]) => T[]
    firstOrDefault(predicate?: (x: T) => boolean): T | undefined
    firstOrDefaultAsync(predicate?: (x: T) => Promise<boolean>): Promise<T | undefined>
    lastOrDefault(predicate?: (x: T) => boolean): T | undefined
    lastOrDefaultAsync(predicate?: (x: T) => Promise<boolean>): Promise<T | undefined>
    removeDuplicate: () => T[]
    removeDuplicateKeepFirstByKey: (getKeyCallback: GetKeyCallbackFn<T>) => T[]
    removeDuplicateKeepLastByKey: (getKeyCallback: GetKeyCallbackFn<T>) => T[]
  }
  type GetKeyCallbackFn<T> = (item: T) => string
}

export default function initializeLinqExtensions(): void {
  Array.prototype.all = function <TSource>(predicate: (x: TSource) => boolean): boolean {
    return all(this, predicate)
  }
  Array.prototype.allAsync = function <TSource>(predicate: (x: TSource) => Promise<boolean>): Promise<boolean> {
    return allAsync(this, predicate)
  }

  Array.prototype.any = function <TSource>(predicate?: (x: TSource) => boolean): boolean {
    return any(this, predicate)
  }
  Array.prototype.anyAsync = function <TSource>(predicate?: (x: TSource) => Promise<boolean>): Promise<boolean> {
    return anyAsync(this, predicate)
  }

  Array.prototype.count = function <TSource>(predicate?: (x: TSource) => boolean): number {
    return count(this, predicate)
  }
  Array.prototype.countAsync = function <TSource>(predicate?: (x: TSource) => Promise<boolean>): Promise<number> {
    return countAsync(this, predicate)
  }

  Array.prototype.isEmpty = function (): boolean {
    return isEmpty(this)
  }

  Array.prototype.clear = function (): void {
    return clear(this)
  }

  Array.prototype.update = function <TSource>(data: TSource[]): TSource[] {
    return update(this, data)
  }

  Array.prototype.firstOrDefault = function <TSource>(predicate?: (x: TSource) => boolean): TSource | undefined {
    return firstOrDefault(this, predicate)
  }
  Array.prototype.firstOrDefaultAsync = function <TSource>(
    predicate?: (x: TSource) => Promise<boolean>
  ): Promise<TSource | undefined> {
    return firstOrDefaultAsync(this, predicate)
  }

  Array.prototype.lastOrDefault = function <TSource>(predicate?: (x: TSource) => boolean): TSource | undefined {
    return lastOrDefault(this, predicate)
  }
  Array.prototype.lastOrDefaultAsync = function <TSource>(
    predicate?: (x: TSource) => Promise<boolean>
  ): Promise<TSource | undefined> {
    return lastOrDefaultAsync(this, predicate)
  }

  Array.prototype.removeDuplicate = function <TSource>(): TSource[] {
    return removeDuplicate<TSource>(this)
  }

  Array.prototype.removeDuplicateKeepFirstByKey = function <TSource>(
    getKeyCallback: GetKeyCallbackFn<TSource>
  ): TSource[] {
    return removeDuplicateKeepFirstByKey<TSource>(this, getKeyCallback)
  }

  Array.prototype.removeDuplicateKeepLastByKey = function <TSource>(
    getKeyCallback: GetKeyCallbackFn<TSource>
  ): TSource[] {
    return removeDuplicateKeepLastByKey<TSource>(this, getKeyCallback)
  }
}

export function from<TSource>(source: TSource[]) {
  return {
    all(predicate: (x: TSource) => boolean): boolean {
      return all(source, predicate)
    },
    allAsync(predicate: (x: TSource) => Promise<boolean>): Promise<boolean> {
      return allAsync(source, predicate)
    },

    any(predicate?: (x: TSource) => boolean): boolean {
      return any(source, predicate)
    },
    anyAsync(predicate?: (x: TSource) => Promise<boolean>): Promise<boolean> {
      return anyAsync(source, predicate)
    },

    count(predicate?: (x: TSource) => boolean): number {
      return count(source, predicate)
    },
    countAsync(predicate?: (x: TSource) => Promise<boolean>): Promise<number> {
      return countAsync(source, predicate)
    },

    isEmpty(): boolean {
      return isEmpty(source)
    },

    clear(): void {
      return clear(source)
    },
    update(data: TSource[]): TSource[] {
      return update(source, data)
    },

    firstOrDefault(predicate?: (x: TSource) => boolean): TSource | undefined {
      return firstOrDefault(source, predicate)
    },
    firstOrDefaultAsync(predicate?: (x: TSource) => Promise<boolean>): Promise<TSource | undefined> {
      return firstOrDefaultAsync(source, predicate)
    },

    removeDuplicate(): TSource[] {
      return removeDuplicate(source)
    },
    removeDuplicateKeepFirstByKey(getKeyCallback: GetKeyCallbackFn<TSource>): TSource[] {
      return removeDuplicateKeepFirstByKey(source, getKeyCallback)
    },
    removeDuplicateKeepLastByKey(getKeyCallback: GetKeyCallbackFn<TSource>): TSource[] {
      return removeDuplicateKeepLastByKey(source, getKeyCallback)
    },
  }
}

export function all<TSource>(source: TSource[], predicate: (x: TSource) => boolean): boolean {
  if (predicate) {
    for (const item of source) {
      if (predicate(item) === false) {
        return false
      }
    }
  }
  return true
}

export async function allAsync<TSource>(
  source: TSource[],
  predicate: (x: TSource) => Promise<boolean>
): Promise<boolean> {
  if (predicate) {
    for (const item of source) {
      if ((await predicate(item)) === false) {
        return false
      }
    }
  }
  return true
}

//type FunctionOrPromise<TSource> = ((x: TSource) => boolean) | ((x: TSource) => Promise<boolean>);
//type FunctionOrPromiseReturn<TSource> = ((x: TSource) => boolean) | ((x: TSource) => Promise<boolean>)
// export function any<TSource, P extends ((x: TSource) => boolean) | ((x: TSource) => Promise<boolean>)>(
//   source: Iterable<TSource>,
//   predicate?: P
// ): P extends (x: TSource) => boolean ? boolean : P extends (x: TSource) => Promise<boolean> ? Promise<boolean> : never {
//   if (predicate) {
//     const isPromise = typeof predicate == ""
//     if (isPromise) {
//       return new Promise((resolve, reject) => {
//         ;(async () => {
//           for (const item of []) {
//             if ((await predicate(item)) === true) {
//               return resolve(true)
//             }
//           }

//           return resolve(false)
//         })()
//       })
//     }
//     for (const item of source) {
//       if ((isPromise ? Promise.resolve(predicate(item)) : predicate(item)) === true) {
//         return true
//       }
//     }

//     return false
//   } else {
//     // eslint-disable-next-line @typescript-eslint/no-unused-vars
//     for (const _ of source) {
//       return true
//     }

//     return false
//   }
// }

export function any<TSource>(source: TSource[], predicate?: (x: TSource) => boolean): boolean {
  if (predicate) {
    for (const item of source) {
      if (predicate(item) === true) {
        return true
      }
    }
    return false
  } else {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    for (const _ of source) {
      return true
    }
    return false
  }
}

export async function anyAsync<TSource>(
  source: TSource[],
  predicate?: (x: TSource) => Promise<boolean>
): Promise<boolean> {
  if (predicate) {
    for (const item of source) {
      if ((await predicate(item)) === true) {
        return true
      }
    }
    return false
  } else {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    for (const _ of source) {
      return true
    }
    return false
  }
}

export function count<TSource>(source: TSource[], predicate?: (x: TSource) => boolean): number {
  if (predicate) {
    // eslint-disable-next-line no-shadow
    let count = 0
    for (const item of source) {
      if (predicate(item) === true) {
        count++
      }
    }
    return count
  } else {
    // eslint-disable-next-line no-shadow
    let count = 0
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    for (const _ of source) {
      count++
    }
    return count
  }
}

export async function countAsync<TSource>(
  source: TSource[],
  predicate?: (x: TSource) => Promise<boolean>
): Promise<number> {
  if (predicate) {
    // eslint-disable-next-line no-shadow
    let count = 0
    for (const item of source) {
      if ((await predicate(item)) === true) {
        count++
      }
    }
    return count
  } else {
    // eslint-disable-next-line no-shadow
    let count = 0
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    for (const _ of source) {
      count++
    }
    return count
  }
}

export function isEmpty<TSource>(source: TSource[]): boolean {
  if (source.length === 0) {
    return true
  }
  return false
}

export function clear<TSource>(source: TSource[]): void {
  if (source.length === 0) {
    return
  }
  source.splice(0, source.length)
}

export function update<TSource>(source: TSource[], data: TSource[]): TSource[] {
  // clear array
  clear(source)
  // insert array
  source.splice(0, 0, ...data)
  return source
}

export function firstOrDefault<TSource>(source: TSource[], predicate?: (x: TSource) => boolean): TSource | undefined {
  // https://stackoverflow.com/questions/4090491/how-to-get-the-first-element-of-an-array#answer-27180125
  // source.filter((x) => typeof x !== "undefined").shift()
  if (predicate) {
    for (const item of source) {
      if (predicate(item) === true) {
        return item
      }
    }
    return undefined
  } else {
    for (const item of source) {
      return item
    }
    return undefined
  }
}

export async function firstOrDefaultAsync<TSource>(
  source: TSource[],
  predicate?: (x: TSource) => Promise<boolean>
): Promise<TSource | undefined> {
  // https://stackoverflow.com/questions/4090491/how-to-get-the-first-element-of-an-array#answer-27180125
  // source.filter((x) => typeof x !== "undefined").shift()
  if (predicate) {
    for (const item of source) {
      if ((await predicate(item)) === true) {
        return item
      }
    }
    return undefined
  } else {
    for (const item of source) {
      return item
    }
    return undefined
  }
}

export function lastOrDefault<TSource>(source: TSource[], predicate?: (x: TSource) => boolean): TSource | undefined {
  // https://stackoverflow.com/questions/4090491/how-to-get-the-first-element-of-an-array#answer-27180125
  // source.filter((x) => typeof x !== "undefined").shift()
  if (predicate) {
    for (const item of source.reverse()) {
      if (predicate(item) === true) {
        return item
      }
    }
    return undefined
  } else {
    for (const item of source.reverse()) {
      return item
    }
    return undefined
  }
}

export async function lastOrDefaultAsync<TSource>(
  source: TSource[],
  predicate?: (x: TSource) => Promise<boolean>
): Promise<TSource | undefined> {
  // https://stackoverflow.com/questions/4090491/how-to-get-the-first-element-of-an-array#answer-27180125
  // source.filter((x) => typeof x !== "undefined").shift()
  if (predicate) {
    for (const item of source.reverse()) {
      if ((await predicate(item)) === true) {
        return item
      }
    }
    return undefined
  } else {
    for (const item of source.reverse()) {
      return item
    }
    return undefined
  }
}

export function removeDuplicate<TSource>(source: TSource[]): TSource[] {
  //https://stackoverflow.com/questions/9229645/remove-duplicate-values-from-js-array
  if (source.length === 0) {
    return []
  }
  return [...new Set(source)]
}

export function removeDuplicateKeepFirstByKey<TSource>(
  source: TSource[],
  getKeyCallback: GetKeyCallbackFn<TSource>
): TSource[] {
  //https://stackoverflow.com/questions/9229645/remove-duplicate-values-from-js-array
  if (source.length === 0) {
    return []
  }
  const seen = new Set()
  return source.filter((item) => {
    const key = getKeyCallback(item)
    return seen.has(key) ? false : seen.add(key)
  })
}

export function removeDuplicateKeepLastByKey<TSource>(
  source: TSource[],
  getKeyCallback: GetKeyCallbackFn<TSource>
): TSource[] {
  //https://stackoverflow.com/questions/9229645/remove-duplicate-values-from-js-array
  if (source.length === 0) {
    return []
  }
  return [...new Map(source.map((item) => [getKeyCallback(item), item])).values()]
}
