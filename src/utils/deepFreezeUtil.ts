// type DeepReadonly<T> = {
//   readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
// }

// export function deepFreeze<T = unknown>(obj: T): DeepReadonly<T> {
//   return obj
// }

export function deepFreeze<T = unknown>(obj: T): T {
  if (obj instanceof Map) {
    obj.clear =
      obj.delete =
      obj.set =
        function () {
          throw new Error("map is read-only")
        }
  } else if (obj instanceof Set) {
    obj.add =
      obj.clear =
      obj.delete =
        function () {
          throw new Error("set is read-only")
        }
  } else if (obj instanceof WeakSet) {
    obj.add = obj.delete = function () {
      throw new Error("WeakSet is read-only")
    }
  } else if (obj instanceof WeakMap) {
    obj.set = obj.delete = function () {
      throw new Error("WeakMap is read-only")
    }
  }

  // WARN: <PERSON><PERSON> một số type sẽ gây lỗi khi xử lý các properies children -> Cần bỏ qua ko freeze
  // TODO: Nên bỏ qua cả class hoặc cần test thêm
  if (obj instanceof RegExp) {
    return obj
  }

  // Freeze self
  Object.freeze(obj)

  Object.getOwnPropertyNames(obj).forEach((name) => {
    const prop = obj[name]
    const type = typeof prop

    // Freeze prop if it is an object or function and also not already frozen
    if ((type === "object" || type === "function") && !Object.isFrozen(prop)) {
      deepFreeze(prop)
    }
  })

  return obj
}

export default deepFreeze
