type CacheEntry<T> = {
  value: T // Gi<PERSON> trị được lưu trong cache
  expiry: number // Thời gian hết hạn
  timeoutId: NodeJS.Timeout // ID của setTimeout để tự động xóa (nếu cần)
}

export default class InMemCacher<K = string, V = any> {
  private cache: Map<K, CacheEntry<V>>

  constructor() {
    this.cache = new Map<K, CacheEntry<V>>()
  }

  /**
   * Lấy giá trị từ cache nếu tồn tại và chưa hết hạn.
   * Nếu hết hạn, tự động xóa khỏi cache.
   * @param key - Khóa để truy xuất giá trị cache.
   * @returns Giá trị từ cache hoặc null nếu không tồn tại hoặc đã hết hạn.
   */
  get(key: K): V | null {
    const now = Date.now()
    const entry = this.cache.get(key)

    if (entry) {
      if (entry.expiry > now) {
        return entry.value // <PERSON>i<PERSON> trị hợp lệ
      }
      // Nế<PERSON> hết hạn, xóa khỏi cache
      this.cache.delete(key)
    }

    return null // Không có giá trị hoặc đã hết hạn
  }

  /**
   * Lưu giá trị vào cache với thời gian sống (TTL).
   * @param key - Khóa để lưu giá trị.
   * @param value - Giá trị cần lưu.
   * @param ttl - Thời gian sống (ms).
   */
  set(key: K, value: V, ttl: number): void {
    // Nếu khóa đã tồn tại, hủy timeout hiện tại trước khi đặt giá trị mới
    if (this.cache.has(key)) {
      const existingEntry = this.cache.get(key)
      if (existingEntry?.timeoutId) {
        clearTimeout(existingEntry.timeoutId)
      }
    }

    const timeoutId = setTimeout(() => {
      this.cache.delete(key) // Tự động xóa khi hết hạn
    }, ttl)

    this.cache.set(key, { value, expiry: Date.now() + ttl, timeoutId })
  }

  /**
   * Thực hiện một action với caching.
   * @param key - Khóa để lưu giá trị cache.
   * @param action - Hàm thực hiện action cần cache.
   * @param ttl - Thời gian sống (TTL) của cache (ms).
   * @returns Kết quả từ cache hoặc từ action.
   */
  async execute(key: K, action: () => Promise<V> | V, ttl: number): Promise<V> {
    const cachedValue = this.get(key)
    if (cachedValue !== null) {
      return cachedValue
    }

    const result = await Promise.resolve(action())
    this.set(key, result, ttl)
    return result
  }

  /**
   * Xóa cache theo key.
   * @param key - Khóa để xóa giá trị trong cache.
   */
  clear(key: K): void {
    const entry = this.cache.get(key)
    if (entry?.timeoutId) {
      clearTimeout(entry.timeoutId) // Hủy timeout
    }
    this.cache.delete(key)
  }

  /**
   * Xóa toàn bộ cache.
   */
  clearAll(): void {
    this.cache.forEach((entry) => {
      if (entry.timeoutId) {
        clearTimeout(entry.timeoutId) // Hủy tất cả timeout
      }
    })
    this.cache.clear()
  }
}
