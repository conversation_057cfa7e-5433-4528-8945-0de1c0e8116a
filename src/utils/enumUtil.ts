import type { SelectListItem } from "@/types/base"

/**
 * <PERSON><PERSON><PERSON><PERSON> hiện convert enum sang SelectListItem[] để hiển thị dropdownlist
 */
export function enumToSelectListItem<TEnum, <PERSON><PERSON><PERSON><PERSON> extends string>(enumType: Record<TKeys, TEnum>): SelectListItem[] {
  const enumKind = getEnumKind(enumType)
  if (enumKind == EnumKind.NumericEnum) {
    // Numeric enum: All enum members have numeric values.
    // Ex: Numeric enum starting from 2.
    // enum Scope {
    //   CREATE = 2,
    //   UPDATE,
    //   DELETE,
    // }
    return Object.entries(enumType as object)
      .filter(([key, value]) => !isNaN(Number(value)))
      .map(([key, value]) => ({ label: key, value }) as SelectListItem)
  } else if (enumKind == EnumKind.StringEnum) {
    // String enum: All enum members have string values
    // enum Scope {
    //   CREATE = "create1",
    //   UPDATE = "edit2",
    //   DELETE = "delete3"
    // }
    return Object.entries(enumType as object).map(([key, value]) => ({ label: key, value }) as SelectListItem)
  } else {
    // Heterogeneous enum: Enum members are mixed but its essential for numeric members to follow a valid numeric value order.
    // Ex: Heterogeneous enum with one string and three numeric members
    // enum Scope {
    //   CREATE,
    //   UPDATE = "edit",
    //   DELETE = 4,
    //   GET,
    // }

    throw Error(`Heterogeneous enum is not suppoted`)
  }
}

/**
 * Lấy giá trị value từ Enum (Cách cụ thể của TEnum.toString())
 * @example
 ```
  enum ColorAsNumericEnum {
    Red,
    Green
  }
  enum ColorAsStringEnum {
    Red="RED",
    Green="GREEN"
  }
  getEnumValue(ColorAsNumericEnum, ColorAsNumericEnum.Green) == 1
  getEnumValue(ColorAsStringEnum, ColorAsStringEnum.Green) == 'RED'
  ```
  */
export function enumGetValue<TEnum, TKeys extends string>(
  enumType: Record<TKeys, TEnum>,
  enumValue: string | number
): string | number {
  return typeof enumValue == "string" ? enumValue.toString() : enumValue
}

/**
 * Lấy Key name của enum dựa trên value
 * @param enumType
 * @param enumValue
 * @returns Enum key name (undefined nếu ko tìm thấy)
 */
export function enumKeyByValue<TEnum, TKeys extends string>(
  enumType: Record<TKeys, TEnum>,
  enumValue: string | number
): string | undefined {
  return Object.keys(enumType)[Object.values(enumType).indexOf(enumValue)]
}

/**
 * Lấy Enum từ giá trị key của Enum
 * @example
 ```
  enum ColorAsNumericEnum {
    Red,
    Green
  }
  enum ColorAsStringEnum {
    Red="RED",
    Green="GREEN"
  }
  getEnumByKey(ColorAsNumericEnum, "Red") == ColorAsStringEnum.Red
  getEnumByKey(ColorAsStringEnum, "Red") == ColorAsStringEnum.Red
  ```
  */
export function enumByKey<TEnum, TKeys extends string>(
  enumType: Record<TKeys, TEnum>,
  enumKey: string | undefined | null,
  defaultIfNotFound?: TEnum
): TEnum {
  const enumKind = getEnumKind(enumType)
  if (enumKind == EnumKind.NumericEnum) {
    // Numeric enum: All enum members have numeric values.
    // Ex: Numeric enum starting from 2.
    // enum Scope {
    //   CREATE = 2,
    //   UPDATE,
    //   DELETE,
    // }
    const keys = Object.keys(enumType).filter((key) => isNaN(Number(key)))
    const values = Object.values(enumType).filter((key) => !isNaN(Number(key)))

    const i = keys.indexOf(String(enumKey))
    if (i < 0 && typeof defaultIfNotFound != "undefined") return defaultIfNotFound
    if (i < 0) throw new Error(`${enumKey} does not exist in enum:` + enumType)
    return values[i] as TEnum
  } else if (enumKind == EnumKind.StringEnum) {
    // String enum: All enum members have string values
    // enum Scope {
    //   CREATE = "create1",
    //   UPDATE = "edit2",
    //   DELETE = "delete3"
    // }
    const keys = Object.keys(enumType)
    const values = Object.values(enumType)

    const i = keys.indexOf(String(enumKey))
    if (i < 0 && typeof defaultIfNotFound != "undefined") return defaultIfNotFound
    if (i < 0) throw new Error(`${enumKey} does not exist in enum:` + enumType)
    return values[i] as TEnum
  } else {
    // Heterogeneous enum: Enum members are mixed but its essential for numeric members to follow a valid numeric value order.
    // Ex: Heterogeneous enum with one string and three numeric members
    // enum Scope {
    //   CREATE,
    //   UPDATE = "edit",
    //   DELETE = 4,
    //   GET,
    // }
    throw Error(`Heterogeneous enum is not suppoted`)
  }
}

export function enumByValue<TEnum, TKeys extends string>(
  enumType: Record<TKeys, TEnum>,
  enumValue: number | string | undefined | null,
  defaultIfNotFound?: TEnum
): TEnum {
  const enumKind = getEnumKind(enumType)
  if (enumKind == EnumKind.NumericEnum) {
    // Numeric enum: All enum members have numeric values.
    // Ex: Numeric enum starting from 2.
    // enum Scope {
    //   CREATE = 2,
    //   UPDATE,
    //   DELETE,
    // }
    const keys = Object.keys(enumType).filter((key) => isNaN(Number(key)))
    const values = Object.values(enumType).filter((key) => !isNaN(Number(key)))

    const i = values.indexOf(Number(enumValue))
    if (i < 0 && typeof defaultIfNotFound != "undefined") return defaultIfNotFound
    if (i < 0) throw new Error(`${enumValue} does not exist in enum:` + enumType)
    return values[i] as TEnum
  } else if (enumKind == EnumKind.StringEnum) {
    // String enum: All enum members have string values
    // enum Scope {
    //   CREATE = "create1",
    //   UPDATE = "edit2",
    //   DELETE = "delete3"
    // }
    const keys = Object.keys(enumType)
    const values = Object.values(enumType)

    const i = values.indexOf(String(enumValue))
    if (i < 0 && typeof defaultIfNotFound != "undefined") return defaultIfNotFound
    if (i < 0) throw new Error(`${enumValue} does not exist in enum:` + enumType)
    return values[i] as TEnum
  } else {
    // Heterogeneous enum: Enum members are mixed but its essential for numeric members to follow a valid numeric value order.
    // Ex: Heterogeneous enum with one string and three numeric members
    // enum Scope {
    //   CREATE,
    //   UPDATE = "edit",
    //   DELETE = 4,
    //   GET,
    // }
    throw Error(`Heterogeneous enum is not suppoted`)
  }
}

export enum EnumKind {
  /** String enum: All enum members have string values
   * @example
   * ```
    enum Scope {
      CREATE = "create1",
      UPDATE = "edit2",
      DELETE = "delete3"
    }
   ```
   */
  StringEnum,
  /** Numeric enum: All enum members have numeric values.
   * @example
   * ```
    //Ex: Numeric enum starting from 2.
    enum Scope {
      CREATE = 2,
      UPDATE,
      DELETE,
    }
   ```
   */
  NumericEnum,
  /** Heterogeneous enum: Enum members are mixed but its essential for numeric members to follow a valid numeric value order.
   * @example
   * ```
    //Ex: Heterogeneous enum with one string and three numeric members
    enum Scope {
      CREATE,
      UPDATE = "edit",
      DELETE = 4,
      GET,
    }
   ```
   */
  HeterogeneousEnum,
}

export function getEnumKind<TEnum, TKeys extends string>(enumType: Record<TKeys, TEnum>): EnumKind {
  // Nếu chỉ check đơn giản cho 2 loại StringEnum và NumericEnum thì
  // const isNumericEnum = Object.entries(enumType).some(([key, value]) => isNaN(Number(value)) == false) // Có 1 thằng có value là number (Cần tránh trường hợp value=0)
  // const isStringEnum = !isNumericEnum // NOTE: Cần xử lý ngược hơn là dùng every isNaN(Number(value))==true do để tránh trường hợp Number("1") = 1 và trường hợp Number("0") = 0 = false

  const countValuesIsNumber = Object.entries(enumType).filter(([key, value]) => isNaN(Number(value)) == false).length
  const countKeys = Object.keys(enumType).length
  const isStringEnum = countValuesIsNumber == 0
  const isNumericEnum = countValuesIsNumber * 2 == countKeys
  const isHeterogeneousEnum = countValuesIsNumber * 2 != countKeys

  return isStringEnum ? EnumKind.StringEnum : isNumericEnum ? EnumKind.NumericEnum : EnumKind.HeterogeneousEnum
}
