//import { useAppSettings } from "@/appsettings"
import APPC<PERSON>FIG from "@/appConfig";

export function getImgSrc(fileId: Nullable<string>): string | undefined {
  //const appSettings = useAppSettings()

  return fileId ? `${APPCONFIG.APP_API_ENDPOINT}/v1/file/${fileId}` : undefined
  //return "https://192.168.66.63:4569/api/v1/file/4262cb8c-07d7-44e1-9d42-3ee79202aad4"
}

export function reload(options?: { replace?: boolean }) {
  if (options?.replace) {
    window.location.replace(window.location.href)
  } else {
    window.location.reload()
  }
}

export function redirectTo(
  href: string,
  options?: {
    replace?: boolean
  }
) {
  console.log("redirectTo", href)

  if (!href) {
    console.warn("href is not found")
    return
  }

  if (options?.replace) {
    window.location.replace(href)
  } else {
    window.location.href = href
  }
}