import { ref, watch, WatchSource } from "vue"

export async function delay(timeout: int = 3000): Promise<void> {
  return await new Promise((resolve) => setTimeout(resolve, timeout))
}

/**
 * Hỗ trợ delay để đản bảo sau khi hiện loading=true thì phải sau tối thiểu thời gian delay thì mới tắt loading=false.
 * @param loadingSource source để watch. Chấp nhận ref | computed | getter funcfion.
 * @param minTime giá trị timeout tối thiểu hiện loading (default: 2000ms).
 * @returns The Loading with delay. Nó sẽ chỉ có thể false chỉ sau thời gian tối thiểu.
 */
export function useLoadingWithMinTime(loadingSource: WatchSource<boolean>, minTime: number = 2000) {
  // Cứ khi set loading=true thì phải đợi sau timeout mới có loading=false
  //*
  let timeoutId: any
  const loadingWithDelay = ref(false)
  watch(
    loadingSource,
    (isLoading) => {
      if (isLoading) {
        loadingWithDelay.value = isLoading
        if (timeoutId) clearTimeout(timeoutId)
        timeoutId = setTimeout(() => {
          loadingWithDelay.value = false
          // remove timeoutId
          timeoutId = undefined
        }, minTime)
      }
    },
    {
      immediate: true,
    }
  )
  return loadingWithDelay
  //*/
  /*
  const isLoading = refAutoReset(false, minTime) //from "@vueuse/core"
  watch(
    loadingSource,
    (loading) => {
      if (loading) {
        isLoading.value = true
      }
    },
    { immediate: true }
  )
  return isLoading
  //*/
  // const loading = toRef(loadingSource)
  // const loadingDelay = ref(false)
  // let timeoutId: ReturnType<typeof setTimeout> | undefined
  // // Shorthand for watching value to be truthy
  // whenever(
  //   loading,
  //   () => {
  //     // Mỗi khi loading=true thì kích hoạt bộ đợi timeout.
  //     //console.log("loading delay triggered")
  //     loadingDelay.value = true
  //     timeoutId && clearTimeout(timeoutId)
  //     timeoutId = setTimeout(() => {
  //       //console.log("loading delay end")
  //       loadingDelay.value = false
  //       timeoutId = undefined
  //     }, delay)
  //   },
  //   {
  //     immediate: true,
  //   }
  // )

  // return computed(() => loadingDelay.value || loading.value) // chỉ khi loadingDelay=false thì kích hoạt check computed loading
}

export async function executeTaskWithMinTime<T>(task: () => Promise<T>, minTime: number = 2000): Promise<T> {
  const delay = new Promise<void>((resolve) => setTimeout(resolve, minTime))

  return Promise.all([task(), delay]).then(([result]) => result)
}
