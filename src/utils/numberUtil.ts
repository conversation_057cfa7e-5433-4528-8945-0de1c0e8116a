/**
 * Hàm chuẩn để trả về số đầu vào với số thập phân mong muốn mà ko bị làm tròn giá trị. (number.toFixed(2) bị làm tròn lên hoặc xuống theo giá trị)
 *
 * 123.45 -> 123
 *
 * 123.99 -> 123
 */
export function truncNumber(num: number, fixed: number): number {
  // if (Math.trunc) {
  //   return Math.trunc(num * Math.pow(10, fixed)) / Math.pow(10, fixed) // Hàm này vẫn cắt sai 9999999999.22 -> 9999999999.21 ??
  // }

  // https://stackoverflow.com/questions/4187146/truncate-number-to-two-decimal-places-without-rounding
  //const toFixedRegex = new RegExp("^-?\\d+(?:\\.\\d{0," + (fixed || -1) + "})?") // Ex: /^-?\d+(?:\.\d{0,0})?/
  const toFixedRegex = getOrSetRegExpCache(`numberUtil.truncNumber.${fixed}`, () => createRegExpTruncNumber(fixed))
  return Number(num.toString().match(toFixedRegex)?.firstOrDefault())
}
const createRegExpTruncNumber = (fixed: number): RegExp => {
  return new RegExp("^-?\\d+(?:\\.\\d{0," + (fixed || -1) + "})?") // Ex: /^-?\d+(?:\.\d{0,0})?/
}

//#region hỗ trợ để cache Regex, tránh khởi tạo RegExp nhiều lần, tăng performance.
const RegExpCache: Record<string, RegExp> = {}
const getOrSetRegExpCache = (cacheKey: string, createRegExpCallback: () => RegExp): RegExp => {
  let regex: RegExp = RegExpCache[cacheKey]
  if (!regex) {
    regex = createRegExpCallback()
    RegExpCache[cacheKey] = regex
    console.debug("getOrSetRegExpCache", `stored cache '${cacheKey}'`)
  }
  return regex
}
//#endregion

export function roundNumber(num: number, fixed: number): number {
  // num + 0.0000001 để tip trick xử lý đản bảo round(4.475, 2) = 4.48 (thay vì giá trị sai là 4.47)
  const multiplier = Math.pow(10, fixed)
  return Math.round((num + 0.0000001) * multiplier) / multiplier
}
