/**
 * https://betterprogramming.pub/typescript-with-go-rust-errors-no-try-catch-heresy-da0e43ce5f78
 */

import { AxiosError, isAxiosError } from "axios"

type Result<T> = { success: true; value: T } | { success: false; error: Error }

function ok<T>(value: T): Result<T> {
  return { success: true, value }
}

function err(error: Error): Result<never> {
  return { success: false, error }
}

/**
 * https://levelup.gitconnected.com/i-fixed-error-handling-in-javascript-4e3c1a28a292
 * @example const readFile = resultify(fs.readFileSync);
 */
function resultify<T, U>(fn: (arg: T) => U): (arg: T) => Result<U> {
  return (arg: T) => {
    try {
      return ok(fn(arg))
    } catch (error) {
      if (error instanceof Error)
        return err(error)
      else
        return err(new Error("Something went wrong"))
    }
  }
}

//#region safe methods
export type Safe<T> =
  | {
      success: true
      data: T
    }
  | {
      success: false
      error: Error | AxiosError | undefined
      innerError?: any
    }

export type ErrorCallbackHandler = (err: any) => Error | AxiosError | undefined
export type SafeExecuteInput<T> = (() => T) | (() => Promise<T>)
export type SafeExecuteOutput<T> = Promise<Safe<T>> | PromiseLike<Safe<T>> | Safe<T>

export function safeExecute<T>(func: () => T, err?: string | ErrorCallbackHandler): Safe<T> {
  try {
    const result = func()
    return { success: true, data: result }
  } catch (e) {
    console.error("[Safe handle]", e)
    return { success: false, error: handleError(e, err) }
  }
}

export async function safeExecuteAsync<T>(func: () => Promise<T>, err?: string | ErrorCallbackHandler): Promise<Safe<T>> {
  try {
    const result = await func()
    return { success: true, data: result }
  } catch (e) {
    console.error("[Safe handle]", e)
    return { success: false, error: handleError(e, err) }
  }
}

export function safeExecuteSync<T>(func: () => T, err?: string | ErrorCallbackHandler): Safe<T> {
  try {
    const result = func()
    return { success: true, data: result }
  } catch (e) {
    console.error("[Safe handle]", e)
    return { success: false, error: handleError(e, err) }
  }
}

// export function safeExecute<T>(promise: Promise<T>, err?: string | ErrorCallbackHandler): Promise<Safe<T>>
// export function safeExecute<T>(promise: PromiseLike<T>, err?: string | ErrorCallbackHandler): PromiseLike<Safe<T>>
// export function safeExecute<T>(func: () => T, err?: string | ErrorCallbackHandler): Safe<T>
// export function safeExecute<T>(func: () => Promise<T>, err?: string | ErrorCallbackHandler): Promise<Safe<T>>

// export function safeExecute<T>(
//   promiseOrFunc: SafeExecuteInput<T>,
//   err?: string | ErrorCallbackHandler
// ): SafeExecuteOutput<T> {
//   try {
//     if (isPromise(promiseOrFunc) || isPromiseLike(promiseOrFunc)) {
//       return Promise.resolve()
//         .then(() => promiseOrFunc)
//         .then((result) => ({ success: true, data: result }) as SafeResultTrue<T>)
//         .catch((e) => ({ success: false, error: handleError(e, err) }) as SafeResultFalse<T>)
//     }
//     if (typeof promiseOrFunc === "function") {
//       const result = promiseOrFunc()
//       if (isPromise(result) || isPromiseLike(result)) {
//          return Promise.resolve()
//            .then(() => result)
//            .then((result) => ({ success: true, data: result }) as SafeResultTrue<T>)
//            .catch((e) => ({ success: false, error: handleError(e, err) }) as SafeResultFalse<T>)
//       }
//       return { success: true, data: result }
//     }
//   } catch (e) {
//     return { success: false, error: handleError(e, err) }
//   }

//   throw new Error("Invalid input type")
// }

// export function safeExecute<T>(
//   promiseOrFunc: SafeExecuteInput<T>,
//   err?: string | ErrorCallbackHandler
// ): Promise<Safe<T>> | PromiseLike<Safe<T>> | Safe<T> {
//   if (isPromise(promiseOrFunc) || isPromiseLike(promiseOrFunc)) {
//     return safeExecuteAsync(promiseOrFunc, err)
//   }
//   return safeExecuteSync(promiseOrFunc, err)
// }

// async function safeExecuteAsync<T>(
//   promise: Promise<T> | PromiseLike<T>,
//   err?: string | ErrorCallbackHandler
// ): Promise<Safe<T>> {
//   try {
//     const result = await promise
//     return { success: true, data: result }
//   } catch (e) {
//     console.error("[Safe handle]", e)
//     return { success: false, error: handleError(e, err) }
//   }
// }

// function safeExecuteSync<T>(func: () => T, err?: string | ErrorCallbackHandler): Safe<T> {
//   try {
//     const result = func()
//     return { success: true, data: result }
//   } catch (e) {
//     console.error("[Safe handle]", e)
//     return { success: false, error: handleError(e, err) }
//   }
// }

function isPromise<T>(value: any | Promise<T>): value is Promise<T> {
  return !!value && typeof value.then === "function"
}

function isPromiseLike<T>(value: any | PromiseLike<T>): value is PromiseLike<T> {
  return !!value && typeof value.then === "function"
}

function isFunction<T>(value: any | (() => T)): value is () => T {
  return typeof value == "function"
}

function handleError<T>(error: unknown, handle?: string | ErrorCallbackHandler): Error | AxiosError | undefined {
  if (typeof handle === "function") {
    return handle(error)
  }
  if (typeof handle === "string") {
    return new Error(handle)
  }
  if (isAxiosError(error)) {
    return error
  }
  if (error instanceof Error) {
    return error
  }
  return new Error("Something went wrong")
}

//#endregion safe methods
