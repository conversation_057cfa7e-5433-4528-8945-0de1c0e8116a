export function convertToBoolean(value: string | null | undefined) : boolean | undefined {
  value = value?.trim()

  if (!value) {
    return false // Treat null or empty string as false
  } else  if (value === "") {
    return false // Treat empty string as false
  } else if (value.toLowerCase() === "true") {
    return true
  } else if (value.toLowerCase() === "false") {
    return false
  } else if (value.toLowerCase() === "1") {
    return true
  } else if (value.toLowerCase() === "0") {
    return false
  } else {
    return undefined
  }

  return undefined
}
