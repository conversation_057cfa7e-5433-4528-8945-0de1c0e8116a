import APPCONFIG from "@/appConfig"
import { useGlobalCookies } from "@/plugins/cookies"
import { type AxiosStatic } from "axios"

export function useFakeAuthCookies(axios: AxiosStatic) {
  if (APPCONFIG.MODE == "development" || APPCONFIG.MODE == "production") {
    if (APPCONFIG.APP_DEV_FAKE_JWT) {
      console.log("execute FakeAuthCookies")

      const cookies = useGlobalCookies()
      cookies.set("auth", APPCONFIG.APP_DEV_FAKE_JWT, {
        // path: '/ma', ???
      })

      axios.defaults.withCredentials = !!APPCONFIG.APP_DEV_FAKE_JWT // TODO send cookies APP_DEV_FAKE_JWT https://stackoverflow.com/questions/43002444/make-axios-send-cookies-in-its-requests-automatically
    }
  }
}
