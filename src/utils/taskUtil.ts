import { watch } from "vue"
import { Task } from "vue-concurrency"
import { useToastService } from "@/services/toastService"

export function handleTaskError<T, U extends any[]>(
  task: Task<T, U>,
  taskName: string,
  callbackWhenError?: (task: Task<T, U>) => void
): Task<T, U> {
  watch(
    () => task.isError,
    (isError) => {
      if (isError) {
        const toastService = useToastService()

        // Get message if can.
        const message = task.last?.error.message ? task.last?.error.message : task.last?.error
        console.warn(
          `error of task '${taskName}' has been catched!`,
          message ? `message: ${message}` : task.last?.error
        )

        // Một số message đã gặp: Timeout
        if (callbackWhenError) {
          callbackWhenError(task)
        } else {
          // Default error handling
          toastService.error({})
        }
      }
    }
  )
  return task
}
