import type { AxiosResponse } from "axios"

function isAxiosResponse(input: any): input is AxiosResponse {
  return typeof input == "object" && input.data && input.headers
}

/**
 * Xử lý save file downloaded from api.
 * Chú ý nó không thể xử lý file lớn ~500MB. Tham khảo [link](https://github.com/axios/axios/issues/5891)
 * @param response
 * @param fileName - File name to save. (if empty, try get from response)
 */
export function saveAs(response: AxiosResponse | Blob | File, fileName?: string): void {
  const UNKNOWN_FILENAME = "unknown_name.data"
  let href: string

  if (response instanceof Blob) {
    // Blob doesnt have file name info
    fileName = fileName || UNKNOWN_FILENAME

    href = URL.createObjectURL(response)
  } else if (response instanceof File) {
    // get file name from response
    fileName = fileName || response.name || UNKNOWN_FILENAME

    href = URL.createObjectURL(response)
  } else if (isAxiosResponse(response)) {
    // get file name from response
    fileName = fileName || getFileNameFromResponse(response) || UNKNOWN_FILENAME

    // create file link in browser's memory
    //const blob = new Blob([res.data], { type: "application/pdf" })
    //const href = URL.createObjectURL(blob)
    href = URL.createObjectURL(response.data)
  } else {
    throw new Error("Type of response is not supported. It cannot save to file")
  }

  // create "a" HTML element with href to file & click
  const link = document.createElement("a")
  link.href = href
  if (fileName) link.setAttribute("download", fileName) // set file name
  document.body.appendChild(link)

  // trigger click
  link.click()

  // clean up "a" element & remove ObjectURL
  document.body.removeChild(link)
  URL.revokeObjectURL(href)
}

export function getFileNameFromResponse(response: AxiosResponse): string | null {
  let fileName: string | null = null
  const contentDisposition = response.headers["content-disposition"]
  if (contentDisposition) {
    const fileNameMatch = contentDisposition.match(/filename=["']?([^'"]+)["']?/)
    //const fileNameMatch = contentDisposition.match(/filename(?:\*)?=["']?(?:UTF|utf-8'')?([^'"]+)["']?/)
    // support:  attachment; filename=file.txt
    // support:  attachment; filename*="UTF-8''file name tiếng việt.txt"

    if (fileNameMatch.length === 2) fileName = decodeURIComponent(fileNameMatch[1])
  }
  return fileName
}

export const blobToFile = (blob: Blob, fileName: string): File => {
  const b: any = blob
  //A Blob() is almost a File() - it's just missing the two properties below which we will add
  b.lastModifiedDate = new Date()
  b.name = fileName

  //Cast to a File() type
  return blob as File
}
