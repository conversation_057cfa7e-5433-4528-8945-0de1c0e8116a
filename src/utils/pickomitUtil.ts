//https://camchenry.com/blog/typescript-pick-omit
//https://stackoverflow.com/questions/56916532/difference-b-w-only-exclude-and-omit-pick-exclude-typescript

export function extend<Data extends object, Source extends object>(data: Data, sources: Source): Data & Source {
  return Object.assign({}, data, sources)
}

/**
 * Tạo đối tượng mới chỉ có các property của đối tượng cái được lựa chọn.
 * Đ<PERSON><PERSON> là phiên bản đơn giản của Lodash.pick
 * @param data - object
 * @param keys - Include prop keys
 * @returns 
 */
export function pick<Data extends object, Keys extends keyof Data>(data: Data, keys: Keys[]): Pick<Data, Keys> {
  const result = {} as Pick<Data, Keys>

  for (const key of keys) {
    result[key] = data[key]
  }

  return result
}

/**
 * Tạo đối tượng mới bao gồm các property của đối tượng cái bị loại trừ.
 * <PERSON><PERSON><PERSON> là phiên bản đơn giản của Lo<PERSON>h.omit
 * @param data - object
 * @param keys - Exclude prop keys
 * @returns 
 */
export function omit<Data extends object, <PERSON> extends keyof Data>(data: Data, keys: Keys[]): Omit<Data, Keys> {
  const result = { ...data }

  for (const key of keys) {
    delete result[key]
  }

  return result as Omit<Data, Keys>
}
