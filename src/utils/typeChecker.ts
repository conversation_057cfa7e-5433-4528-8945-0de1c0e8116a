//https://dev.to/krumpet/generic-type-guard-in-typescript-258l
// Chỉ áp dụng được với các Type là Primitive types hoặc Class types.

interface TypeMap {
  // for mapping from strings to types
  string: string
  number: number
  boolean: boolean
}

type PrimitiveOrConstructor = // 'string' | 'number' | 'boolean' | constructor
  { new (...args: any[]): any } | keyof TypeMap

// infer the guarded type from a specific case of PrimitiveOrConstructor
type GuardedType<T extends PrimitiveOrConstructor> = T extends { new (...args: any[]): infer U }
  ? U
  : T extends keyof TypeMap
    ? TypeMap[T]
    : never

// finally, guard ALL the types!
export function typeGuard<T extends PrimitiveOrConstructor>(o: any, className: T): o is GuardedType<T> {
  const localPrimitiveOrConstructor: PrimitiveOrConstructor = className
  if (typeof localPrimitiveOrConstructor === "string") {
    return typeof o === localPrimitiveOrConstructor
  }
  return o instanceof localPrimitiveOrConstructor
}

/**
 * Kiểm tra object input thuộc type nào.
 * Chỉ áp dụng được với các Type là Primitive Types hoặc Class Types
 * @example
console.log(isType(5, 'number'), 'is true');
console.log(isType(5, 'string'), 'is false');

console.log(isType(new A(), A), 'is true');
console.log(isType(new A(), B), 'is false');

console.log(isType(new B(), A), 'is true');
console.log(isType(new B(), B), 'is true');

console.log(isType(new B(), 'string'), 'is false');
 */
export const isType = typeGuard