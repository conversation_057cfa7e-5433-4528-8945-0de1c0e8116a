import moment from "moment"
import APPCONFIG from "@/appConfig"

export function roundCurrency<T extends Nullable<number>>(
  amount: T,
  currency: "USD" | "VND" | string | null | undefined
): T {
  currency = currency?.toUpperCase()
  if (currency?.trim() == "") {
    currency = null
  }

  if (amount == null || amount == undefined) {
    return amount
  }

  let fractionDigits: number
  switch (currency) {
    case "USD":
      fractionDigits = 2
      break
    case "VND":
      fractionDigits = 0
      break
    default:
      fractionDigits = 2
      //const logger = useGlobalLogger()
      //throw Error(`The currency '${currency}' is not allowed.`)
      // Xử lý cố hiển thị với warning.
      //logger.warn(`The currency '${currency}' round amount is not supported.`)
      break
  }
  //return Number(amount.toFixed(fractionDigits)) as T // Code này có lỗi làm tròn sai
  //return Number(formatNumber(amount, fractionDigits).replaceAll(",", "")) as T // Code này dài dòng loằng ngoằng quá

  // amount + 0.0000001 để tip trick xử lý đản bảo round(4.475, 2) = 4.48 (thay vì giá trị sai là 4.47)
  const multiplier = Math.pow(10, fractionDigits)
  return (Math.round((amount + 0.0000001) * multiplier) / multiplier) as T
}

export interface FormatCurrencyOptions {
  appendCurrency?: boolean
  valueIfZero?: string
}
export function formatCurrency(
  amount: number | null | undefined,
  currency: "USD" | "VND" | string | null | undefined,
  options?: FormatCurrencyOptions
): string {
  const { appendCurrency } = Object.assign(
    {
      appendCurrency: true,
    },
    options
  )

  currency = currency?.trim().toUpperCase()
  if (currency == "") {
    currency = undefined
  }

  if (amount == 0 || amount == null || amount == undefined) {
    return "-"
  }

  if (amount == 0 && options?.valueIfZero != undefined) {
    return options?.valueIfZero
  }

  let fractionDigits: number
  switch (currency) {
    case "USD":
      fractionDigits = 2
      break
    case "VND":
      fractionDigits = 0
      break
    default:
      fractionDigits = 2
      //const logger = useGlobalLogger()
      //throw Error(`The currency '${currency}' is not allowed.`)
      // Xử lý cố hiển thị với warning.
      //logger.warn(`The currency '${currency}' formater is not supported.`)
      break
  }

  let formatedAmount = Intl.NumberFormat("en-US", {
    style: "decimal",
    currencyDisplay: "code",
    minimumFractionDigits: fractionDigits,
    maximumFractionDigits: fractionDigits,
    currency: currency ?? undefined,
  }).format(amount)
  if (appendCurrency && currency) formatedAmount = formatedAmount.replace(/^(.+)$/, `$1 ${currency}`)
  return formatedAmount
}

export function formatNumber(amount: number | null | undefined, digits: number = 2): string {
  if (amount == null || amount == undefined) {
    return "-"
  } else {
    return Intl.NumberFormat("en-US", {
      style: "decimal",
      currencyDisplay: "code",
      minimumFractionDigits: digits,
      maximumFractionDigits: digits,
    }).format(amount)
  }
}

export function formatExchangeRate(
  fromAmount: number,
  fromCurrency: "USD" | "VND" | string | null | undefined,
  toAmount: number | null | undefined,
  toCurrency: "USD" | "VND" | string | null | undefined
): string {
  // Trước 09/01/2025: Khi format cho exchange rate thì theo format currency. Ex: 1 USD = 23,000 VND
  // Sau 09/01/2025  : Khi format cho exchange rate thì cần giữ phần thập phân .00 khi hiển thị. Ex: 1 USD = 23,000.00 VND
  // để đản bảo hiển thị rằng payment amount = đúng sát với total amount * exchange rate.
  return `${fromAmount || "-"} ${fromCurrency || "-"} = ${toAmount ? formatNumber(toAmount, 2) : "-"} ${toCurrency || "-"}`.replaceAll("- -", "-")
}

export function formatRangeDateOnly(
  start: string | Date | null | undefined,
  end: string | Date | null | undefined
): string {
  return `${formatDateOnly(start, "")} - ${formatDateOnly(end, "")}`
}

export function formatRangeDateTime(
  start: string | Date | null | undefined,
  end: string | Date | null | undefined
): string {
  return `${formatDateTime(start, "")} - ${formatDateTime(end, "")}`
}

/**
 * Formats a given date value to a string with the format specified in the application configuration.
 *
 * @param value - The date value to format.
 * @param defaultVal - The default string to return if the value is null or undefined. Defaults to an empty string.
 * @returns The formatted date string or the default value if the input is null or undefined.
 */
export function formatDateOnly(value: string | Date | null | undefined, defaultVal: string = ""): string {
  return !value ? defaultVal : moment(value).format(APPCONFIG.FORMAT_DATEONLY_MOMENT)
}

/**
 * Formats a given date/time value into a string based on the application's date/time format configuration.
 *
 * @param value - The date/time value to format.
 * @param defaultVal - The default value to return if the input value is null or undefined. Defaults to an empty string.
 * @returns The formatted date/time string or the default value if the input is null or undefined.
 */
export function formatDateTime(value: string | Date | null | undefined, defaultVal: string = ""): string {
  return !value ? defaultVal : moment(value).format(APPCONFIG.FORMAT_DATETIME_MOMENT)
}

export function formatTimeZone(value: string | Date | null, defaultVal: string = ""): string {
  return !value ? defaultVal : moment(value).format("ZZ")
}
