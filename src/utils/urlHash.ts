export type UrlHashParameterRecord = Record<string, string>

function urlHashFn() {
  function toUrlHashParameters(params: UrlHashParameterRecord) {
    const keys = Object.keys(params)
    const values = Object.values(params)
    const pairs: string[] = []
    keys.forEach(function (key, i) {
      if (key != "") pairs.push(keys[i] + "=" + values[i])
    })
    const hash = `#${pairs.join("&")}`

    return hash
  }

  function getUrlHashParameter(param: string) {
    const params = getUrlHashParameters()
    return params[param]
  }

  function getUrlHashParameters() {
    let sPageURL = window.location.hash
    if (sPageURL) sPageURL = sPageURL.split("#")[1]
    const pairs = sPageURL.split("&")
    const hashParamObj: UrlHashParameterRecord = {}
    pairs.forEach(function (pair, i) {
      const pairItems = pair.split("=")
      if (pairItems[0] != "") hashParamObj[pairItems[0]] = pairItems[1]
    })
    return hashParamObj
  }

  // accepts an object like { paramName: value, paramName1: value }
  // and transforms to: url.com#paramName=value&paramName1=value
  function setUrlHashParameters(params: UrlHashParameterRecord) {
    const keys = Object.keys(params)
    const values = Object.values(params)
    const pairs: string[] = []
    keys.forEach(function (key, i) {
      if (key != "") pairs.push(keys[i] + "=" + values[i])
    })
    const hash = pairs.join("&")
    //window.location.hash = hash
    // use pushState to make silently update-url-without-triggering-route-in-vue-router
    if (history.replaceState) {
      history.replaceState(null, "", `${location.href.split("#")[0]}#${hash}`)
    } else if (history.pushState) {
      history.pushState(null, "", `${location.href.split('#')[0]}#${hash}`)
    } else {
      location.hash = hash
    }
  }

  function setUrlHashParameter(param: string, value: string | number | undefined | null) {
    const params = getUrlHashParameters()
    params[param] = String(value)
    setUrlHashParameters(params)
  }

  return {
    toUrlHashParameters: toUrlHashParameters,
    getUrlHashParameter: getUrlHashParameter,
    getUrlHashParameters: getUrlHashParameters,
    setUrlHashParameter: setUrlHashParameter,
    setUrlHashParameters: setUrlHashParameters,
  }
}

export const urlHash = urlHashFn()