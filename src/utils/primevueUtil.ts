import type { GlobalComponentConstructor } from "primevue/ts-helpers"
import type { DefineComponent } from "vue"

//export function getComponentDefaultProps(primeComponent: DefineComponent<{}, {}, any>): Record<string | number | symbol, any> {
export function getComponentDefaultProps<T>(primeComponent: GlobalComponentConstructor<T>): Record<string | number | symbol, any> {
  const component = primeComponent as DefineComponent<{}, {}, any>
  const props = {
    ...component.extends?.props,
    ...component.props
  }
  const propsDefaults: Record<string | number | symbol, any> = {}
  $.each(props, (key, value) => {
    if (value?.default) propsDefaults[key] = value?.default
  })
  return propsDefaults
}

export const getPrimevueDefaultProps = getComponentDefaultProps

export default {
  getDefaultProps: getComponentDefaultProps
}