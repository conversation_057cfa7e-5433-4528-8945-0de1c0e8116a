import slugid from "slugid"

export function generateUUid(): string {
  return slugid.decode(slugid.v4())
}

export function generateShortUUid(): string {
  return slugid.v4()
}

export function makeRandom(length: number) {
  let result = ""
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
  const charactersLength = characters.length
  let counter = 0
  while (counter < length) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength))
    counter += 1
  }
  return result
}
