import { computed, ComputedRef, nextTick, Ref, ref } from "vue"

export function useComputedRefreshable<T>(getter: () => T): [ComputedRef<T>, () => void, Ref<number>] {
  const refreshKey = ref(0)
  const computedGetter = () => {
    const v = refreshKey.value // track
    return getter()
  }
  const refreshFn = async () => {
    refreshKey.value = refreshKey.value + 1
    await nextTick()
  }

  return [computed(computedGetter), refreshFn, refreshKey]
}
