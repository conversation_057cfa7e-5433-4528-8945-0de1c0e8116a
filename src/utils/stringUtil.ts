export function searchText(source: string, searchValue?: string): boolean {
  return searchValue ? source.search(new RegExp(searchValue, "i")) > -1 : true
}

export function capitalize(value: string | null | undefined): string {
  return !value ? "" : value.charAt(0).toUpperCase() + value.slice(1)
}

/**
 *
 * @example
 * formatText("Hello {0}. My name is {1}", "<PERSON>", "<PERSON>") -> Hello Jon. My name is Anna
 */
export function formatText(format: string, ...args: any[]): string {
  //var args = Array.prototype.slice.call(arguments, 1)
  return format.replace(/{(\d+)}/g, function (match, number) {
    return typeof args[number] != "undefined" ? args[number] : match
  })
}

export default {
  searchText,
  capitalize,
  formatText,
}
