{"hello": "Hello", "locale": {"en": "English", "vi": "Vietnamese"}, "common": {"link-name": "Link name", "link-url": "Link URL", "amount": "Amount", "transactionId": "Payment ID", "customerName": "Customer Name", "date": "Date", "paymentMethod": "Payment Method", "cardNumber": "Card Number", "transType": "Trans. Type", "last-updated": "Last updated", "status": "Status", "download-qr": "Download QR", "edit": "Edit", "percentSymbol": "%", "toast": {"success-summary": "Successful", "error-summary": "An error occurred. Please try again", "copy-success-summary": "Link copied successfully", "action-not-permission": "You do not have permission for this action"}, "confirm-dialog": {"accept-label": "Ok", "reject-label": "Cancel", "header": "Confirm", "message": "Are you sure you want to confirm?"}, "payment-link-status": {"SearchLabel": "@:common.payment-link-status.{LimitReached}", "SearchAll": "All", "SearchActive": "Active", "SearchInactive": "Inactive", "SearchExpired": "Expired", "SearchLimitReached": "Maximum reached", "SearchSuccessful": "Successful"}, "payment-link-options": {"labels": {"name": "Name", "phone": "Phone number", "email": "Email", "address": "Address", "notes": "Customer note"}, "states": {"required": "Required", "optional": "Optional"}}, "payment-link-transaction-status": {"AwaitTransactionResult": "Waiting for transaction result", "Successful": "Successful", "Failed": "Failed", "Processing": "Processing", "Incomplete": "Incomplete", "AwaitMerchantApproval": "Waiting for <PERSON>'s approval", "MerchantRejected": "Merchant Rejected", "AwaitOnepayApprovval": "Waiting for OnePay's approval", "OnepayRejected": "OnePay Rejected", "MerchantApproved": "Merchant Approved", "OnepayApproved": "OnePay Approved"}, "payment-link-transaction-type": {"Authorize": "Authorize", "Capture": "Capture", "Purchase": "Purchase", "Refund": "Refund", "RefundCapture": "Refund Capture", "RefundDispute": "Refund Dispute", "VoidAuthorize": "Void Authorize", "VoidCapture": "Void Capture", "VoidPurchase": "Void Purchase", "VoidRefund": "Void Refund", "VoidRefundCapture": "Void Refund Capture", "RequestRefund": "Request Refund", "PayLater": "Pay Later", "Void": "Void"}, "data-table": {"current-page-report-template": "Showing {'{'}first{'}'}-{'{'}last{'}'} of {'{'}totalRecords{'}'} records", "last-updated": "Last updated", "status": "Status", "payment-link-status": {"Active": "Active", "Inactive": "Inactive", "Expired": "Expired", "LimitReached": "Maximum reached", "Successful": "Successful"}, "payment-link-status-tooltip": {"Active": "The link is working normally and accepting payments", "Inactive": "You have actively turned off the link, preventing customers from accessing and making payments", "Expired": "The link has expired due to the set due date, preventing customers from accessing and making payments", "LimitReached": "The link has reached the maximum successful payments set up and does not accept any more payments", "Successful": "The link has been successfully paid"}}, "tooltip": {"exchange-rate-withlink": "The exchange rate is based on the <a href='%{url}'>Currency setting</a> of your merchant profile.", "exchange-rate": "The exchange rate is based on the Currency setting of your merchant profile.", "action-not-permission": "You do not have permission for this action"}, "buttons": {"apply": "Apply", "save": "Save", "confirm": "Confirm", "cancel": "Cancel", "create": "Create", "copy-link": "Copy link", "view-all": "View all", "delete": "Delete", "remove": "Remove", "reload": "Reload"}}, "errors": {"102": {"head-title": "Processing", "title": "Processing", "subtitle": "Processing data. Please try again later."}, "403": {"head-title": "Access denied", "title": "Access denied", "subtitle": "You do not have access to this page. Please contact your merchant admin for more information."}, "404": {"head-title": "URL Not found", "title": "The page you are looking for cannot be found.", "subtitle": "It could be because the URL is wrong or not available."}, "500": {"head-title": "Error", "title": "Error!", "subtitle": "The request has failed"}, "403-NoMerchant": {"head-title": "Access denied", "title": "Access denied", "subtitle": "You do not have access to any merchant profiles. Please contact your merchant admin for more information."}, "403-NoAppPermission": {"head-title": "Access denied", "title": "Access denied", "subtitle": "You do not have access to this application. Please contact your merchant admin for more information."}}, "left-menu": {"input-filter-profiles-placeholder": "Search merchant profile"}, "page-Dashboard": {"page-title": "Dashboard", "mylink-title": "MyLink", "share-your-link": "Share your MyLink to the customers to get paid fast", "btn-copy-link": "Copy link", "btn-download-qr": "Download QR", "txt-or": "or", "btn-create-link": "Create a payment link", "total-revenue": "Total Revenue", "total-refund": "Total Refund", "revenue-trend": "Revenue Trend", "transaction-volume": "Transaction Volume", "transaction-number": "Transaction Number", "transactions": "transactions", "add-link": "Create link"}, "page-LinkManagement": {"page-title": "Link management", "btn-create": "New payment link", "input-filter-placeholder": "Search Link name, Link URL", "data-table": {"columns": {}}, "export-file": {"title": "Exporting files...", "running-export": "The file is exporting. Please proceed to the File Management feature to download"}, "link-type": "Link type"}, "page-LinkManagement-detail": {"page-title": "Link details", "confirms": {"change-link-enabled": {"header": "Confirm your changes", "message": "There are %{sessions} payment sessions currently in progress. Modifying the link information could affect the payment process of the customers. Are you sure to save changes?"}}, "actions": {"edit": "Edit link", "download-qr": "Download QR", "status": "Link status"}, "link-info": {"title": "Link infomation", "name": "Link name", "description": "Description", "payment-method": "Payment method"}, "options": {"required-customer-info": "Require customers to provide information"}, "duedate": "Have a due date", "attachment": "Attachment", "last-updated": "Last updated", "created-date": "Created date", "payment-amount": {"title": "Payment amount", "amount": "Amount", "discount": "Discount", "tax": "Tax", "percent": "%", "total-amount": "Total amount", "payment-amount-number": "Payment amount"}, "limit-payment": {"title": "Limit number of payment", "max-payment": "Maximum payment quantity", "inprogress-payment": "Payments in progress", "successful-payment": "Successful payments"}, "recent-transactions": "Recent transactions", "type": {"one-time": "One-time", "multi": "Multi-use"}}, "page-UserManagement": {"page-title": "User management", "data-table": {"columns": {}}}, "page-UserDetail": {"page-title": "User detail", "page-title-add": "Add new user", "page-title-edit": "Edit user %{userName}", "head-title-edit": "Edit user", "fields": {"email": "Email", "status": "Status", "sourceHeader": "Avaiable", "targetHeader": "Selected", "selectPermissionType": "Choose type"}}, "page-RoleManagement": {"page-title": "Role management", "data-table": {"columns": {}}}, "page-RoleDetail": {"page-title": "Role detail", "page-title-add": "Add new role", "page-title-edit": "Edit role %{roleName}", "head-title-edit": "Edit role", "fields": {"name": "Role name", "description": "Description"}}, "components-paymentLink-add": {"head-title": "Create new payment link", "title": "Create new payment link", "btn-save": "Save", "link-type": "Link type:", "link-type-options": {"one-time": {"lable": "One-time link", "description": "Accepts only one payment"}, "multi": {"lable": "Multi-use link", "description": "Accepts multiple payments from different customers"}}}, "components-paymentLink-LinkInfomation": {"title": "Link information", "link-name": "Link Name", "link-url": "Link URL", "description": "Description", "payment-method": {"title": "Payment method", "PAY_NOW": "PayNow", "INSTALLMENT": "Installment"}, "validate-input": {"required-name": "The link name is required", "required-url": "The link URL is required", "existed-url": "The link URL already existed", "invalid-url": "URL only allow alphanumeric characters, - and _"}, "installment-setting-details": "details"}, "components-paymentLink-LinkOptions": {"title": "More options", "info": "Require customers to provide information", "due-date": "Have a due date", "attachment": "Add attachment", "btn-add": "Add", "collect-info": "Collect info", "required": "Required", "optional": "Optional", "name": "Name", "phone-number": "Phone number", "email": "Email", "address": "Address", "customer-note": "Customer note", "upload-file": "Upload file", "note-1": "Upload only one file", "note-2": "Support Doc, Docx, PDF, JGP, JPEG, PNG", "note-3": "Maximum size 10Mb"}, "components-paymentLink-LinkPaymentAmount": {"title": "Payment amount", "content": "Payment amount is an optional setting of a payment link", "btn-add": "Add", "amount": "Amount", "total-amount": "Total amount", "discount": "Discount", "tax": "Tax", "other-fee": "Other fee"}, "components-paymentLink-LinkLimitPayment": {"title": "Limit number of payment", "content": "Set up the maximum number of successful payments the link accepts", "btn-add": "Add"}, "components-paymentLink-Installment": {"title": "Installment setting", "content": "Select the banks & installment periods you want to allow customers to make payments", "all-bank": "All banks", "all-period": "All periods", "bank-allow": "Bank allowed", "period-allow": "Period allowed", "bank": "Bank", "installment-period": "Installment period", "btn-save": "Save", "btn-cancel": "Cancel", "3-month": "3-month", "6-month": "6-month", "9-month": "9-month", "12-month": "12-month", "15-month": "15-month", "18-month": "18-month", "24-month": "24-month", "placeholder": {"bank": "Select bank", "period": "Select period"}, "placeholder-dropdown-bank": "Search Bank", "placeholder-dropdown-period": "All period"}, "components-paymentLink-AddPaymentAmount": {"title": "Payment amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "amount": "Amount", "discount": "Discount", "tax": "Tax", "other-fee": "Other fee", "total": "Total amount", "payment": "Payment amount", "btn-confirm": "Confirm", "btn-cancel": "Cancel", "btn-add": "Add", "tooltip": "The exchange rate is based on the currency setting of your merchant profile.", "warnning-maximun-amount": "Maximum payment amount:", "warnning-maximun-amount-value": "Maximum payment amount: %{maxAmount}", "max-amount-locale-usd": "9,999,999,999.99 USD", "max-amount-locale-vnd": "9,999,999,999 VND"}, "components-paymentLink-AddLimit": {"title": "Limit number of payment", "btn-confirm": "Confirm", "btn-cancel": "Cancel", "inputAmount-placeholder": "Example: 100"}, "components-paymentLink-AddSuccessPayment": {"title": "Saved successfully", "payment-amount": "Payment amount", "limit-payment": "Maximum payment quantity", "download-qr": "Download QR"}, "components-download-qr-dialog": {"payment-term": "Expiration date", "powered-by": "POWERED BY", "download-qr-header": "Download QR"}, "components-paymentLink-edit": {"head-title": "Edit payment link", "side-bar-header": {"title": "Edit payment link", "action-save": "Save"}, "side-bar-content": {"inprogress-header": "There are payment sessions currently in progress", "inprogress-body": "Modifying the link information at the moment could affect the payment process of the customers"}, "dialog-confirm": {"title": "Confirm your changes", "detail": "There are {inprogressPayment} payment sessions currently in progress. Modifying the link information could affect the payment process of the customers. Are you sure to save changes?", "actions": {"confirm": "Confirm", "cancel": "Cancel"}}, "tooltip": "Link type is not allowed to edit"}, "components-export-transactions-dialog": {"header": "Download transaction", "title": "Please select the time period you want to download transactions", "actions": {"download": "Download", "cancel": "Cancel"}}, "component-create-payment": {"label": "Create new payment", "amount-label": "Amount", "note-label": "Note", "pay": "Pay"}, "component-transaction-setting-column": {"header": {"title": "Column display"}, "content": {"select-all": "Select all", "selected": "selected", "trans-id": "Payment ID", "customer-name": "Customer name", "customer-email": "Customer email", "customer-phone": "Customer mobile", "customer-address": "Customer address", "customer-note": "Customer note", "trans-ref": "Transaction reference", "order-ref": "Order reference", "link-name": "Link name", "link-url": "Link URL", "amount": "Amount", "date": "Date", "payment-method": "Payment Method", "trans-type": "Transaction Type", "status": "Status", "note": "Note", "payment-date": "Payment date", "card-type": "Card type", "card-number": "Card number", "operator": "Operator", "response-code": "Response code"}, "footer": {"apply": "Apply", "cancel": "Cancel"}}, "component-transaction-more-filter": {"payment-method": "Payment method", "status": "Transaction status", "list-payment-method": {"Label": "@:component-transaction-more-filter.list-payment-method.{0}", "All": "All", "InternationalCard": "International card", "DomesticCard": "Domestic card", "MobileApp": "Mobile app", "DigitalWallet": "Digital wallet", "BuyNowPayLater": "Buy now, Pay later", "Installment": "Installment", "VietQR": "VietQR"}, "list-trans-status": {"Label": "@:component-transaction-more-filter.list-trans-status.{0}", "All": "All", "AwaitTransactionResult": "Waiting for transaction result", "Successful": "Successful", "Failed": "Failed", "Processing": "Processing", "Incomplete": "Incomplete", "AwaitMerchantApproval": "Waiting for <PERSON>'s Approval", "MerchantRejected": "Merchant Rejected", "AwaitOnepayApprovval": "Waiting for OnePay's <PERSON><PERSON><PERSON><PERSON>", "OnepayRejected": "OnePay Rejected", "MerchantApproved": "Merchant Approved", "OnepayApproved": "OnePay Approved"}}, "component-app-data-table": {"dont-have-data": "No results found", "check-filer": "Sorry, we couldn't find any results for your search"}, "component-async-content": {"error": "Error!"}, "component-currency-setting": {"label": "Set the status to \"On\" for the currencies you allow for payment. The \"Auto\" type uses the Vietcombank exchange rate, updated at 09:00 AM every day", "auto": "Auto", "manual": "Manual", "placeholder": "Search currency code, currency name", "header": "Currency setting", "invalid-list-currency": "Invalid some currency data", "table": {"code": "Currency code", "name": "Currency name", "status": "Status", "update-type": "Update type", "exchange": "Exchange rate (to VND)", "last-update": "Last update", "update-by": "Updated by", "save": "Save changes"}}, "component-merchant-profiles": {"favorite": "Favorite", "all-profiles": "All profiles"}, "component-app-header": {"title": "OTA Payment", "profile-setting": "Profile settings", "logout": "Logout", "admin": "Admin"}, "component-app-left-menu": {"currency-setting": "Currency setting", "switch-profile": "Switch profile", "dashboard": "Dashboard", "payment-link": "Payment link", "trans-management": "Transaction management", "report": "Report"}, "default-layout": "Menu is loading...", "component-app": {"user-loading": "User data is loading...", "cannot-load-user": "User data cannot be loaded"}, "page-Transaction-Management": {"page-title": "Today transactions", "button-download": "More transactions", "all-trans": {"date": "Date", "trans-type": "Transaction type", "more-filter": "More filters", "clear-filter": "Clear filter", "list-trans-type": {"Label": "@:page-Transaction-Management.all-trans.list-trans-type.{0}", "All": "All", "Authorize": "Authorize", "Capture": "Capture", "Purchase": "Purchase", "Refund": "Refund", "RefundCapture": "Refund Capture", "RefundDispute": "Refund Dispute", "VoidAuthorize": "Void Authorize", "VoidCapture": "Void Capture", "VoidPurchase": "Void Purchase", "VoidRefund": "Void Refund", "VoidRefundCapture": "Void Refund Capture", "RequestRefund": "Request Refund", "PayLater": "Pay Later", "Void": "Void"}, "placeholder": "Search", "tab-title": "All transactions"}, "refund": {"created-date": "Created date", "refund-type": "Refund type", "refund-status": "Refund status", "clear-filter": "Clear filter", "list-refund-type": {"Label": "@:page-Transaction-Management.refund.list-refund-type.{0}", "All": "All", "Refund": "Refund", "RefundCapture": "Refund Capture", "RefundDispute": "Refund Dispute", "VoidRefund": "Void Refund", "RequestRefund": "Request Refund", "VoidRefundCapture": "Void Refund Capture"}, "list-trans-status": {"Label": "@:page-Transaction-Management.refund.list-trans-status.{0}", "All": "All", "AwaitTransactionResult": "Waiting for transaction result", "Successful": "Successful", "Failed": "Failed", "Processing": "Processing", "Incomplete": "Incomplete", "AwaitMerchantApproval": "Waiting for <PERSON>'s Approval", "MerchantRejected": "Merchant Rejected", "AwaitOnepayApprovval": "Waiting for OnePay's <PERSON><PERSON><PERSON><PERSON>", "OnepayRejected": "OnePay Rejected", "MerchantApproved": "Merchant Approved", "OnepayApproved": "OnePay Approved"}, "placeholder": "Search", "tab-title": "Refund", "column-header": {"trans-ref": "Transaction reference", "order-ref": "Order reference", "amount": "Amount", "note": "Note", "payment-date": "Payment date", "card-type": "Card type", "card-number": "Card number", "operator": "Operator", "status": "Status", "response-code": "Response code", "trans-id": "Payment ID", "link-name": "Link name", "created-date": "Created date", "created-by": "Created by", "refund-type": "Refund type"}, "buttons": {"approve": "Approve", "reject": "Reject"}, "actions-message": {"approve": "Are you sure you want to approve {number} refund requests?", "reject": "Are you sure you want to reject {number} refund requests?"}}, "total-trans-number": "Total Transaction number:", "total-trans-amount": "Total Transaction amount:", "export-file": {"title": "Exporting files...", "running-export": "The file is exporting. Please proceed to the File Management feature to download"}, "cannot-download-file": "The file is exporting. Please proceed to the File Management feature to download"}, "page-Transaction-Detail": {"page-title": "Transaction details", "header": {"title": "Transaction details", "actions": {"void": "Void", "refund": "Refund", "capture": "Capture", "approve": "Approve", "reject": "Reject"}}, "content": {"date": "Date", "order-ref": "Order Reference", "amount": "Amount", "payment-amount": "Payment Amount", "order-amount": "Order Amount", "total-amount": "Total Amount", "exchange-rate": "Exchange Rate", "capture-amount": "Capture Amount", "refund-amount": "Available to Refund", "res-code": "Response Code", "trans-state": "Transaction State", "customer-name": "Customer Name", "customer-email": "Customer <PERSON><PERSON>", "customer-phone": "Customer Mobile", "customer-address": "Customer Address", "customer-note": "Custom Field", "custom-field": "Custom Field", "merchant-name": "Merchant Profile name", "link-name": "Link name"}, "history": {"title": "Transaction history", "column-header": {"trans-id": "Payment ID", "date": "Date", "tran-type": "Transaction Type", "amount": "Amount", "status": "Status", "description": "Description"}}, "dialog-actions": {"void": {"title": "Void transaction", "message": "Are you sure you want to void this transaction?", "actions": {"cancel": "Cancel", "confirm": "Confirm"}, "toast": {"void-purchase": "Void Purchase transaction has been created successfully", "void-authorize": "Void Authorize transaction has been created successfully", "void-capture": "Void Capture transaction has been created successfully", "void-refund-capture": "Void Refund Capture transaction has been created successfully", "void-refund": "Void Refund transaction has been created successfully", "default": "Void transaction has been created successfully", "erorr": "An error occurred"}}, "approve": {"title": "Approve refund", "message": "Are you sure you want to approve this refund transaction?", "actions": {"cancel": "Cancel", "confirm": "Confirm"}, "toast": {"success": "Approved successfully", "erorr": "An error occurred", "number-success": "Approved {number}/{total} records successfully"}}, "reject": {"title": "Reject refund", "message": "Are you sure you want to reject this refund transaction?", "actions": {"cancel": "Cancel", "confirm": "Confirm"}, "toast": {"success": "Rejected successfully", "erorr": "An error occurred", "number-success": "Rejected {number}/{total} records successfully"}}, "capture": {"title": "Capture transaction", "available-to-capture": "Available to capture", "capture-amount": "Capture amount", "actions": {"cancel": "Cancel", "confirm": "Do capture"}, "toast": {"success": "Capture transaction has been created successfully", "erorr": "An error occurred"}}, "refund": {"title": "Refund transaction", "available-to-refund": "Available to Refund", "refund-amount": "Refund amount", "actions": {"cancel": "Cancel", "confirm": "Do refund"}, "toast": {"success": "Refund request has been created successfully", "erorr": "An error occurred"}}}}, "component-one-time-link": {"header": "One-time link", "content": {"link-name": "Link Name", "amount": "Amount", "description": "Description", "note": {"one-time-link": "A one-time link accepts only one successful payment.", "more-advanced": "For more advanced settings,", "action": "click here."}}, "footer": "Create link"}, "payment-link-type": {"Label": "@:payment-link-type.{0}", "All": "All", "MultiUse": "Multi-use link", "OneTime": "One-time link"}, "page-ReportManagement": {"page-title": "Transaction Report", "filter": {"date": "Date", "time-interval": "Time interval", "link-type": "Link type", "clear-filter": "Clear filter", "list-time-interval": {"Label": "@:page-ReportManagement.filter.list-time-interval.{0}", "Daily": "Daily", "Weekly": "Weekly", "Monthly": "Monthly"}, "list-link-type": {"Label": "@:page-ReportManagement.filter.list-link-type.{0}", "All": "All", "MyLink": "MyLink", "PaymentLink": "Payment link"}, "link-name": "Link Name", "placeholder": "Search Link name"}, "datatable": {"column": {"date": "Date", "purchase": "No. of Purchase Trans", "authorize": "No. of Authorize Trans", "capture": "No. of Capture Trans", "voi-refund": "No. of Refund/Void Trans", "total-revenue": "Total Revenue", "total-refund": "Total Refund"}, "row": {"total": "TOTAL"}}}, "file-upload-massage": {"error": {"limit-file-size": "The file exceeds the size limit 10Mb", "invalid-file-fomat": "File invalid format"}}, "format-payment-method-transaction-managerment": {"international-card": "International card", "domestic-card": "Domestic card", "app": "Mobile app", "shopee-pay": "Mobile app", "union-pay": "Mobile app", "smart-pay": "Mobile app", "bnpl": "Buy now, Pay later", "installment": "Installment", "viet-qr": "VietQR"}, "file-name-too-long": "File name too long, maximum 200 characters"}