// import { defineStore, storeToRefs } from "pinia"
// import { computed, reactive, ref, watch } from "vue"
// import { useAuthService } from "../services/authService"
// import { uniqueId } from "lodash-es"
// import APPCONFIG from "@/app.config"

// const PrefixKey = "paymentlink"

// interface UserSettingsModel {
//   leftMenuCollapsed: boolean
//   merchantProfileId: string | undefined
// }

// function defaultUserSetting(model?: UserSettingsModel): UserSettingsModel {
//   const defaultValue: UserSettingsModel = {
//     leftMenuCollapsed: false,
//     merchantProfileId: undefined,
//   }
//   if (model) {
//     Object.assign(model, defaultValue)
//   }
//   return defaultValue
// }

// export const useLocalUserSettingStore = defineStore(uniqueId("localUserSettingStore-"), () => {
//   const authService = useAuthService()

//   const { authUserId } = storeToRefs(authService)

//   const setting = ref<ReturnType<typeof useLocalStorageStore>>()
//   const userSetting = ref<ReturnType<typeof useLoginUserLocalStorageStore>>()

//   const locale = computed({
//     get() {
//       return setting.value?.locale
//     },
//     set(value) {
//       if (setting.value) setting.value.locale = value
//     },
//   })

//   const leftMenuCollapsed = computed({
//     get() {
//       return userSetting.value?.leftMenuCollapsed
//     },
//     set(value) {
//       userSetting.leftMenuCollapsed = value
//     },
//   })

//   const merchantProfileId = computed({
//     get() {
//       return userSetting.merchantProfileId
//     },
//     set(value) {
//       userSetting.merchantProfileId = value
//     },
//   })

//   return {
//     authUserId,
//     userSetting,
//     // settings
//     locale,
//     leftMenuCollapsed,
//     merchantProfileId,
//   }
// })

// /**
//  * Lưu trữ các setting vào LocalStorage
//  */
// export const useLocalStorageStore = defineStore(uniqueId("LocalStorageStore-"), () => {
  
//   const locale = ref<string | undefined>(localStorage.getItem(`${PrefixKey}-lang`) || undefined)
//   watch(locale, (locale) => localStorage.setItem(`${PrefixKey}-lang`, locale || ""))
  
//   return {
//     locale,
//   }
// })

// /**
//  * Lưu trữ các setting của loginuser vào LocalStorage
//  */
// export const useLoginUserLocalStorageStore = (authUserId: uuid) => {
//   return defineStore(uniqueId(`LoginUserLocalStorageStore-${authUserId}-`), () => {
//     const userSetting = reactive<UserSettingsModel>(defaultUserSetting())

//     loadUserSettingData()

//     watch(
//       userSetting,
//       () => {
//         updateUserSettingData()
//       },
//       {
//         deep: true,
//       }
//     )

//     function loadUserSettingData() {
//       const userId = authUserId
//       const strData = localStorage.getItem(`${PrefixKey}-usersettings-${userId}`)
//       if (strData) {
//         const jsonData: UserSettingsModel = JSON.parse(strData)
//         Object.assign(userSetting, jsonData)
//         console.log("loaded user setting", userId, jsonData)
//       }
//     }

//     function updateUserSettingData() {
//       const userId = authUserId
//       const jsonData: UserSettingsModel = userSetting
//       const strData = JSON.stringify(jsonData)
//       localStorage.setItem(`${PrefixKey}-usersettings-${userId}`, strData)
//       console.log("updated user setting", userId, jsonData)
//     }

//     return {
//       leftMenuCollapsed: userSetting.leftMenuCollapsed,
//       merchantProfileId: userSetting.merchantProfileId,
//     }
//   })
// }
