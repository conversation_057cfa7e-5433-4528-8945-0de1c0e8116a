import { defineStore, storeToRefs } from "pinia"
import { computed, reactive, ref, watch } from "vue"
import { useAuthService } from "../services/authService"
import { uniqueId } from "lodash-es"

const PrefixKey = "paymentlink"

interface UserSettingsModel {
  leftMenuCollapsed: boolean
  merchantProfileId: string | undefined
}

function defaultUserSetting(model?: UserSettingsModel): UserSettingsModel {
  const defaultValue: UserSettingsModel = {
    leftMenuCollapsed: false,
    merchantProfileId: undefined,
  }
  if (model) {
    Object.assign(model, defaultValue)
  }
  return defaultValue
}

export const useLocalUserSettingStore = defineStore(uniqueId("localUserSettingStore-"), () => {
  const authService = useAuthService()

  const { authUserId } = storeToRefs(authService)

  const userSetting = reactive<UserSettingsModel>(defaultUserSetting())

  const locale = ref<string | undefined>(localStorage.getItem(`${PrefixKey}-locale`) || undefined)
  watch(locale, (locale) => localStorage.setItem(`${PrefixKey}-locale`, locale || ""))

  const leftMenuCollapsed = computed({
    get() {
      return userSetting.leftMenuCollapsed
    },
    set(value) {
      userSetting.leftMenuCollapsed = value
    },
  })

  const merchantProfileId = computed({
    get() {
      return userSetting.merchantProfileId
    },
    set(value) {
      userSetting.merchantProfileId = value
    },
  })

  watch(
    authUserId,
    (authUserId) => {
      loadUserSettingData(authUserId)
    },
    {
      immediate: true,
    }
  )

  watch(
    userSetting,
    () => {
      updateUserSettingData()
    },
    {
      deep: true,
    }
  )

  function loadUserSettingData(userId?: string) {
    userId ??= authUserId.value
    const strData = localStorage.getItem(`${PrefixKey}-usersettings-${userId}`)
    if (strData) {
      const jsonData: UserSettingsModel = JSON.parse(strData)
      Object.assign(userSetting, jsonData)
      console.log("loaded user setting", userId, jsonData)
    }
  }

  function updateUserSettingData() {
    const userId = authUserId.value
    const jsonData: UserSettingsModel = userSetting
    const strData = JSON.stringify(jsonData)
    localStorage.setItem(`${PrefixKey}-usersettings-${userId}`, strData)
    console.log("updated user setting", userId, jsonData)
  }

  return {
    authUserId,
    userSetting,
    // settings
    locale,
    leftMenuCollapsed,
    merchantProfileId,
  }
})
