import { useGlobalCookies } from "@/plugins/cookies"
import { uniqueId } from "lodash-es"
import { defineStore } from "pinia"
import { ref, watch } from "vue"

const PrefixKey = "paymentlink"

/**
 * <PERSON><PERSON><PERSON> tr<PERSON> các setting vào Cookies
 */
export const useCookiesStorageStore = defineStore(uniqueId("CookiesStorageStore-"), () => {
  const cookies = useGlobalCookies()
  
  const locale = ref<string | undefined>(cookies.get(`${PrefixKey}-lang`) || undefined)
  watch(locale, (locale) => cookies.set(`${PrefixKey}-lang`, locale || ""))

  return {
    locale,
  }
})
