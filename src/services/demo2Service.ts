import { getUsers as apiGetUsers, getUserById as apiGetUserById } from "@/apis/demo2"
import { RootObject, User } from "@/types/demo2"
import { AxiosError } from "axios"
import { uniqueId } from "lodash-es"
import { defineStore } from "pinia"
import { ref, reactive } from "vue"
import { useTask, useAsyncTask, TaskInstance } from "vue-concurrency"

export type Demo2Service = ReturnType<typeof useDemo2Service>

export const useDemo2Service = defineStore(uniqueId("demo2Store-"), () => {
  const users = ref<User[]>([])

  const getUsersTask = useAsyncTask(async (signal, input: object) => {
    const { data, status } = await apiGetUsers(input)

    users.value = data.users
    return data
  })

  function getUsers(input: object): TaskInstance<RootObject> {
    return getUsersTask.perform(input)
  }

  //getUsersTask.last?.value

  return {
    users,
    getUsersTask,
    getUsers,
  }
})

