import { defineStore } from "pinia"
import { TaskInstance, useAsyncTask } from "vue-concurrency"
import {
  ApproveRejectTransactionRequest,
  BnplRequestRefund,
  BnplRequestVoid,
  CaptureRequest,
  ExportRefundInput,
  ExportTransactionsInput,
  GetListRefundInput,
  GetListRefundOutput,
  GetListTransInput,
  GetListTransOutput,
  ListTransaction,
  RefundRequest,
  TranStatus,
  VoidRequest }
from "@/apis/transactionApi"
import { transactionApi } from "@/apis"
import { EnumTransactionStatus, EnumTransactionType, EnumTransManagermentPaymentMethod } from "@/app.const"
import { uniqueId } from "lodash-es"
import { useGlobalI18n } from "@/plugins/i18n"

export const useTransactionService = defineStore(uniqueId("transactionService-"), () => {
  const { t } = useGlobalI18n()

  const getListAllTransTask = useAsyncTask(async (signal, input: GetListTransInput) => {
    const { data } = await transactionApi.getListAllTrans(input);

    return data
  }).keepLatest()

  const getDetailTask = useAsyncTask(async(signal, transId: string) => {
    const { data } = await transactionApi.detailTrans(transId, { handleErrorManually: true });

    return data
  }).keepLatest()

  const getListRefundTask = useAsyncTask(async(signal, input: GetListRefundInput) => {
    const { data } = await transactionApi.getListRefund(input);

    return data
  }).keepLatest()

  const refundTransactionTask = useAsyncTask(
    async (
      signal,
      transType: string,
      request: RefundRequest,
      bnblRequest: BnplRequestRefund
    ) => {
      const response = await transactionApi.refundTransaction(transType, request, bnblRequest)

      return response ? response.data : null
    }
  ).keepLatest()

  const voidTransactionTask = useAsyncTask(
    async (
      signal,
      transType: string,
      request: VoidRequest,
      bnplReuquest: BnplRequestVoid,
      transactionTypeMic?: number
    ) => {
      const response = await transactionApi.voidTransaction(transType, request, bnplReuquest, transactionTypeMic)

      return response ? response.data : null
    }
  ).keepLatest()

  const captureTransactionTask = useAsyncTask(async (signal, transType: string, request: CaptureRequest) => {
    const response = await transactionApi.captureTransaction(transType, request)

    return response ? response.data : null
  }).keepLatest()

  const getListAllTrans = (input: GetListTransInput): TaskInstance<ListTransaction> => {
    return getListAllTransTask.perform(input);
  }

  const getListRefund = (input: GetListRefundInput): TaskInstance<GetListRefundOutput> => {
    return getListRefundTask.perform(input);
  }

  const exportTransactionsTask = useAsyncTask(async (signal, input: ExportTransactionsInput) => {
    await transactionApi.exportTransactions(input)
  }).keepLatest()

  const exportRefundTask = useAsyncTask(async (signal, input: ExportRefundInput) => {
    await transactionApi.exportTransactionsRefund(input)
  }).keepLatest()

  const approveRejectTask = useAsyncTask(async (signal, input: ApproveRejectTransactionRequest[]) => {
    const { data } = await transactionApi.approveRejectTransaction(input, {
      handleErrorManually: true
    })

    return data;
  }).keepLatest()

  const formatTranStatus = (status: number): TranStatus => {
    const result: TranStatus = {
      label: '',
      class: ''
    }

    switch (status) {
      case EnumTransactionStatus.AwaitTransactionResult:
        result.label = t("component-transaction-more-filter.list-trans-status.AwaitTransactionResult")
        result.class = "text-transaction-await-transaction-result"
        break
      case EnumTransactionStatus.Successful:
        result.label = t("component-transaction-more-filter.list-trans-status.Successful")
        result.class = "text-transaction-successful"
        break
      case EnumTransactionStatus.Failed:
        result.label = t("component-transaction-more-filter.list-trans-status.Failed")
        result.class = "text-transaction-failed"
        break
      case EnumTransactionStatus.Processing:
        result.label = t("component-transaction-more-filter.list-trans-status.Processing")
        result.class = "text-transaction-processing"
        break
      case EnumTransactionStatus.Incomplete:
        result.label = t("component-transaction-more-filter.list-trans-status.Incomplete")
        result.class = "text-transaction-incomplete"
        break
      case EnumTransactionStatus.AwaitMerchantApproval:
        result.label = t("component-transaction-more-filter.list-trans-status.AwaitMerchantApproval")
        result.class = "text-transaction-await-merchant-approval"
        break
      case EnumTransactionStatus.MerchantRejected:
        result.label = t("component-transaction-more-filter.list-trans-status.MerchantRejected")
        result.class = "text-transaction-merchant-rejected"
        break
      case EnumTransactionStatus.AwaitOnepayApprovval:
        result.label = t("component-transaction-more-filter.list-trans-status.AwaitOnepayApprovval")
        result.class = "text-transaction-await-onepay-approvval"
        break
      case EnumTransactionStatus.OnepayRejected:
        result.label = t("component-transaction-more-filter.list-trans-status.OnepayRejected")
        result.class = "text-transaction-onepay-rejected"
        break
      case EnumTransactionStatus.MerchantApproved:
        result.label = t("component-transaction-more-filter.list-trans-status.MerchantApproved")
        result.class = "text-transaction-merchant-approved"
        break
      case EnumTransactionStatus.OnepayApproved:
        result.label = t("component-transaction-more-filter.list-trans-status.OnepayApproved")
        result.class = "text-transaction-onepay-approved"
        break
      case 0:
        result.label = "-"
        result.class = "text-transaction-Null"
        break
      default:
        result.label = `${status}-Unknown`
        result.class = "text-unknown"
        break
    }

    return result
  }

  const formatTranType = (transType: number): string => {
    switch (transType) {
      case EnumTransactionType.Authorize:
        return t("page-Transaction-Management.all-trans.list-trans-type.Authorize")
      case EnumTransactionType.Capture:
        return t("page-Transaction-Management.all-trans.list-trans-type.Capture")
      case EnumTransactionType.Purchase:
        return t("page-Transaction-Management.all-trans.list-trans-type.Purchase")
      case EnumTransactionType.Refund:
        return t("page-Transaction-Management.all-trans.list-trans-type.Refund")
      case EnumTransactionType.RefundCapture:
        return t("page-Transaction-Management.all-trans.list-trans-type.RefundCapture")
      case EnumTransactionType.RefundDispute:
        return t("page-Transaction-Management.all-trans.list-trans-type.RefundDispute")
      case EnumTransactionType.VoidAuthorize:
        return t("page-Transaction-Management.all-trans.list-trans-type.VoidAuthorize")
      case EnumTransactionType.VoidCapture:
        return t("page-Transaction-Management.all-trans.list-trans-type.VoidCapture")
      case EnumTransactionType.VoidPurchase:
        return t("page-Transaction-Management.all-trans.list-trans-type.VoidPurchase")
      case EnumTransactionType.VoidRefund:
        return t("page-Transaction-Management.all-trans.list-trans-type.VoidRefund")
      case EnumTransactionType.VoidRefundCapture:
        return t("page-Transaction-Management.all-trans.list-trans-type.VoidRefundCapture")
      case EnumTransactionType.RequestRefund:
        return t("page-Transaction-Management.all-trans.list-trans-type.RequestRefund")
      case EnumTransactionType.PayLater:
        return t("page-Transaction-Management.all-trans.list-trans-type.PayLater")
      case EnumTransactionType.Void:
        return t("page-Transaction-Management.all-trans.list-trans-type.Void")
      case 0:
        return "-"
      default:
        return `${transType}-Invalid`
    }
  }

  const formatCololumnTable = (field: string): string => {
    switch (field) {
      case "trans-ref":
        return t("component-transaction-setting-column.content.trans-ref")
      case "order-ref":
        return t("component-transaction-setting-column.content.order-ref")
       case "amount":
        return t("component-transaction-setting-column.content.amount")
      case "note":
        return t("component-transaction-setting-column.content.note")
      case "payment-date":
        return t("component-transaction-setting-column.content.payment-date")
      case "card-type":
        return t("component-transaction-setting-column.content.card-type")
      case "card-number":
        return t("component-transaction-setting-column.content.card-number")
      case "operator":
        return t("component-transaction-setting-column.content.operator")
      case "status":
        return t("component-transaction-setting-column.content.status")
      case "response-code":
        return t("component-transaction-setting-column.content.response-code")
      default:
        return ""
    }
  }

  const formatPaymentMethod = (paymentMethod: string) => {
    paymentMethod = paymentMethod.trim()
    switch (paymentMethod) {
      case EnumTransManagermentPaymentMethod.InternationalCard:
        return t("format-payment-method-transaction-managerment.international-card")
      case EnumTransManagermentPaymentMethod.DomesticCard:
        return t("format-payment-method-transaction-managerment.domestic-card")
      case EnumTransManagermentPaymentMethod.App:
        return t("format-payment-method-transaction-managerment.app")
      case EnumTransManagermentPaymentMethod.ShopeePay:
        return t("format-payment-method-transaction-managerment.shopee-pay")
      case EnumTransManagermentPaymentMethod.UnionPay:
        return t("format-payment-method-transaction-managerment.union-pay")
      case EnumTransManagermentPaymentMethod.SmartPay:
        return t("format-payment-method-transaction-managerment.smart-pay")
      case EnumTransManagermentPaymentMethod.BNPL:
        return t("format-payment-method-transaction-managerment.bnpl")
      case EnumTransManagermentPaymentMethod.Installment:
        return t("format-payment-method-transaction-managerment.installment")
      case EnumTransManagermentPaymentMethod.VietQR:
        return t("format-payment-method-transaction-managerment.viet-qr")
      case "":
        return "-"
      default:
        return paymentMethod
    }
  }

  const formatToastVoidAction = (transType: number) => {
    switch (transType) {
      case EnumTransactionType.Purchase:
        return "void-purchase"
      case EnumTransactionType.Authorize:
        return "void-authorize"
      case EnumTransactionType.Capture:
        return "void-capture"
      case EnumTransactionType.RefundCapture:
        return "void-refund-capture"
      case EnumTransactionType.Refund:
        return "void-refund"
      default:
        return "default"
    }
  }

  return {
    getListAllTransTask,
    getListAllTrans,
    getDetailTask,
    formatTranStatus,
    formatTranType,
    getListRefundTask,
    getListRefund,
    refundTransactionTask,
    voidTransactionTask,
    captureTransactionTask,
    exportTransactionsTask,
    approveRejectTask,
    formatCololumnTable,
    exportRefundTask,
    formatPaymentMethod,
    formatToastVoidAction,
  }
})
