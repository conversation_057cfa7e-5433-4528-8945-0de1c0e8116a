import { defineStore } from "pinia"
import { ref } from "vue"
import { Router, RouteLocationNormalized } from "vue-router"

export const useRouterService = defineStore("routerService", () => {
  const router = ref<Router>()
  const currentRoute = ref<RouteLocationNormalized>()
  const previousRoute = ref<RouteLocationNormalized>()

  async function initService(routerInput: Router) {
    router.value = routerInput

    routerInput.beforeEach((to, from) => {
      // luôn có previousRoute bắt đầu từ /
      previousRoute.value = from
    })
    routerInput.afterEach((to, from) => {
      currentRoute.value = to
    })
  }

  return {
    currentRoute,
    previousRoute,
    initService,
  }
})