import { GetListReportRequest } from "@/apis/reportApi"
import { uniqueId } from "lodash-es"
import { defineStore } from "pinia"
import { useAsyncTask } from "vue-concurrency"
import { reportApi } from "@/apis"

export const useReportService = defineStore(uniqueId("repỏtService-"), () => {
  const getListReportTask = useAsyncTask(async (signal, request: GetListReportRequest) => {
    const { data } = await reportApi.getListReport(request)

    return data
  }).keepLatest();

  const getListLinkNameTask = useAsyncTask(async (signal, request: { dateFrom: datetime, dateTo: datetime }) => {
    const { data } = await reportApi.getListLinkName(request.dateFrom, request.dateTo);

    return data;
  }).keepLatest();

  const exportSumaryTask = useAsyncTask(async (signal, input: GetListReportRequest) => {
    await reportApi.exportSumary(input)
  }).keepLatest()

  return {
    getListReportTask,
    getListLinkNameTask,
    exportSumaryTask,
  }
})
