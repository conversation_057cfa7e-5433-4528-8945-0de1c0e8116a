import { paymentLinkApi } from "@/apis"
import type { PaymentMethods } from "@/types/apis"
import { uniqueId } from "lodash-es"
import { defineStore } from "pinia"
import { ref } from "vue"
import { TaskInstance, useAsyncTask } from "vue-concurrency"

export const usePaymentCommonService = defineStore(uniqueId("paymentCommonService-"), () => {

  const listPaymentMethod = ref<PaymentMethods[]>([])

  const getAllPaymentMethodTask = useAsyncTask(async (signal, forceUpdate: boolean = false) => {
    if (!forceUpdate && listPaymentMethod.value.length > 0) {
      return listPaymentMethod.value
    }

    const { data } = await paymentLinkApi.getAllPaymentMethod()

    listPaymentMethod.value = data

    return data
  }).keepLatest()

  function getAllPaymentMethods(forceUpdate?: boolean): TaskInstance<PaymentMethods[]> {
    return getAllPaymentMethodTask.perform(forceUpdate)
  }

  return {
    listPaymentMethod,
    getAllPaymentMethodTask,
    getAllPaymentMethods,
  }
})

export type {
  PaymentMethods,
}