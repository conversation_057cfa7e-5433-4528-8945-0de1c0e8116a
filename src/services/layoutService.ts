import { defineStore, storeToRefs } from "pinia"
import { ref } from "vue"
import { useLocalUserSettingStore } from "@/stores/localUserSettingStore"
import { MaybeComputedRef, useHead } from "@vueuse/head"

export const useLayoutService = defineStore("layoutService", () => {
  const localUserSettingStore = useLocalUserSettingStore()
  const { leftMenuCollapsed } = storeToRefs(localUserSettingStore)

  const showLogoutWithouUser = ref(false)

  const layoutReloadKey = ref(0)
  
  function useHeadTitle(title: MaybeComputedRef<string> | Promise<MaybeComputedRef<string>>): void {
    useHead({
      title: title,
    })
  }

  function reload() {
    console.log("reload layout")
    layoutReloadKey.value++
  }

  return {
    leftMenuCollapsed,
    layoutReloadKey,
    showLogoutWithouUser,
    reload,
    useHeadTitle,
  }
})
