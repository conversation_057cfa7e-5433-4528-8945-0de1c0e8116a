import { auth<PERSON>pi, merchant<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, user<PERSON><PERSON> } from "@/apis"
import { LoginInput } from "@/apis/authApis"
import type { MerchantSimpleInfo, PermissionSimpleInfo, UserSimpleInfo } from "@/apis/userApis"
import APPCONFIG from "@/appConfig"
import { useGlobalCookies } from "@/plugins/cookies"
import { isAxiosError } from "axios"
import { defineStore, storeToRefs } from "pinia"
import { computed, nextTick, ref, watch } from "vue"
import { useAsyncTask, usePipeTask } from "vue-concurrency"
import { useRouter } from "vue-router"
import { useLayoutService } from "./layoutService"
import moment from "moment"
import { MerchantProfile, PinMerchantRequest } from "@/apis/types/merchantTypes"
import { useLocalUserSettingStore } from "@/stores/localUserSettingStore"
import { PermissionEnum } from "@/enums/permissions"
import { RoleSimpleInfo } from "@/apis/types/roleTypes"
import { enumToSelectListItem } from "@/utils/enumUtil"
import { AppRouteNames } from "@/enums/routers"
import { handleApiError } from "@/apis/https/httpApi"
import { useToastService } from "./toastService"
import { handleTaskError } from "@/utils/taskUtil"

export enum LoginModeEnum {
  DEV_MIC = "DEV+MIC",
  OP = "OP",
}
// Riêng authService ko sử dung uniqueId để có thể keep state khi reload trong devmode
//export const useAuthService = defineStore(uniqueId("authService-"), () => {
export const useAuthService = defineStore("authService", () => {
  const loginMode: LoginModeEnum = APPCONFIG.MODE_DEV_MIC ? LoginModeEnum.DEV_MIC : LoginModeEnum.OP

  const cookies = useGlobalCookies()
  const layoutService = useLayoutService()
  const router = useRouter()
  const loginLoopCount = ref(0)

  const isAuth = computed(() => !!authUserData.value)
  const isSysAdmin = computed(() => {
    const authUser = authUserData.value
    if (authUser && APPCONFIG.APP_USER_ADMIN.length > 0) {
      const email = authUser.email.toLowerCase()
      const isSysAdmin = APPCONFIG.APP_USER_ADMIN.any((u) => u.toLowerCase() == email)
      // Chỉ show log khi nó là user hardcode trong hệ thống
      isSysAdmin &&
        APPCONFIG.APP_USER_ADMIN_MAIN.any((u) => u.toLowerCase() == email) &&
        console.log("adm", APPCONFIG.APP_USER_ADMIN)
      return isSysAdmin
    }
    return false
  })
  const isNoMerchant = computed(() => authUserMerchantsList.value.length == 0)
  const isNoAppPermission = ref(false)

  const authUserId = ref<uuid>() // Get from auth cookies
  const authUserSId = ref<string>()
  const authUserEmail = ref<string>() // Get from auth cookies

  const authMerchantProfileId = ref<string>()
  watch(authMerchantProfileId, (authMerchantProfileId) => {
    const localUserSettingStore = useLocalUserSettingStore()
    const { userSetting } = storeToRefs(localUserSettingStore)
    userSetting.value.merchantProfileId = authMerchantProfileId
  })

  // Data of user
  const authUserData = ref<UserSimpleInfo>()
  const authUserMerchantsList = ref<MerchantSimpleInfo[]>([])
  const authUserPermissionsList = ref<PermissionSimpleInfo[]>([])
  const authUserRolesList = ref<RoleSimpleInfo[]>([])

  // Data of selected merchant
  const authMerchantProfileData = ref<MerchantProfile>()

  type VerifyLoginDataTaskResult = {
    success: boolean
    mode: LoginModeEnum
    errCode: number
    error: any | undefined
  }

  /**
   * Thực hiện check lại dữ liệu login mỗi khi vào page (authMiddleware).
   * Đản bảo để có được thông tin user và merchant khi vào trang.
   * Kiểm soát việc redirect đi đâu tiếp qua authMiddleware.
   */
  const verifyLoginDataTask = useAsyncTask<VerifyLoginDataTaskResult, any[]>(async (signal, input?: void) => {
    // Check login mode
    if (loginMode == LoginModeEnum.OP) {
      let errCode = 0
      let error: any | undefined
      // Xử lý Login ở môi trường OP
      try {
        // 1. Thực hiện call 1 api bất kỳ lên server OP
        // 1.2. MA-WEB tự chặn request để pass cookie auth thành x-user-id. Nếu false sẽ trả về 401.
        // 2. Thực hiện check nếu trả về status 401 thì redirect về login page của MA-WEB
        // 3. Thực hiện login

        const { data: dataGetUserByLogin, status: httpStatus } = await userApi.getUserByLogin({
          allowAnonymous: true,
          handleErrorManually: true,
        })

        console.log("verifyLoginDataTask", httpStatus, dataGetUserByLogin)

        if (httpStatus == 200 && dataGetUserByLogin.email) {
          const authUserEmail = dataGetUserByLogin.email

          // Thực hiện login lại
          const loginResult = await loginTask.perform({
            username: authUserEmail,
            password: "",
          })

          if (loginResult.success) {
            return {
              success: true,
              mode: LoginModeEnum.OP,
              errCode: 0,
              error: undefined,
            }
          }

          return {
            success: false,
            mode: LoginModeEnum.OP,
            errCode: -1,
            error: undefined,
          }
        } else if (httpStatus == 200) {
          throw new Error("Cannot get user info from OP Api.")
        } else {
          throw new Error(`Error OP Api get user info. Status: ${httpStatus}`)
        }
      } catch (ex) {
        error = ex

        if (isAxiosError(ex)) {
          console.error(
            `verifyLoginDataTask Error API.`,
            ex.response?.status || "-",
            ex.response?.statusText || "-",
            ex.message || "-"
          )
          if (ex.response?.status) errCode = ex.response?.status

          // 401 Unauthorized thì redirect về login page
          if (ex.response?.status == 401) {
            authUserData.value = undefined // -> isAuth = false
            // return để xử lý ở authMiddleware
            return {
              success: false,
              mode: LoginModeEnum.OP,
              errCode: errCode, // 401
              error: error,
            }
          }

          // 403 Forbidden thì show màn hình 403
          if (ex?.response?.status == 403) {
            isNoAppPermission.value = true
            // return để xử lý ở authMiddleware
            return {
              success: false,
              mode: LoginModeEnum.OP,
              errCode: errCode, // 403
              error: error,
            }
          }

          // show error as default
          handleApiError(ex)

          return {
            success: false,
            mode: LoginModeEnum.OP,
            errCode: errCode,
            error: error,
          }
        }
      }

      console.error(`verifyLoginDataTask Error Unknown.`, error)

      // show error as default
      const toastService = useToastService()
      toastService.error({})

      return {
        success: false,
        mode: LoginModeEnum.OP,
        errCode: -1, // Unknown
        error: error,
      }
    }

    if (loginMode == LoginModeEnum.DEV_MIC) {
      const cookies = useGlobalCookies()

      // check tự đăng nhập cho fake user
      if (cookies.hasKey("debugonly-auth-user")) {
        const loginEmail = cookies.get("debugonly-auth-user")

        // Thực hiện login lại
        const { success } = await loginTask.perform({
          username: loginEmail,
          password: "",
        })

        if (success) {
          return {
            success: true,
            mode: LoginModeEnum.DEV_MIC,
            errCode: 0,
            error: undefined,
          }
        }
      }

      return {
        success: false,
        mode: LoginModeEnum.DEV_MIC,
        errCode: -1, // Unknown
        error: undefined,
      }
    }

    throw new Error(`Unknown login mode. loginMode=${loginMode}`)
  })

  const loginTask = useAsyncTask(async (signal, input?: LoginInput) => {
    // FAKE DEV: lưu luôn thành username
    // TODO: Khối xử lý login+authMiddleware+router(LOGIN) đang bị cồng kềnh, cần xử lý lại.

    authUserEmail.value = input?.username || "<EMAIL>"

    if (loginLoopCount.value++ > 3) {
      throw new Error("Login cannot progress.")
    }

    // Load user + merchant
    await loadAuthUserData.perform(authUserEmail.value)

    loginLoopCount.value = 0

    // reload view
    layoutService.reload()

    // Đản bảo có isAuth
    await nextTick()

    if (isAuth.value == true) {
      if (loginMode == LoginModeEnum.OP) {
        // DO NOTHING ở môi trường OP. vì trên OP sẽ ko xử lý cái này mà bằng verifyLoginDataTask
      }

      if (loginMode == LoginModeEnum.DEV_MIC) {
        cookies.set("debugonly-auth-user", authUserEmail.value, {
          path: "/",
          expireTimes: moment().add(1, "hour").toDate(),
        })
      }

      // Redirect to returnUrl sau khi thành công
      if (input?.returnUrl) {
        await router.push({
          path: input.returnUrl || "/",
        })
      }
    }

    return {
      success: isAuth.value,
    }
  })
  handleTaskError(loginTask, "loginTask")

  const logoutTask = useAsyncTask(async (signal, input: void) => {
    if (loginMode == LoginModeEnum.OP) {
      // Xử lý Logout ở môi trường OP
      const { data, status } = await authApi.logout()
      console.log("logout OP", status, data)
    }

    if (loginMode == LoginModeEnum.DEV_MIC) {
      // Xử lý Logout ở môi trường MIC+DEV
      cookies.remove("debugonly-auth-user", {
        path: "/",
      })
      console.log("logout MIC")
    }

    // clear tất cả dữ liệu khác liên quan đến user
    authUserData.value = undefined
    isNoAppPermission.value = false

    const route = router.currentRoute.value
    // Xử lý trường hợp logout ở trang error như 401, 403, 404
    if (route.matched.any((m) => m.meta.isErrorPage == true)) {
      const returnUrl = route.query.returnUrl?.toString()
      // Nếu có returnUrl thì quay về returnUrl
      if (returnUrl) {
        await router.push({
          path: returnUrl,
        })
        router.go(0) // refresh all
        return
      }

      // Trường hợp còn lại thì đưa về home
      await router.push({
        name: AppRouteNames.HOME,
      })
      router.go(0) // refresh all
      return
    }

    router.go(0) // refresh lại trang để return lại về login page bằng authMiddleware
    return
  })

  //#region Tasks to loading all required login user data

  /**
   * Xử lý load dữ liệu user khi vào trang.
   */
  const loadAuthUserInfoTask = useAsyncTask(async (signal, userName: string) => {
    userName = userName || authUserEmail.value || ""

    if (!userName) {
      console.error("LoadLoginUser: User email is required.")
      throw Error("User email is required")
    }

    const { data, status } = await userApi.getUserByEmail(userName)

    authUserId.value = data.user.id
    authUserSId.value = data.user.userSId
    authUserEmail.value = data.user.email

    authUserData.value = data.user
    authUserMerchantsList.value = data.merchants ?? []
    authUserPermissionsList.value = data.permissions ?? []
    authUserRolesList.value = data.roles ?? []

    if (authUserMerchantsList.value.isEmpty()) {
      console.warn("User don't have any Merchant -> 403 NoMerchant")
      return data
    }

    // Filter out null/empty values and sort merchants with pinned ones at the top and alphabetically
    authUserMerchantsList.value = authUserMerchantsList.value
      .filter(merchant => merchant && merchant.merchant_name) // Filter out null/empty values
      .sort((a, b) => {
        // First sort by pin status (pinned items first)
        if (a.pin !== b.pin) {
          return a.pin ? -1 : 1;
        }
        // If both are pinned, sort alphabetically
        if (a.pin && b.pin) {
          return a.merchant_name.localeCompare(b.merchant_name);
        }
        // If both are unpinned, maintain original order
        return 0;
      });

    return data
  })
  //handleTaskError(loadAuthUserInfoTask, "loadAuthUserInfoTask")

  /**
   * Xử lý load dữ liệu merchant khi vào trang.
   * Trường hợp 1. sẽ xử lý sau khi execute loadAuthUserInfoTask.
   * @param input là kết quả trả về từ loadAuthUserInfoTask
   */
  const loadAuthUserMerchantTask = useAsyncTask(async (signal, input: void) => {
    // Đợi hoàn tất cập nhật lại authUserId+authUserMerchantsList trước khi xử lý
    await nextTick()

    let merchantProfileId = authMerchantProfileId.value

    if (!merchantProfileId) {
      const { userSetting } = useLocalUserSettingStore()
      merchantProfileId = userSetting.merchantProfileId
    }

    // Nếu ko tồn tại hoặc không nằm trong list quản lý thì load lại bằng first item.
    if (!merchantProfileId || !authUserMerchantsList.value.some((item) => item.id == merchantProfileId)) {
      const firstAuthMerchant = authUserMerchantsList.value[0]

      console.log("load Merchant by First ProfileId", firstAuthMerchant)
      merchantProfileId = firstAuthMerchant?.id

      // update ngược lại authMerchantProfileId
      authMerchantProfileId.value = merchantProfileId
    }

    if (!merchantProfileId) {
      console.warn("Cannot load any Merchant")
      return null
    }

    const data = await selectMerchantProfileTask.perform({ merchantId: merchantProfileId })

    return data
  })

  /**
   * Xử lý load dữ liệu cả user và merchant khi vào trang.
   */
  const loadAuthUserData = usePipeTask(loadAuthUserInfoTask, loadAuthUserMerchantTask)

  //#endregion

  const selectMerchantProfileTask = useAsyncTask(async (signal, input: { merchantId: string | undefined }) => {
    const { merchantId } = input

    if (!merchantId) {
      console.error("SelectMerchantProfile: The merchant SId must be defined.")
      throw Error("The merchant SId must be defined.")
    }

    if (merchantId == authMerchantProfileId.value && merchantId == authMerchantProfileData.value?.id) {
      return authMerchantProfileData.value || ({} as MerchantProfile)
    }

    const { data, status } = await merchantProfileApi.getMerchantProfileById(merchantId)

    authMerchantProfileData.value = data
    authMerchantProfileId.value = data.id

    layoutService.reload()

    return data
  })

  const pinMerchant = useAsyncTask(async (signal, request: PinMerchantRequest) => {
    await merchantProfileApi.pinMerchantProfile(request)
  })

  function hasPermission(permission: PermissionEnum) {
    return authUserPermissionsList.value.any((p) => p.name == permission)
  }

  function _verifyPermissionEnum() {
    const checks = authUserPermissionsList.value
    const array = enumToSelectListItem(PermissionEnum)
    for (let index = 0; index < array.length; index++) {
      const item = array[index]
      if (checks.any((p) => p.name == item.value) == false) {
        console.warn(`PermissionEnum ${item.label}:${item.value} is not found!`)
      }
    }
  }

  return {
    selectMerchantProfileTask,
    verifyLoginDataTask: verifyLoginDataTask,
    loginTask,
    logoutTask,
    //loadAuthUserInfo,
    //selectMerchantProfileByMerchanSId,
    isAuth,
    isSysAdmin,
    isNoMerchant,
    isNoAppPermission,
    authUserId,
    authUserSId,
    authUserEmail,
    authMerchantProfileId,
    authUserData,
    authUserMerchantsList,
    authUserPermissionsList,
    authUserRolesList,
    authMerchantProfile: authMerchantProfileData,
    pinMerchant,
    hasPermission,
  }
})

// // Định nghĩa type of State cho Store
// interface IAuthState {
//   _isAuthenticated: boolean
//   _jwtToken: string
//   _userName: string
//   _fullName: string
//   _email: string
// }

// // Định nghĩa Store
// export const useAuthService = defineStore("authStore", {
//   state: (): IAuthState => ({
//     _isAuthenticated: false,
//     _jwtToken: "",
//     _userName: "nghianl",
//     _fullName: "Nguyễn Lê Nghĩa",
//     _email: "<EMAIL>",
//   }),
//   getters: {
//     isAuthenticated(): boolean {
//       return this._isAuthenticated
//     },
//     userName(): string {
//       return this._userName
//     },
//     fullName(): string {
//       return this._fullName
//     },
//     email(): string {
//       return this._email
//     },
//   },
//   actions: {
//     login(jwtToken: string) {
//       this._isAuthenticated = true
//       this._jwtToken = jwtToken
//     },
//     logout() {
//       this.$reset()
//     },
//   },
// })
