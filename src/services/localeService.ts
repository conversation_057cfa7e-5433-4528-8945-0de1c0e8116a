import { AppLocaleNames } from "@/enums/locales"
import { useCookiesStorageStore } from "@/stores/cookiesStorageStore"
import { useLocalUserSettingStore } from "@/stores/localUserSettingStore"
import { verifyBrowserLocalesSupported } from "@/utils/localeUtil"
import { DefaultLocale, useGlobalI18n } from "@plugins/i18n"
import { uniqueId } from "lodash-es"
import { defineStore } from "pinia"
import { computed } from "vue"

export const LocaleCookiesKeyName = "paymentlink-locale"

export const useLocaleService = defineStore(uniqueId("localeService-"), () => {
  const { locale: i18nLocale, availableLocales: i18nAvailableLocales } = useGlobalI18n()
  const locale = computed(() => i18nLocale.value)
  const availableLocales = computed(() => i18nAvailableLocales)

  const userSetting = useLocalUserSettingStore()
  const cookiesStorageStore = useCookiesStorageStore()

  function initLocale() {
    const locale = localeResover()

    if (locale) {
      setLocale(locale)
    }
  }

  function verifySupported(locales: string[]): void {
    for (let idx = 0; idx < locales.length; idx++) {
      const locale = locales[idx]
      if (verifyBrowserLocalesSupported([...locale])) {
        throw Error(`The locale ${locale} is not supported.`)
      }
    }
  }

  function validateLocale(locale: string): boolean {
    return (<any>Object).values(AppLocaleNames).includes(locale)
  }

  function localeResover(): string {
    let locale: string | null = null

    // Load from user setting
    if (userSetting.locale) {
      locale = userSetting.locale

      if (locale && validateLocale(locale)) {
        console.log("Locale resoved by UserSetting", locale)
        return locale
      }
    }
    // Load from cookies
    if (cookiesStorageStore.locale) {
      locale = cookiesStorageStore.locale

      if (locale && validateLocale(locale)) {
        console.log("Locale resoved by CookiesSetting", locale)
        return locale
      }
    }
    // Load from current browser lang
    if (globalThis.navigator.language) {
      locale = globalThis.navigator.language

      if (locale && validateLocale(locale)) {
        console.log("Locale resoved by BrowserSetting", locale)
        return locale
      }
    }

    locale = DefaultLocale
    console.log("Locale resoved by Default", locale)
    return locale
  }

  function setLocale(locale: string) {
    i18nLocale.value = locale
    userSetting.locale = locale
    cookiesStorageStore.locale = locale
    document.querySelector("html")?.setAttribute("lang", locale)
  }

  return {
    locale,
    availableLocales,
    setLocale,
    initLocale,
    verifySupported,
  }
})
