import { PresetAmountJson } from "@/types/apis"
import { roundCurrency } from "@/utils/formatUtil"

/**
 * <PERSON>àm hỗ trợ tính toán và hiển thị thông tin PaymentAmount dựa trên tham số presetAmountJson.
 * 
 * Cần đồng nhất ở cả MerchantWeb + ClientWeb.
 */
export function caculatePaymentAmount(input: {
  presetAmountJson: Nullable<PresetAmountJson>
  otherInfo: Nullable<{
    currency: Nullable<string>
    exchangeRate: Nullable<number>
    paymentCurrency: Nullable<string>
  }>
}): CaculatePaymentAmountResult {
  const { presetAmountJson, otherInfo } = input

  // Trường hợp không có presetAmountJson
  if (!presetAmountJson) {
    return {
      enable: false,
      amount: undefined,
      currency: otherInfo?.currency ?? undefined,
      exchangeRate: otherInfo?.exchangeRate ?? undefined,
      paymentAmount: undefined,
      paymentCurrency: otherInfo?.paymentCurrency ?? undefined,
      totalAmount: undefined,

      enableDiscount: false,
      discount: undefined,
      discountIsPercent: undefined,
      discountResult: undefined,

      enableTax: false,
      tax: undefined,
      taxResult: undefined,

      enableOtherFee: false,
      otherFeeLabel: undefined,
      otherFee: undefined,
      otherFeeIsPercent: undefined,
      otherFeeResult: undefined,
    }
  }

  const {
    amount,
    enableDiscount,
    discount,
    discountIsPercent,
    enableTax,
    tax,
    enableOtherFee,
    otherFee,
    otherFeeIsPercent,
    otherFeeLabel,
  } = presetAmountJson
  // WARN: Công thức sau được lặp lại tương ứng ở
  // - MerchantWeb: LinkDetail + AddPaymentAmount
  // - ClientWeb: paymentLinkViewStore + paymentLinkViewResultStore
  const currency = otherInfo?.currency
  const exchangeRate = otherInfo?.exchangeRate ?? 0
  const paymentCurrency = otherInfo?.paymentCurrency

  let discountResult = !amount || !discount ? 0 : discountIsPercent ? (amount * discount) / 100 : discount
  //let discountResult = !amount || !discount ? 0 : discountIsPercent ? new Decimal(amount).times(discount).dividedBy(100).toNumber() : discount
  discountResult = discountResult < 0 ? 0 : roundCurrency(discountResult, currency)

  let taxResult = !amount || !tax ? 0 : ((amount - discountResult) * tax) / 100
  //let taxResult = !amount || !tax ? 0 : new Decimal(amount).minus(discountResult).times(tax).dividedBy(100).toNumber()
  taxResult = taxResult < 0 ? 0 : roundCurrency(taxResult, currency)

  let otherFeeResult =
    !amount || !otherFee ? 0 : otherFeeIsPercent ? ((amount - discountResult + taxResult) * otherFee) / 100 : otherFee
  //!amount || !otherFee ? 0 : otherFeeIsPercent ? new Decimal(amount).minus(discountResult).plus(taxResult).times(otherFee).dividedBy(100).toNumber() : otherFee
  otherFeeResult = otherFeeResult < 0 ? 0 : roundCurrency(otherFeeResult, currency)

  let totalAmountResult = !amount ? 0 : amount - discountResult + taxResult + otherFeeResult
  //let totalAmountResult = new Decimal(amount).minus(discountResult).plus(taxResult).plus(otherFeeResult).toNumber()
  totalAmountResult = totalAmountResult < 0 ? 0 : roundCurrency(totalAmountResult, currency)

  let paymentAmountResult = !exchangeRate ? 0 : totalAmountResult * exchangeRate
  //let paymentAmountResult = new Decimal(totalAmountResult).times(exchangeRate).toNumber()
  paymentAmountResult = paymentAmountResult < 0 ? 0 : roundCurrency(paymentAmountResult, paymentCurrency)

  return {
    enable: true,
    amount: amount ?? undefined,
    currency: currency ?? undefined,
    totalAmount: totalAmountResult ?? undefined,
    exchangeRate: exchangeRate ?? undefined,
    paymentAmount: paymentAmountResult ?? undefined,
    paymentCurrency: paymentCurrency ?? undefined,

    enableDiscount: enableDiscount,
    discount: discount ?? undefined,
    discountIsPercent: discountIsPercent ?? undefined,
    discountResult: discountResult ?? undefined,

    enableTax: enableTax,
    tax: tax ?? undefined,
    taxResult: taxResult ?? undefined,

    enableOtherFee: enableOtherFee,
    otherFeeLabel: otherFeeLabel ?? undefined,
    otherFee: otherFee ?? undefined,
    otherFeeIsPercent: otherFeeIsPercent ?? undefined,
    otherFeeResult: otherFeeResult ?? undefined,
  }
}

export interface CaculatePaymentAmountResult {
  enable: boolean

  amount: number | undefined
  currency: string | undefined
  totalAmount: number | undefined
  exchangeRate: number | undefined
  paymentAmount: number | undefined
  paymentCurrency: string | undefined

  enableDiscount: boolean
  discount: number | undefined
  discountIsPercent: boolean | undefined
  discountResult: number | undefined

  enableTax: boolean
  tax: number | undefined
  taxResult: number | undefined

  enableOtherFee: boolean
  otherFeeLabel: string | undefined
  otherFee: number | undefined
  otherFeeIsPercent: boolean | undefined
  otherFeeResult: number | undefined
}
