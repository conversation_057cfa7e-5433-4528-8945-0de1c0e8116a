import { computed, reactive, ref, watch } from "vue"
import { paymentLinkApi } from "@/apis"
import type {
  ExportPaymentLinkInput,
  ExportPaymentLinkTransInput,
  GetByIdPaymentLinkOutput,
  GetListPaymentLinkInput,
  GetListPaymentLinkItem,
  GetListPaymentLinkOutput,
  GetListPaymentLinkTransInput,
  GetListPaymentLinkTransItem,
  GetListPaymentLinkTransOutput,
  PinPaymentLinkInput,
  SetEnableStatusInput,
} from "@/apis/paymentLinkApi"
import { defineStore } from "pinia"
import { TaskInstance, useAsyncTask } from "vue-concurrency"
import { EnumPaymentLinkStatus, EnumPaymentLinkType, EnumTransactionStatus, EnumTransactionType } from "@/app.const"
import type { PaymentMethods, PaymentLinkRequest, CheckIsExit } from "@/types/apis"
import APPCONFIG from "@/appConfig"
import type { Rules } from "async-validator"
import { until, useDebounceFn } from "@vueuse/core"
import { useAsyncValidator } from "@vueuse/integrations/useAsyncValidator"
import { useGlobalI18n } from "@/plugins/i18n"
import { uniqueId } from "lodash-es"
import { saveAs } from "@/utils/fileUtil"

type AddEditLinkForm = {
  linkId: uuid | null
  name: string
  routerUri: string
  routerUriOld: string
  isStopAutoGenUri: boolean
}

export const usePaymentLinkService = defineStore(uniqueId("paymentLinkService-"), () => {
  const { t } = useGlobalI18n()

  const getOrSetDefaultForm = (input?: AddEditLinkForm): AddEditLinkForm => {
    const DefaultData = {
      linkId: null,
      name: "",
      routerUri: "",
      routerUriOld: "",
      isStopAutoGenUri: false,
    } as AddEditLinkForm
    // Xử lý nếu là get
    if (typeof input == "undefined") {
      return DefaultData
    }
    // Xử lý nếu là set
    for (const [key, value] of Object.entries(DefaultData)) {
      input[key] = value
    }
    return input
  }
  const form = reactive<AddEditLinkForm>(getOrSetDefaultForm())

  const rules = computed<Rules>(() => {
    return {
      name: [
        {
          required: true,
          message: () => t("components-paymentLink-LinkInfomation.validate-input.required-name"),
        },
      ],
      routerUri: [
        {
          required: true,
          message: () => t("components-paymentLink-LinkInfomation.validate-input.required-url"),
        },
        {
          asyncValidator: useDebounceFn(async (rule, value, callback, source) => {
            if (!value || value.length < 0) return
            // so sánh thay đổi trước và sau
            if (source.routerUriOld?.toLowerCase() === value.toLowerCase()) return Promise.resolve()

            const request: CheckIsExit = {
              routerUri: value,
              orderId: source.linkId,
            }

            //const isExis = (await checkExitUriTask.perform(request)).exist
            const checkExitUriTaskRes = await checkExitUriTask.perform(request)

            const isExis = checkExitUriTaskRes.exist

            return isExis
              ? Promise.reject(t("components-paymentLink-LinkInfomation.validate-input.existed-url"))
              : Promise.resolve()
          }, 1000),
        },
        {
          pattern: /[a-zA-Z0-9_-]+/gi,
          message: () => t("components-paymentLink-LinkInfomation.validate-input.invalid-url"),
        },
      ],
    }
  })

  const formValidator = useAsyncValidator(form, rules, {
    immediate: false,
    //validateOption: {
    //  first: true, // thằng này thì chỉ báo lỗi thằng đầu tiên bị lỗi -> sửa thằng dưới thì nó ko báo thằng đó nếu thằng trên đang lỗi.
    //},
  })

  const clearFormValidator = async () => {
    await until(formValidator.isFinished).toMatch((isFinished) => Boolean(isFinished) == true, {
      timeout: 30000,
      throwOnTimeout: true,
    })
    formValidator.errorInfo.value = null
  }

  const getListPaymentLinkTask = useAsyncTask(async (signal, input: GetListPaymentLinkInput) => {
    const { data } = await paymentLinkApi.getListPaymentLink(input)

    return data
  }).keepLatest()

  const pinPaymentLinkTask = useAsyncTask(async (signal, input: PinPaymentLinkInput) => {
    const { data } = await paymentLinkApi.pinPaymentLink(input)

    return data
  }).keepLatest()

  const exportPaymentLinkTask = useAsyncTask(async (signal, input: ExportPaymentLinkInput) => {
    console.log("Export PaymentLink ", input)

    const res = await paymentLinkApi.exportPaymentLink(input)

    // Save to file
    saveAs(res)
  }).keepLatest()

  const exportPaymentLinkTransTask = useAsyncTask(async (signal, input: ExportPaymentLinkTransInput) => {
    console.log("Export PaymentLink Transaction", input)

    const res = await paymentLinkApi.exportPaymentLinkTrans(input)

    // Save to file
    saveAs(res)
  }).keepLatest()

  const setEnableStatusTask = useAsyncTask(async (signal, input: SetEnableStatusInput) => {
    const { data } = await paymentLinkApi.setEnableStatus(input)
  }).keepLatest()

  const getByIdPaymentLinkTask = useAsyncTask(async (signal, input: uuid) => {
    const { data } = await paymentLinkApi.getById(input)

    return data
  }).keepLatest()

  const getListPaymentLinkTransTask = useAsyncTask(async (signal, input: GetListPaymentLinkTransInput) => {
    const { data } = await paymentLinkApi.getListPaymentLinkTrans(input)

    return data
  }).keepLatest()

  const getAllPaymentMethodsTask = useAsyncTask(async () => {
    const { data } = await paymentLinkApi.getAllPaymentMethod()

    return data
  })

  const getAllLocalesTask = useAsyncTask(async () => {
    const { data } = await paymentLinkApi.getAllLocale()

    return data
  })

  const addNewPaymentLinkTask = useAsyncTask(async (signal, input: PaymentLinkRequest) => {
    await formValidator.execute()
    const isValid = formValidator.isFinished.value && formValidator.pass.value
    if (!isValid) return

    const { data } = await paymentLinkApi.addNew(input)

    return data
  })

  const uploadTask = useAsyncTask(async (signal, input: any) => {
    const { data } = await paymentLinkApi.upload(input)

    return data
  })

  const downloadTask = useAsyncTask(async (signal, fileId: string) => {
    console.log("Download FileId", fileId)

    const res = await paymentLinkApi.download(fileId)

    // Save to file
    saveAs(res)
  })

  const getCurrencyConfigTask = useAsyncTask(async (signal, merchantProfileId: string | undefined) => {
    if (!merchantProfileId) {
      console.error("GetCurrencyConfig: The merchant Profile Id must be defined.")
      throw Error("The merchant Profile Id must be defined.")
    }

    const { data } = await paymentLinkApi.getCurrencyConfig(merchantProfileId)

    return data
  })

  const editTask = useAsyncTask(async (signal, input: PaymentLinkRequest, id: string) => {
    await formValidator.execute()
    const isValid = formValidator.isFinished.value && formValidator.pass.value
    if (!isValid) return

    const { data } = await paymentLinkApi.edit(input, id)

    return data
  })

  const getInstallmentsTask = useAsyncTask(async (signal, input: { merchantId: string | undefined }) => {
    if (!input.merchantId) {
      console.error("GetInstallments: The merchant Id must be defined.")
      throw Error("The merchant Id must be defined.")
    }

    const { data } = await paymentLinkApi.getInstallmentsSetting(input.merchantId)

    return data
  })

  const checkExitUriTask = useAsyncTask(async (signal, routerUri: CheckIsExit) => {
    const { data } = await paymentLinkApi.checkExits(routerUri)

    return data
  }).keepLatest()

  function getList(input: GetListPaymentLinkInput): TaskInstance<GetListPaymentLinkOutput> {
    return getListPaymentLinkTask.perform(input)
  }

  function getListTrans(input: GetListPaymentLinkTransInput): TaskInstance<GetListPaymentLinkTransOutput> {
    return getListPaymentLinkTransTask.perform(input)
  }

  function pin(input: PinPaymentLinkInput): TaskInstance<void> {
    return pinPaymentLinkTask.perform(input)
  }

  function exportTransData(input: ExportPaymentLinkTransInput): TaskInstance<void> {
    return exportPaymentLinkTransTask.perform(input)
  }

  function setEnableStatus(input: SetEnableStatusInput): TaskInstance<void> {
    return setEnableStatusTask.perform(input)
  }

  function formatPaymentLinkStatus(item: GetListPaymentLinkItem | GetByIdPaymentLinkOutput) {
    const enabled = item.enabled
    const status = item.status

    if (status === undefined) {
      return {
        label: "-",
        itemClass: "paymentLink-Status-NULL",
      }
    } else if (status === EnumPaymentLinkStatus.Successful) {
      return {
        label: t(`common.data-table.payment-link-status.Successful`),
        tooltip: t(`common.data-table.payment-link-status-tooltip.Successful`),
        itemClass: "paymentLink-Status-Active",
      }
    } else if (status == EnumPaymentLinkStatus.LimitReached) {
      return {
        label: t(`common.data-table.payment-link-status.LimitReached`),
        tooltip: t(`common.data-table.payment-link-status-tooltip.LimitReached`),
        itemClass: "paymentLink-Status-LimitReached",
      }
    } else if (status == EnumPaymentLinkStatus.Expired) {
      return {
        label: t(`common.data-table.payment-link-status.Expired`),
        tooltip: t(`common.data-table.payment-link-status-tooltip.Expired`),
        itemClass: "paymentLink-Status-Expired",
      }
    } else {
      if (enabled === undefined) {
        return {
          label: "-",
          itemClass: "paymentLink-Status-NULL",
        }
      } else if (enabled === false) {
        return {
          label: t(`common.data-table.payment-link-status.Inactive`),
          tooltip: t(`common.data-table.payment-link-status-tooltip.Inactive`),
          itemClass: "paymentLink-Status-Inactive",
        }
      } else {
        return {
          label: t(`common.data-table.payment-link-status.Active`),
          tooltip: t(`common.data-table.payment-link-status-tooltip.Active`),
          itemClass: "paymentLink-Status-Active",
        }
      }
    }

    // #region logic cũ

    // if (enabled == undefined || status == undefined)
    //   return {
    //     label: "-",
    //     itemClass: "paymentLink-Status-NULL",
    //   }
    // else if (enabled == false)
    //   return {
    //     label: t(`common.data-table.payment-link-status.Inactive`),
    //     tooltip: t(`common.data-table.payment-link-status-tooltip.Inactive`),
    //     itemClass: "paymentLink-Status-Inactive",
    //   }
    // else if (status == EnumPaymentLinkStatus.Expired)
    //   return {
    //     label: t(`common.data-table.payment-link-status.Expired`),
    //     tooltip: t(`common.data-table.payment-link-status-tooltip.Expired`),
    //     itemClass: "paymentLink-Status-Expired",
    //   }
    // else if (status == EnumPaymentLinkStatus.LimitReached)
    //   return {
    //     label: t(`common.data-table.payment-link-status.LimitReached`),
    //     tooltip: t(`common.data-table.payment-link-status-tooltip.LimitReached`),
    //     itemClass: "paymentLink-Status-LimitReached",
    //   }
    // else if (status == EnumPaymentLinkStatus.Active)
    //   return {
    //     label: t(`common.data-table.payment-link-status.Active`),
    //     tooltip: t(`common.data-table.payment-link-status-tooltip.Active`),
    //     itemClass: "paymentLink-Status-Active",
    //   }
    // else if (status == EnumPaymentLinkStatus.Successful)
    //   return {
    //     label: t(`common.data-table.payment-link-status.Successful`),
    //     tooltip: t(`common.data-table.payment-link-status-tooltip.Successful`),
    //     itemClass: "paymentLink-Status-Active",
    //   }
    // else
    //   return {
    //     label: `${status}-Unknown`,
    //     itemClass: `paymentLink-Status-Unknown-${status}`,
    //   }

    // #endregion
  }

  function formatPaymentLinkTransType(transType: number) {
    if (transType == EnumTransactionType.Authorize)
      return {
        label: t(`common.payment-link-transaction-type.Authorize`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Type-Authorize",
      }
    else if (transType == EnumTransactionType.Capture)
      return {
        label: t(`common.payment-link-transaction-type.Capture`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Type-Capture",
      }
    else if (transType == EnumTransactionType.Purchase)
      return {
        label: t(`common.payment-link-transaction-type.Purchase`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Type-Purchase",
      }
    else if (transType == EnumTransactionType.Refund)
      return {
        label: t(`common.payment-link-transaction-type.Refund`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Type-Refund",
      }
    else if (transType == EnumTransactionType.RefundCapture)
      return {
        label: t(`common.payment-link-transaction-type.RefundCapture`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Type-RefundCapture",
      }
    else if (transType == EnumTransactionType.RefundDispute)
      return {
        label: t(`common.payment-link-transaction-type.RefundDispute`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Type-RefundDispute",
      }
    else if (transType == EnumTransactionType.VoidAuthorize)
      return {
        label: t(`common.payment-link-transaction-type.VoidAuthorize`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Type-VoidAuthorize",
      }
    else if (transType == EnumTransactionType.VoidCapture)
      return {
        label: t(`common.payment-link-transaction-type.VoidAuthorize`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Type-VoidAuthorize",
      }
    else if (transType == EnumTransactionType.VoidPurchase)
      return {
        label: t(`common.payment-link-transaction-type.VoidPurchase`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Type-VoidPurchase",
      }
    else if (transType == EnumTransactionType.VoidRefund)
      return {
        label: t(`common.payment-link-transaction-type.VoidRefund`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Type-VoidRefund",
      }
    else if (transType == EnumTransactionType.VoidRefundCapture)
      return {
        label: t(`common.payment-link-transaction-type.VoidRefundCapture`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Type-VoidRefundCapture",
      }
    else if (transType == EnumTransactionType.RequestRefund)
      return {
        label: t(`common.payment-link-transaction-type.RequestRefund`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Type-RequestRefund",
      }
    else if (transType == EnumTransactionType.PayLater)
      return {
        label: t(`common.payment-link-transaction-type.PayLater`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Type-PayLater",
      }
    else if (transType == EnumTransactionType.Void)
      return {
        label: t(`common.payment-link-transaction-type.Void`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Type-Void",
      }
    else if (!transType)
      return {
        label: "-",
        itemClass: `paymentLink-Trans-Type-Null`,
      }
    else
      return {
        label: `${transType}-Unknown`,
        itemClass: `paymentLink-Trans-Type-Unknown-${transType}`,
      }
  }

  function formatPaymentLinkTransStatus(status: EnumTransactionStatus) {
    //const status = item.transaction ? item.transaction.status : item.status

    if (status == EnumTransactionStatus.AwaitTransactionResult)
      return {
        label: t(`common.payment-link-transaction-status.AwaitTransactionResult`),
        //tooltip: t(`common.payment-link-transaction-status.AwaitTransactionResult`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Status-AwaitTransactionResult",
      }
    else if (status == EnumTransactionStatus.Successful)
      return {
        label: t(`common.payment-link-transaction-status.Successful`),
        //tooltip: t(`common.payment-link-transaction-status.Successful`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Status-Successful",
      }
    else if (status == EnumTransactionStatus.Failed)
      return {
        label: t(`common.payment-link-transaction-status.Failed`),
        //tooltip: t(`common.payment-link-transaction-status.Failed`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Status-Failed",
      }
    else if (status == EnumTransactionStatus.Processing)
      return {
        label: t(`common.payment-link-transaction-status.Processing`),
        //tooltip: t(`common.payment-link-transaction-status.Processing`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Status-Processing",
      }
    else if (status == EnumTransactionStatus.Incomplete)
      return {
        label: t(`common.payment-link-transaction-status.Incomplete`),
        //tooltip: t(`common.payment-link-transaction-status.Incomplete`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Status-Incomplete",
      }
    else if (status == EnumTransactionStatus.AwaitMerchantApproval)
      return {
        label: t(`common.payment-link-transaction-status.AwaitMerchantApproval`),
        //tooltip: t(`common.payment-link-transaction-status.AwaitMerchantApproval`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Status-AwaitMerchantApproval",
      }
    else if (status == EnumTransactionStatus.MerchantRejected)
      return {
        label: t(`common.payment-link-transaction-status.MerchantRejected`),
        //tooltip: t(`common.payment-link-transaction-status.MerchantRejected`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Status-MerchantRejected",
      }
    else if (status == EnumTransactionStatus.AwaitOnepayApprovval)
      return {
        label: t(`common.payment-link-transaction-status.AwaitOnepayApprovval`),
        //tooltip: t(`common.payment-link-transaction-status.AwaitOnepayApprovval`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Status-AwaitOnepayApprovval",
      }
    else if (status == EnumTransactionStatus.OnepayRejected)
      return {
        label: t(`common.payment-link-transaction-status.OnepayRejected`),
        //tooltip: t(`common.payment-link-transaction-status.OnepayRejected`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Status-OnepayRejected",
      }
    else if (status == EnumTransactionStatus.MerchantApproved)
      return {
        label: t(`common.payment-link-transaction-status.MerchantApproved`),
        //tooltip: t(`common.payment-link-transaction-status.MerchantApproved`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Status-MerchantApproved",
      }
    else if (status == EnumTransactionStatus.OnepayApproved)
      return {
        label: t(`common.payment-link-transaction-status.OnepayApproved`),
        //tooltip: t(`common.payment-link-transaction-status.OnepayApproved`),
        tooltip: "",
        itemClass: "paymentLink-Trans-Status-OnepayApproved",
      }
    else if (!status)
      return {
        label: "-",
        itemClass: `paymentLink-Trans-Status-Null`,
      }
    else
      return {
        label: `${status}-Unknown`,
        itemClass: `paymentLink-Trans-Status-Unknown-${status}`,
      }
  }

  function correctPaymentLinkRouterUri(routerUri: string): string {
    routerUri = routerUri.replace("\\", "/")
    if (!routerUri.startsWith("/")) routerUri = `/${routerUri}`

    return routerUri
  }

  function getPaymentLinkFullRouterUri(routerUri: string, host?: string): string {
    host = host ?? APPCONFIG.APP_PL_HOST
    if (host.endsWith("/")) host = host.substring(0, host.length - 2)

    routerUri = correctPaymentLinkRouterUri(routerUri)

    return `${host}${routerUri}`
  }

  function getAllPaymentMethods(): TaskInstance<PaymentMethods[]> {
    return getAllPaymentMethodsTask.perform()
  }

  function stringToSlug(str: string) {
    // remove accents
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a")
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e")
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i")
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o")
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u")
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y")
    str = str.replace(/đ/g, "d")

    str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A")
    str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E")
    str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I")
    str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O")
    str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U")
    str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y")
    str = str.replace(/Đ/g, "D")
    str = str.replace(/[^\w\s]/gi, "")

    return str
  }

  function formatLinkType(type: number) {
    switch (type) {
      case EnumPaymentLinkType.OneTime:
        return t("page-LinkManagement-detail.type.one-time")
      case EnumPaymentLinkType.MultiUse:
        return t("page-LinkManagement-detail.type.multi")
      default:
        return `${type}-Unknow`
    }
  }


  function setAddEditFormData(value: GetByIdPaymentLinkOutput) {
    form.linkId = value.id
    form.name = value.name
    form.routerUri = value.routerUri
    form.routerUriOld = value.routerUri
    form.isStopAutoGenUri = false
  }

  async function resetAddEditFormData() {
    //console.log("resetAddEditFormData")
    getOrSetDefaultForm(form)
    await clearFormValidator()
  }

  return {
    getListPaymentLinkTask,
    getList,
    pinPaymentLinkTask,
    pin,
    exportPaymentLinkTask,
    exportPaymentLinkTransTask,
    setEnableStatusTask,
    setEnableStatus,
    getByIdPaymentLinkTask,
    getListPaymentLinkTransTask,
    getListTrans,
    formatPaymentLinkStatus,
    formatPaymentLinkTransType,
    formatPaymentLinkTransStatus,
    correctPaymentLinkRouterUri,
    getPaymentLinkFullRouterUri,
    getAllPaymentMethodsTask,
    getAllPaymentMethods,
    getAllLocalesTask,
    addNewPaymentLinkTask,
    uploadTask,
    downloadTask,
    getCurrencyConfigTask,
    editTask,
    getInstallmentsTask,
    checkExitUriTask,
    form: form,
    formValidator,
    formValidatorIsFinished: formValidator.isFinished,
    clearFormValidator,
    stringToSlug,
    formatLinkType,
    setAddEditFormData,
    resetAddEditFormData,
  }
})

export type {
  ExportPaymentLinkInput,
  GetByIdPaymentLinkOutput,
  GetListPaymentLinkInput,
  GetListPaymentLinkOutput,
  GetListPaymentLinkTransInput,
  GetListPaymentLinkTransOutput,
  GetListPaymentLinkTransItem,
  PinPaymentLinkInput,
}
