import { roleAndPermissionApi } from "@/apis"
import { GetAllPermissionInput } from "@/apis/types/permissionTypes"
import { AddRoleInput, CheckRoleExistInput, CheckRoleExistOutput, DeleteRoleInput, EditRoleInput, GetDetailRoleInput, GetListRoleInput } from "@/apis/types/roleTypes"
import { uniqueId } from "lodash-es"
import { defineStore } from "pinia"
import { useAsyncTask } from "vue-concurrency"

export const useRoleAndPermissionService = defineStore(uniqueId("roleService-"), () => {
  const getAllPermissionTask = useAsyncTask(async (signal, input: GetAllPermissionInput) => {
    const { data } = await roleAndPermissionApi.getAllPermission(input)

    return data
  }).keepLatest()

  const getListRoleTask = useAsyncTask(async (signal, input: GetListRoleInput) => {
    const { data } = await roleAndPermissionApi.getListRole(input)

    return data
  }).keepLatest()

  const getDetailRoleTask = useAsyncTask(async (signal, input: GetDetailRoleInput) => {
    const { data } = await roleAndPermissionApi.getDetailRole(input)

    return data
  }).keepLatest()

  const addRoleTask = useAsyncTask(async (signal, input: AddRoleInput) => {
    const { data } = await roleAndPermissionApi.addRole(input)

    return data
  })

  const editRoleTask = useAsyncTask(async (signal, input: EditRoleInput) => {
    const { data } = await roleAndPermissionApi.editRole(input)

    return data
  })

  const deleteRoleTask = useAsyncTask(async (signal, input: DeleteRoleInput) => {
    const { data } = await roleAndPermissionApi.deleteRole(input)

    return data
  })

  const checkRoleExistTask = useAsyncTask(async (signal, input: CheckRoleExistInput) => {
    
    // TODO: fake data
    const fakeData: CheckRoleExistOutput = {
      exist: input?.name == "abc" ? true : false,
    }
    return fakeData

    const { data } = await roleAndPermissionApi.checkRoleExist(input)

    return data
  })

  return {
    // permission actions
    getAllPermissionTask,
    // role actions
    getListRoleTask,
    getDetailRoleTask,
    addRoleTask,
    editRoleTask,
    deleteRoleTask,
    checkRoleExistTask,
  }
})
