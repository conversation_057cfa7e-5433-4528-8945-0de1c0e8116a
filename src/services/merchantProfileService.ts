import { merchantProfileApi } from "@/apis"
import { GetListMerchantProfileInput } from "@/apis/types/merchantTypes"
import { uniqueId } from "lodash-es"
import { defineStore } from "pinia"
import { useAsyncTask } from "vue-concurrency"

export const useMerchantProfileService = defineStore(uniqueId("merchantProfileService-"), () => {
  const getListMerchantProfileTask = useAsyncTask(async (signal, input: GetListMerchantProfileInput) => {
    const { data } = await merchantProfileApi.getListMerchantProfile(input)

    return data
  }).keepLatest()

  return {
    getListMerchantProfileTask,
  }
})
