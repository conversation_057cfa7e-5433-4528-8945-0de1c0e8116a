import { demoApi } from "@/apis"
import { GetStudentInput, Student } from "@/types/demo"
import { AxiosError } from "axios"
import { uniqueId } from "lodash-es"
import { defineStore } from "pinia"
import { reactive, ref } from "vue"
import { TaskInstance, useAsyncTask } from "vue-concurrency"

// Định nghĩa typeof State cho Store
export interface IDemoState {
}

interface RequestState<TData> {
  data: TData
  loading: boolean
  error: Error | null
}

// Định nghĩa Store
export const useDemoServiceNo = defineStore(uniqueId("demoStore-"), () => {
  const students = ref<Student[]>([])
  const getStudentReqState = reactive<RequestState<Student[]>>({
    data: [],
    loading: true,
    error: null,
  })
  const isLoading = ref(false)
  const error = ref<AxiosError>()

  async function getStudents(): Promise<RequestState<Student[]>> {
    
    getStudentReqState.loading = true
    isLoading.value = true

    try {
      const { data, status } = await demoApi.getStudents()

      getStudentReqState.data = data
      students.value = data
    } catch (apiError) {
      error.value = apiError as AxiosError<string>
    } finally {
      getStudentReqState.loading = false
      isLoading.value = false
    }

    

    return getStudentReqState
  }

  return { students, isLoading, error, getStudentReqState }
})

export type DemoService = ReturnType<typeof useDemoService>

export const useDemoService = defineStore("demoStore", () => {
  const students = ref<Student[]>([])

  const getStudentsTask = useAsyncTask(async (signal, input: GetStudentInput) => {
    const { data, status } = await demoApi.getStudents(input)

    students.value = data
    return data
  })

  // Sử dụng yield có vẻ khó dùng và ko có được type chính xác
  // const getStudentsTaskYield = useTask(function*(signal, input: GetStudentInput) {
  //   const response = yield apis.demo.getStudents(input)

  //   //return data /??
  // })

  function getStudents(input: GetStudentInput): TaskInstance<Student[]> {
    return getStudentsTask.perform(input)
  }

  function setStudent(input: Student): void {
    students.value.push(input)
  }

  return { students, getStudents, setStudent, getStudentsTask }
})

// Hướng dẫn cách sử dụng
export async function setup() {
  // get store
  const demoStore = useDemoService()

  // const { students } = storeToRefs(demoStore) // biến students thành ref và giá trị thay đổi theo action được
  // const { students } = demoStore // trường hợp này thì giá trị sẽ cố định tại thời điểm get

  // demoStore.setStudent
  // demoStoreRef.getStudentsTask
  //demoStoreRef.getStudentsTask

  // get data bằng cách gọi await perform trực tiếp từ Task
  const userGetByAsync = await demoStore.getStudentsTask.perform({} as GetStudentInput) // data is Student[]

  // get data bằng cách gọi perform gián tiếp qua promise then từ Task
  demoStore.getStudentsTask.perform({} as GetStudentInput).then((userGetByPromiseThen) => {
    console.log(userGetByPromiseThen) // data is Student[]
  })

  // get data trực tiếp từ function và check running state qua Task
  const userGetByAsync2 = demoStore.getStudents({} as GetStudentInput) // data is Student[]
  userGetByAsync2.value

  // check running state từ Task
  const isRunning = demoStore.getStudentsTask.isRunning
}

