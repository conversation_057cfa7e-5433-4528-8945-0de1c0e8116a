import { userApi } from "@/apis"
import type {
  ActiveUserInput,
  CurrencyUpdateRequest,
  EditUserInput,
  GetListUserInput,
  GetListUserItem,
  GetUserOutput,
  UserSimpleInfo,
} from "@/apis/userApis"
import { defineStore } from "pinia"
import { ref } from "vue"
import { type TaskInstance, useAsyncTask } from "vue-concurrency"
import { uniqueId, remove } from "lodash-es"
import { EnumUserPermissionType } from "@/app.const"

export const useUserService = defineStore(uniqueId("userStore-"), () => {
  const users = ref<GetUserOutput[]>([])

  const getUserByEmailTask = useAsyncTask(async (signal, email: string) => {
    const { data, status } = await userApi.getUserByEmail(email)
    const user = users.value.find((item) => item.user.id == data.user.id)
    if (user) {
      users.value = remove(users.value, (item) => item.user.id == data.user.id)
      users.value.push(data)
    }

    return data
  })

  const getUserBySIdTask = useAsyncTask(async (signal, sid: string) => {
    const { data, status } = await userApi.getUserBySId(sid)

    // Fix sai data
    _fixData(data.user)

    const user = users.value.find((item) => item.user.id == data.user.id)
    if (user) {
      users.value = remove(users.value, (item) => item.user.id == data.user.id)
      users.value.push(data)
    }

    return data
  })

  function _fixData(data: UserSimpleInfo | GetListUserItem[]) {
    if (Array.isArray(data)) {
      // Fix sai data
      data.forEach((u) => {
        u.fullName = u.fullName || (u as any).fullname
        delete (data as any).fullname
      })
    } else {
      data.fullName = data.fullName || (data as any).fullname
      delete (data as any).fullname
    }
  }

  const getAllCurrencyTask = useAsyncTask(async (signal) => {
    const { data } = await userApi.getAllCurrency()

    return data
  })

  const updateAllCurrency = useAsyncTask(async (signal, currencyUpdateRequest: CurrencyUpdateRequest[]) => {
    const { data } = await userApi.updateCurrency(currencyUpdateRequest)

    return data
  }).keepLatest()

  const getListUserTask = useAsyncTask(async (signal, input: GetListUserInput) => {
    const { data } = await userApi.getListUser(input)

    // Fix sai data
    _fixData(data.data)

    return data
  }).keepLatest()

  const editUserTask = useAsyncTask(async (signal, input: EditUserInput) => {
    const { data } = await userApi.editUser(input)

    return data
  }).keepLatest()

  const activeUserTask = useAsyncTask(async (signal, input: ActiveUserInput) => {
    const { data } = await userApi.activeUser(input)

    return data
  }).keepLatest()

  function getUserByEmail(email: string): TaskInstance<GetUserOutput> {
    return getUserByEmailTask.perform(email)
  }

  return {
    users,
    getUserBySIdTask,
    getUserByEmailTask,
    getUserByEmail,
    getAllCurrencyTask,
    updateAllCurrency,
    getListUserTask,
    editUserTask,
    activeUserTask,
  }
})
