import { defineStore } from "pinia"
import { useConfirm } from "primevue/useconfirm"
import { ConfirmationOptions } from "primevue/confirmationoptions"
import { useGlobalI18n } from "@/plugins/i18n"
import { uniqueId } from "lodash-es"

export interface ConfirmDialogServiceOptions extends ConfirmationOptions {
  acceptSeverity?: string
  rejectSeverity?: string
}

export const useConfirmDialogService = defineStore(uniqueId("confirmDialogService-"), () => {
  const confirmDialog = useConfirm()
  const { t } = useGlobalI18n()

  function confirm(options: ConfirmDialogServiceOptions): Promise<boolean> {
    const resultPromise = new Promise<boolean>((resolve) => {
      const message = {
        ...{
          blockScroll: false,
          acceptLabel: t("common.confirm-dialog.accept-label"),
          rejectLabel: t("common.confirm-dialog.reject-label"),
          header: t("common.confirm-dialog.header"),
          message: t("common.confirm-dialog.message"),
        },
        ...options,
        ...{
          accept() {
            console.log("confirmDialogService.accept")
            options.accept?.apply(this)
            resolve(true)
          },
          reject() {
            console.log("confirmDialogService.reject")
            options.reject?.apply(this)
            resolve(false)
          },
        },
      } as ConfirmDialogServiceOptions

      confirmDialog.require(message)
    })

    return resultPromise
  }

  function close(): void {
    confirmDialog.close()
  }

  return {
    confirm,
    close,
  }
})
