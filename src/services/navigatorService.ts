import { useGlobalRouter } from "@/plugins/router"
import { defineStore } from "pinia"
import { computed, nextTick, reactive, ref, VNode } from "vue"
import {
  RouteLocation,
  RouteLocationMatched,
  RouteLocationNormalized,
  RouteLocationRaw,
  Router,
  RouteRecordNormalized
} from "vue-router"

function _isDefined<T>(value: T | undefined | null): value is T {
  return typeof value != 'undefined' && value != null
}

export enum NavigationDirection {
  back = "back",
  forward = "forward",
  unknown = "",
}
export enum NavigationAction {
  back = "back",
  forward = "forward",
  push = "push",
  replace = "replace",
  go = "go",
  unknown = "",
}

export type RouteStackItemType = {
  id: string
  routerViewName?: string
  path?: string
  component: string
  refreshCount: number
}
const DEFAULT_COMPONENT_NAME = "-unknown-"

export const useNavigatorService = defineStore("navigatorService", () => {
  const innerRouter = ref<Router>(useGlobalRouter())
  // init luôn
  //wrapRouter(router)

  const listComponentStack = reactive<RouteStackItemType[]>([])
  const listComponentInclude = computed(() => listComponentStack.map((c) => c.component).removeDuplicate())
  const listComponentExclude = ref<string[]>([])

  /**
   * Thực hiện Refresh chỉ component chính của route theo path
   */
  async function refresh(route?: RouteLocationNormalized) {
    route ??= innerRouter.value.currentRoute
    const componentName = _getRouteComponentName(route)
    const path = _getPathWithoutParams(route)

    // chỗ này có thể tìm theo cả path nhưng thôi. refresh tất cả compnent trùng tên
    const itemComponents = listComponentStack.filter((c) => c.path == path && c.component == componentName)
    console.log("refresh", path, componentName, itemComponents)
    for (const itemComponent of itemComponents) {
      // đưa component vào Excluded để ngăn ngừa cache
      listComponentExclude.value.push(componentName)
      await nextTick()
      // thực hiện reload
      itemComponent.refreshCount++
      // xóa compoent khỏi Excluded để tiếp tục cache
      const index = listComponentExclude.value.indexOf(componentName)
      if (index > -1) {
        listComponentExclude.value.splice(index, 1)
      }
    }
  }

  function _getVNodeComponentName(component?: VNode): string {
    if (!component) return DEFAULT_COMPONENT_NAME

    const componentName: string = (component as any)?.type?.__name || DEFAULT_COMPONENT_NAME
    return componentName
  }

  function _getRouteComponentName(
    route: RouteLocation | RouteLocationNormalized | RouteLocationMatched | undefined
  ): string {
    if (!route) return DEFAULT_COMPONENT_NAME

    const routeResolved = innerRouter.value.resolve({ path: route.path }) as RouteLocation

    // Lấy tên/path của route hiện tại
    const currentRouteName = routeResolved.name
    //const currentRoutePath = route.path // path này đã có full params
    const currentRoutePath = _getPathWithoutParams(routeResolved)

    // Lấy component từ cấu hình route (nếu cần)
    //const matchedRoute = route.matched.length == 0 ? undefined : route.matched[route.matched.length - 1] // lấy match cuối cùng
    const matchedRoute = routeResolved.matched.find((r) => r.name === currentRouteName || r.path === currentRoutePath)

    const componentName: string = (matchedRoute?.components as any)?.default?.__name || DEFAULT_COMPONENT_NAME
    return componentName
  }

  function _getPathWithoutParams(route: RouteLocationNormalized | undefined) {
    let path = route?.path
    if (route?.params) {
      for (const [key, values] of Object.entries(route?.params)) {
        if (Array.isArray(values)) for (const value of values) path?.replace(value, key)
        else path = path?.replace(values, `:${key}`)
      }
    }
    return path
  }

  function getOrSetComponentKey(
    component: VNode,
    route?: RouteLocationNormalized,
    options?: {
      routerViewName?: string
    }
  ) {
    const componentName = _getVNodeComponentName(component)
    const path = _getPathWithoutParams(route)
    // build uuid
    const id: uuid = [options?.routerViewName, componentName].filter((o) => !!o).join("|")

    console.log("GetOrSetComponentKey", path, componentName, id)

    let item = listComponentStack.firstOrDefault((c) => c.id === id)
    if (!item) {
      item = {
        id: id,
        routerViewName: options?.routerViewName,
        path: path,
        component: componentName,
        refreshCount: 0,
      }
      listComponentStack.push(item)
    }

    // generate key
    const componentKey = `${item.id}-${item.refreshCount}`

    return componentKey
  }

  function _getRouteCacheConfig(route: RouteRecordNormalized | RouteLocationNormalized) {
    const defaultCache = true
    const metaCache = _isDefined(route.meta.cache) ? route.meta.cache : defaultCache
    return metaCache
  }

  async function _useRouterCacheable(router: Router) {
    console.log("use RouterCacheable")

    let navigationRouteExtendOptions: { cache: boolean | undefined } | null = null
    // verify để xóa cách mỗi khi router chạy
    router.afterEach(async (to, from) => {
      console.log("Navigation afterEach", to.path, to)

      let routeCache = _getRouteCacheConfig(to)
      if (navigationRouteExtendOptions) {
        if (_isDefined(navigationRouteExtendOptions?.cache)) routeCache = navigationRouteExtendOptions?.cache

        // clear Route extend options sau khi đọc xong
        navigationRouteExtendOptions = null
      }

      if (routeCache == false) {
        await refresh(to)
      }
    })
    // override lại router method để bổ sung khả năng check cache
    ;["push", "replace"].forEach((methodName) => {
      const method = router[methodName]
      router[methodName] = (...args: any) => {
        if (args.length == 0) throw new Error(`The '${methodName}' method must have at least one input parameter.`)
        const to = args[0] as RouteLocationRaw
        console.log("Navigation method", methodName, to)
        if (typeof to === "object") {
          navigationRouteExtendOptions = {
            cache: to.cache,
          }
        }

        return method.apply(router, args)
      }
    })
  }

  async function wrapRouter(router: Router) {
    console.log("wrapRouter init at path", router.currentRoute.value.path)

    _useRouterCacheable(router)

    // let navigationBrowserInfo: { direction: NavigationDirection; delta: number } | null = null
    // let navigationRouteJsInfo: { action: NavigationAction } | null = null
    // navigationRouteJsInfo = null

    // router.options.history.listen((_to, _from, info) => {
    //   navigationBrowserInfo = info
    // })
    // router.afterEach(async (to, from) => {
    //   console.log("Navigation afterEach", to.path, to)

    //   // saveRouteStack(to)
    //   // saveComponentStack(to)
    // })
    // router.beforeEach((to, from) => {
    //   console.log("Navigation beforeEach", to.path, to)

    //   // if (navigationBrowserInfo) {
    //   //   if (navigationBrowserInfo.direction === NavigationDirection.back) {
    //   //     //listRouteStack.push(to.path)
    //   //     console.log("Navigation back", to.path)
    //   //   } else if (navigationBrowserInfo.direction === NavigationDirection.forward) {
    //   //     //listRouteStack.pop()
    //   //     console.log("Navigation forward", to.path)
    //   //   }
    //   //   // need to reset it to null
    //   //   navigationBrowserInfo = null
    //   // }
    //   // if (navigationRouteJsInfo) {
    //   //   // TODO: ...
    //   //   // need to reset it to null
    //   //   navigationRouteJsInfo = null
    //   // }
    // })
    // // ;["push", "replace", "go", "back", "forward"].forEach((methodName) => {
    // //   const method = router[methodName]
    // //   router[methodName] = (...args: any) => {
    // //     navigationRouteJsInfo = {
    // //       action: methodName as NavigationAction,
    // //     }
    // //     console.log("Navigation method", navigationRouteJsInfo)
    // //     return method.apply(router, args)
    // //   }
    // // })
  }

  const previousRoute = computed(() => {
    const backUrl = innerRouter.value.options.history.state.back
    return innerRouter.value.resolve({ path: `${backUrl}` })
  })

  const currentRoute = computed(() => {
    return innerRouter.value.currentRoute
  })

  // /**
  //  * Thực hiện back về route trước đó (được chỉ định chính xác) để hỗ trợ send servicebus
  //  * @param backTo
  //  */
  // async function backExact(backTo: RouteLocationRaw) {
  //   //await router.isReady()

  //   const previousRoute = router.resolve({ path: `${router.options.history.state.back}` })
  //   const backToRoute = router.resolve(backTo)

  //   // check component đã được tạo từ trước và lưu trong KeepAlive hay chưa.
  //   // Đản bảo component đã được tạo xong thì mới quay lại.
  //   // TODO: Nếu có thể thì thực hiện init component và đưa vào KeepAlive để dùng router.back luôn.
  //   const isComponentCreated = _isDefined(backToRoute.matched[0].instances.default)

  //   if (backToRoute.fullPath == previousRoute?.fullPath && isComponentCreated) {
  //     // Nếu previousRoute chính là backToRoute muốn quay lại thì sử dụng back.
  //     router.back()
  //   } else {
  //     // Nếu khác thì sử dụng navigate to.
  //     await router.push(backTo)
  //   }
  // }

  async function navigateTo(
    to: RouteLocationRaw,
    options?: {
      replace?: boolean
    }
  ) {
    if (options?.replace) {
      return await innerRouter.value.replace(to)
    } else {
      return await innerRouter.value.push(to)
    }
  }

  function navigateToExternal(
    href: string,
    options?: {
      replace?: boolean
    }
  ) {
    console.log("redirectTo", href)

    if (!href) {
      console.warn("href is not found")
      return
    }

    if (options?.replace) {
      window.location.replace(href)
    } else {
      window.location.href = href
    }
  }

  return {
    wrapRouter,
    listComponentStack,
    listComponentInclude,
    listComponentExclude,
    currentRoute,
    previousRoute,
    refresh,
    getOrSetComponentKey,
    navigateTo,
    /**
     * Same as `navigatorService.navigateTo({ ... })`
     */
    redirectTo: navigateTo,
    navigateToExternal,
    /**
     * Same as `navigatorService.redirectToExternal('http://link.com')`
     */
    redirectToExternal: navigateToExternal,
  }
})

declare module "vue-router" {
  interface RouteMeta {
    /**
     * Khi set cache=false thì sẽ ko thực hiện load từ cache ra khi route redirect.
     *
     * @example
     * ```ts
     * export const routes: [
     * {
     *  name: AppRouteNames.ABOUT,
     *  path: '/',
     *  component: () => import('./views/About.vue'),
     *  meta: {
     *   cache: false
     *  }
     * },
     * ```
     */
    cache?: boolean
  }
  interface RouteLocationOptions {
    /**
     * Khi set cache=false thì sẽ ko thực hiện load từ cache ra khi route redirect.
     *
     * @example
     * ```ts
     * await router.push({
     *  name: AppRouteNames.ADMIN_ROLEMANAGEMENT,
     *  cache: false
     * })
     * ```
     */
    cache?: boolean
  }
}