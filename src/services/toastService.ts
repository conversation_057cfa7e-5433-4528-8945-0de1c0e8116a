import { defineStore } from "pinia"
import { useToast } from "primevue/usetoast"
import { ToastMessageOptions } from "primevue/toast"
import { useGlobalI18n } from "@/plugins/i18n"
import { uniqueId } from "lodash-es"
import { useComputedRefreshable } from "@/utils/vueUtil"

// https://medium.com/@haseenakhader.uk/angular-primeng-toast-step-by-step-5ab85c0340fc
// https://github.com/primefaces/primevue/issues/3897

export const useToastService = defineStore(uniqueId("toastService-"), () => {
  const toast = useToast()
  const { t } = useGlobalI18n()

  function success(options: ToastMessageOptions): void {
    const message = {
      ...{
        severity: "success",
        closable: false,
        life: 3000,
        summary: t("common.toast.success-summary"),
      },
      ...options,
    } as ToastMessageOptions

    _showMessage(message)
  }

  function error(options: ToastMessageOptions): void {
    const message = {
      ...{
        severity: "error",
        closable: false,
        life: 3000,
        summary: t("common.toast.error-summary"),
      },
      ...options,
    } as ToastMessageOptions

    _showMessage(message)
  }

  function info(options: ToastMessageOptions): void {
    const message = {
      ...{
        severity: "info",
        closable: false,
        life: 3000,
      },
      ...options,
    } as ToastMessageOptions

    _showMessage(message)
  }

  function warn(options: ToastMessageOptions): void {
    const message = {
      ...{
        severity: "warn",
        closable: false,
        life: 3000,
      },
      ...options,
    } as ToastMessageOptions

    _showMessage(message)
  }

  function copySuccess(options: ToastMessageOptions): void {
    const message = {
      ...{
        severity: "contrast",
        closable: false,
        life: 3000,
        styleClass: "p-toast-message-copytoclipboard",
        group: "groupToastQuickAlert",
        summary: t("common.toast.copy-success-summary"),
      },
      ...options,
    } as ToastMessageOptions

    toast.removeGroup("groupToastQuickAlert")
    _showMessage(message)
  }

  function _showMessage(message: ToastMessageOptions): void {
    const isDuplidate = _checkMessageDuplidate(message)

    if (isDuplidate === true) {
      return
    }

    toast.add(message)
  }

  //#region Message Manager

  type MessageManagerItemType = {
    expiredTime: number
    timeout: NodeJS.Timeout
  }
  const messageManager = new Map<string, MessageManagerItemType>()
  const [messageManagerComputed, refreshMessageManagerComputed, messageManagerRefreshKey] = useComputedRefreshable(
    () => messageManager
  )

  function _checkMessageDuplidate(message: ToastMessageOptions): boolean {
    const isDebug = false
    const currentTime = Date.now()
    const key = `${message.summary}-${message.detail}`
    // Kiểm tra nếu thông báo đã hiển thị trong vòng `checkLife` (1s)
    if (messageManager.has(key)) {
      const item = messageManager.get(key)
      if (item && currentTime < item.expiredTime) {
        return true // Message đã/đang hiển thị.
      }

      // Xóa thông báo khỏi Map để hiển thị lại
      if (item) {
        clearTimeout(item.timeout)
        messageManager.delete(key)
        refreshMessageManagerComputed()
        isDebug && console.log("messageManager", "expired", key, messageManager)
      }
    }

    // Thêm thông báo vào Map với thời điểm hiện tại
    messageManager.set(key, {
      expiredTime: currentTime + (message.life ?? 3000),
      timeout: setTimeout(() => {
        //alert("timeout")
        // Tùy chọn: Xóa thông báo khỏi Map sau `life` để đảm bảo quản lý bộ nhớ
        if (messageManager.has(key)) {
          const item = messageManager.get(key)
          if (item) {
            messageManager.delete(key)
            refreshMessageManagerComputed()
            isDebug && console.log("messageManager", "timeout", key, messageManager)
          }
        }
      }, message.life ?? 3000),
    })
    refreshMessageManagerComputed()
    isDebug && console.log("messageManager", "added", key, messageManager)

    return false
  }

  //#endregion

  return {
    messageManager: messageManagerComputed,
    //refreshKey: messageManagerRefreshKey, // Nếu cần check messageManager ở devtools thì cần enable cái này để nó thực hiện cập nhật view.
    info,
    warn,
    error,
    success,
    copySuccess,
  }
})
