import { Rules } from "async-validator"
import { uniqueId } from "lodash-es"
import { defineStore } from "pinia"
import { computed, reactive } from "vue"
import { useGlobalI18n } from "@/plugins/i18n"
import { useAsyncValidator } from "@vueuse/integrations/useAsyncValidator"
import { useAsyncTask } from "vue-concurrency"
import { DashBoardRequest } from "@/apis/dashBoardApis"
import { dashBoardApi } from "@/apis"
import APPCONFIG from "@/appConfig"
import { formatCurrency } from "@/utils/formatUtil"

export const useDashBoardService = defineStore(uniqueId("dashBoardService-"), () => {
  const { t } = useGlobalI18n()

  const form = reactive<{
    name: string
    amount?: number
    routerUri?: string
    currency: string
  }>({
    name: "",
    currency: "",
  })

  const rules = computed<Rules>(() => {
    return {
      name: [
        {
          required: true,
          message: () => t("components-paymentLink-LinkInfomation.validate-input.required-name"),
        }
        // {
        //   validator: (rule, value, callback, source) => {
        //     if (!source.routerUri) return

        //     const isValid = source.routerUri.length <= 200

        //     if (isValid) return

        //     return new Error(t("components-paymentLink-AddPaymentAmount.warnning-maximun-amount"))
        //   },
        // }
      ],
      amount: [
        {
          asyncValidator: (rule, value) => {
            if (!value) return Promise.resolve()

            if (value > APPCONFIG.MAX_PAYMENTAMOUNT_VND && form.currency === "VND") {
              return Promise.reject(
                `${t("components-paymentLink-AddPaymentAmount.warnning-maximun-amount")} ${formatCurrency(APPCONFIG.MAX_PAYMENTAMOUNT_VND, form.currency)}`
              )
            } else if (value > APPCONFIG.MAX_PAYMENTAMOUNT && form.currency !== "VND") {
              return Promise.reject(
                `${t("components-paymentLink-AddPaymentAmount.warnning-maximun-amount")} ${formatCurrency(APPCONFIG.MAX_PAYMENTAMOUNT, form.currency)}`
              )
            } else {
              return Promise.resolve()
            }
          },
        },
      ],
    }
  })

  const dashBoardTask = useAsyncTask(async (signal, request: DashBoardRequest) => {
    const { data } = await dashBoardApi.dashBoard(request);

    return data;
  }).keepLatest();

  const formValidator = useAsyncValidator(form, rules, {
    immediate: false,
  })

  const clearFormValidator = () => {
    formValidator.errorInfo.value = null
  }

  return {
    form,
    formValidator,
    dashBoardTask,
    clearFormValidator,
  }
})
