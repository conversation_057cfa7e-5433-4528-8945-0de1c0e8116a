<!doctype html>
<html lang="en">
  <%# Đ<PERSON>y là cú pháp của ejs template, tham kh<PERSON>o https://ejs.co/ %>
  <head>
    <% if (baseUrl) { %>
    <base href="<%= baseUrl %>" />
    <% } %>
    <meta name="buildDate" content="<%= buildDate %>" />
    <meta name="version" content="<%= version %>" />
    <!-- <meta name="mode" content="%MODE%" /> -->
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><%= title %></title>
    <style>
      [v-cloak] {
        display: none;
      }
      #app:not([v-cloak]) ~ .v-cloak-overlay {
        display: none !important;
      }
      .v-cloak-overlay {
        position: fixed;
        width: 100%;
        height: 100%;
        z-index: 9999;
        background-color: #fff;
        display: flex;
        gap: 1rem;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
      }
      .v-cloak-loader {
        animation: rotate 1.5s infinite;
        height: 50px;
        width: 50px;
      }
      .v-cloak-loader:before,
      .v-cloak-loader:after {
        border-radius: 50%;
        content: "";
        display: block;
        height: 20px;
        width: 20px;
      }
      .v-cloak-loader:before {
        animation: ball1 1s infinite;
        background-color: #1d559f;
        box-shadow: 30px 0 0 #00b6eb;
        margin-bottom: 10px;
      }
      .v-cloak-loader:after {
        animation: ball2 1s infinite;
        background-color: #00b6eb;
        box-shadow: 30px 0 0 #1d559f;
      }

      @keyframes rotate {
        0% {
          transform: rotate(0deg) scale(0.8);
        }
        50% {
          transform: rotate(360deg) scale(1.2);
        }
        100% {
          transform: rotate(720deg) scale(0.8);
        }
      }

      @keyframes ball1 {
        0% {
          box-shadow: 30px 0 0 #00b6eb;
        }
        50% {
          box-shadow: 0 0 0 #00b6eb;
          margin-bottom: 0;
          transform: translate(15px, 15px);
        }
        100% {
          box-shadow: 30px 0 0 #00b6eb;
          margin-bottom: 10px;
        }
      }

      @keyframes ball2 {
        0% {
          box-shadow: 30px 0 0 #1d559f;
        }
        50% {
          box-shadow: 0 0 0 #1d559f;
          margin-top: -20px;
          transform: translate(15px, 15px);
        }
        100% {
          box-shadow: 30px 0 0 #1d559f;
          margin-top: 0;
        }
      }
    </style>
  </head>
  <body>
    <div id="app" v-cloak></div>
    <div class="v-cloak-overlay">
      <div class="v-cloak-spinner v-cloak-loader"></div>
      <div class="v-cloak-text">Please wait...</div>
    </div>

    <script type="module" src="/src/main.ts"></script>

    <!-- Check Trình duyệt không hỗ trợ module -->
    <script nomodule>
      window.nomodules = true
    </script>
    <script>
      window.nomodules && alert('Your browser is not supported!')
    </script>
  </body>
</html>
