# Editor configuration, see https://editorconfig.org

# Stop the editor from looking for .editorconfig files in the parent directories -> Use .prettierrc
# root = true

# Unix-style newlines with a newline ending every file
[*]
# Non-configurable Prettier behaviors
charset = utf-8
insert_final_newline = true
# Caveat: Prettier won’t trim trailing whitespace inside template strings, but your editor might.
trim_trailing_whitespace = true

# Configurable Prettier behaviors
# (change these if your Prettier config differs)
indent_style = space
indent_size = 2
end_of_line = crlf
max_line_length = 120
